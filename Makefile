# Makefile for Sage Intacct MCP Server

.PHONY: help install install-dev test lint format clean run dev

# Default target
help:
	@echo "Available commands:"
	@echo "  make install      Install core dependencies"
	@echo "  make install-dev  Install all dependencies (including dev)"
	@echo "  make test         Run tests with coverage"
	@echo "  make lint         Run linting tools"
	@echo "  make format       Format code with black and isort"
	@echo "  make clean        Clean up generated files"
	@echo "  make run          Run the MCP server"
	@echo "  make dev          Run in development mode"

# Install core dependencies
install:
	pip install --upgrade pip
	pip install -r requirements.txt

# Install all dependencies including dev
install-dev:
	pip install --upgrade pip
	pip install -r requirements-dev.txt
	pre-commit install

# Run tests with coverage
test:
	pytest tests/ -v --cov=src --cov-report=html --cov-report=term

# Run linting
lint:
	flake8 src tests
	mypy src
	black --check src tests
	isort --check-only src tests
	bandit -r src

# Format code
format:
	black src tests
	isort src tests

# Clean up generated files
clean:
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	rm -rf .coverage htmlcov .pytest_cache .mypy_cache
	rm -rf build dist *.egg-info

# Run the server
run:
	python -m src.main

# Run in development mode
dev:
	fastmcp dev src/main.py --reload
