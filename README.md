# Sage Intacct MCP Server

A Model Context Protocol (MCP) server for Sage Intacct API integration, built with FastMCP 2.0. This server enables Large Language Models (LLMs) to interact seamlessly with Sage Intacct's comprehensive API.

## 🚀 Features

- **OAuth 2.0 Authentication**: Secure authentication with automatic token refresh
- **Modular Architecture**: Separate servers for AP, AR, and GL modules
- **Smart API Surface Management**: Handles Intacct's extensive API efficiently
- **Cross-Module Operations**: Unified tools for operations across modules
- **Batch Processing**: Efficient handling of bulk operations
- **Comprehensive Error Handling**: User-friendly error messages and recovery

## 📊 Current Progress

### Phase 1: Foundation
- ✅ **Task 1**: Project Setup and Structure
- 🔄 **Task 2**: OAuth 2.0 Authentication Implementation
  - ✅ Base authentication module
  - ✅ Authorization Code Flow with PKCE
  - ✅ Client Credentials Flow
  - 🔲 Token Management
  - 🔲 Authenticated HTTP Client
- 🔲 **Task 3**: Base Server Architecture
- 🔲 **Task 4**: Initial Testing Infrastructure

## 📋 Prerequisites

- Python 3.9 or higher
- Sage Intacct account with API access
- OAuth 2.0 credentials (Client ID and Secret)

## 🛠️ Installation

### Prerequisites

Ensure you have Python 3.9 or higher installed:
```bash
python --version
```

### Basic Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/sage-intacct-mcp-server.git
   cd sage-intacct-mcp-server
   ```

2. Create and activate a virtual environment:
   ```bash
   # Create virtual environment
   python -m venv venv
   
   # Activate on Windows:
   venv\Scripts\activate
   
   # Activate on Unix/macOS:
   source venv/bin/activate
   ```

3. Install core dependencies:
   ```bash
   pip install -r requirements.txt
   ```

### Development Installation

For development with testing and linting tools:
```bash
# Install development dependencies
pip install -r requirements-dev.txt

# Install pre-commit hooks
pre-commit install

# Or use make (if available)
make install-dev
```

### Configuration Setup

1. Copy environment template:
   ```bash
   cp .env.example .env
   ```

2. Set up configuration files:
   ```bash
   cp config/auth_config.yaml.template config/auth_config.yaml
   cp config/server_config.yaml.template config/server_config.yaml
   ```

3. Edit `.env` and configuration files with your Intacct credentials.

## 🔧 Configuration

### Authentication Configuration

Create a `config/auth_config.yaml` file with your Intacct OAuth credentials:

```yaml
oauth:
  client_id: "your-client-id"
  client_secret: "your-client-secret"
  redirect_uri: "http://localhost:8080/callback"
  authorization_url: "https://api.intacct.com/ia/oauth2/authorize"
  token_url: "https://api.intacct.com/ia/oauth2/token"
  scope: "full"
```

### Server Configuration

Configure modules and features in `config/server_config.yaml`:

```yaml
server:
  name: "Sage Intacct MCP Server"
  version: "1.0.0"
  
modules:
  accounts_payable:
    enabled: true
  accounts_receivable:
    enabled: true
  general_ledger:
    enabled: true
```

## 🚀 Usage

### Running the Server

```bash
python -m src.main
```

Or use the FastMCP CLI:

```bash
fastmcp run src/main.py --transport streamable-http --port 8000
```

### Development Mode

For development with auto-reload:

```bash
fastmcp dev src/main.py
```

## 🛠️ Development Commands

The project includes a Makefile for common development tasks:

```bash
# Install dependencies
make install          # Core dependencies only
make install-dev      # All dependencies including dev tools

# Code quality
make lint            # Run all linters (flake8, mypy, black, isort, bandit)
make format          # Auto-format code with black and isort

# Testing
make test            # Run tests with coverage report

# Running
make run             # Run the server
make dev             # Run in development mode with auto-reload

# Cleanup
make clean           # Remove generated files and caches
```

## 📚 Documentation

- [Configuration Guide](CONFIGURATION.md) - Detailed configuration options
- [Product Requirements Document](prd.md) - Architecture and implementation plan
- [Development Tasks](DEVELOPMENT_TASKS.md) - Detailed task breakdown
- [Tasks Summary](TASKS_SUMMARY.md) - Quick reference guide

## 🧪 Testing

Run the test suite:

```bash
pytest tests/
```

Run with coverage:

```bash
pytest tests/ --cov=src --cov-report=html
```

## 🏗️ Project Structure

```
sage-intacct-mcp-server/
├── src/
│   ├── auth/              # OAuth 2.0 authentication
│   ├── servers/           # MCP server modules
│   ├── utils/             # Utility functions
│   └── main.py           # Main entry point
├── config/               # Configuration files
├── tests/                # Test suite
├── openapi/              # OpenAPI specifications
└── requirements.txt      # Python dependencies
```

## 🤝 Contributing

Please read our contributing guidelines before submitting pull requests.

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Built with [FastMCP](https://github.com/jlowin/fastmcp)
- Sage Intacct API documentation
