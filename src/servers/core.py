"""
Base server architecture for Sage Intacct MCP servers.

This module provides the core server implementation that all specific
Intacct servers inherit from.
"""

import logging
from typing import Dict, Any, Optional, List, Callable
from contextlib import asynccontextmanager
import os

from fastmcp import FastMCP
from fastmcp.exceptions import Tool<PERSON>rror, ResourceError

from ..auth.interfaces import AuthConfig
from ..auth.token_manager import TokenManager
from ..client.auth_manager import IntacctAuthManager
from ..utils.openapi_loader import OpenAPISpec, OpenAPILoader
from ..utils.route_mapper import RouteMapper, EndpointCategory

logger = logging.getLogger(__name__)


class IntacctServerError(Exception):
    """Base exception for Intacct server errors."""
    pass


class BaseIntacctServer:
    """
    Base class for all Sage Intacct MCP servers.
    
    This class provides common functionality for authentication,
    error handling, and server configuration.
    """
    
    def __init__(
        self,
        name: str,
        version: str = "1.0.0",
        auth_config: Optional[AuthConfig] = None,
        token_manager: Optional[TokenManager] = None,
        **kwargs
    ):
        """
        Initialize the base server.
        
        Args:
            name: Server name
            version: Server version
            auth_config: Authentication configuration
            token_manager: Optional token manager instance
            **kwargs: Additional FastMCP configuration
        """
        self.name = name
        self.version = version
        self.auth_config = auth_config
        self.token_manager = token_manager or TokenManager()
        self.auth_manager: Optional[IntacctAuthManager] = None
        
        # Initialize FastMCP server
        self.server = FastMCP(
            name=name
        )
        
        # Route mapper for organizing tools
        self.route_mapper = RouteMapper()
        
        # Track loaded specs
        self.loaded_specs: List[OpenAPISpec] = []

    
    async def initialize_auth(self, config: Optional[AuthConfig] = None) -> None:
        """
        Initialize authentication manager.
        
        Args:
            config: Optional auth config override
        """
        config = config or self.auth_config
        if not config:
            raise IntacctServerError("No authentication configuration provided")
        
        self.auth_manager = IntacctAuthManager(
            auth_config=config,
            token_manager=self.token_manager
        )
        
        # Ensure we have a valid token
        try:
            await self.auth_manager.get_authenticated_client()
            logger.info(f"Authentication initialized for {self.name}")
        except Exception as e:
            raise IntacctServerError(f"Authentication failed: {str(e)}")
    
    def load_openapi_spec(
        self,
        spec_path: str,
        base_url: Optional[str] = None
    ) -> OpenAPISpec:
        """
        Load an OpenAPI specification.
        
        Args:
            spec_path: Path to the OpenAPI spec file
            base_url: Optional base URL override
            
        Returns:
            Loaded OpenAPI specification
        """
        loader = OpenAPILoader()
        spec = loader.load_spec(spec_path)
        
        # Override base URL if provided
        if base_url and spec.servers:
            spec.servers[0]["url"] = base_url
        
        self.loaded_specs.append(spec)
        logger.info(f"Loaded OpenAPI spec: {spec.info.get('title')}")
        
        return spec
    
    def create_tools_from_spec(
        self,
        spec: OpenAPISpec,
        include_categories: Optional[List[EndpointCategory]] = None,
        exclude_categories: Optional[List[EndpointCategory]] = None
    ) -> List:
        """
        Create MCP tools from an OpenAPI specification.
        
        Args:
            spec: OpenAPI specification
            include_categories: Only include these categories
            exclude_categories: Exclude these categories
            
        Returns:
            List of created tools
        """
        tools = []
        operations = spec.get_operations()
        
        # Categorize operations
        categorized = self.route_mapper.categorize_operations(operations)
        
        for category, ops in categorized.items():
            # Apply category filters
            if include_categories and category not in include_categories:
                continue
            if exclude_categories and category in exclude_categories:
                continue
            
            # Create tools for operations
            for operation in ops:
                tool = self._create_tool_from_operation(operation)
                if tool:
                    tools.append(tool)
        
        logger.info(f"Created {len(tools)} tools from {spec.info.get('title')}")
        return tools
    
    def _create_tool_from_operation(self, operation: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Create a single tool from an OpenAPI operation."""
        path = operation.get("_path", "")
        method = operation.get("_method", "GET")
        
        # Generate tool name
        tool_name, _ = self.route_mapper.map_endpoint(path, method)
        
        # Create tool description
        description = operation.get("summary", "")
        if operation.get("description"):
            description += f"\n\n{operation['description']}"
        
        # For now, return None as we'll implement this with FastMCP's
        # actual tool creation in the specific server implementations
        return None
    
    @asynccontextmanager
    async def lifespan(self):
        """Server lifespan manager."""
        logger.info(f"Starting {self.name} v{self.version}")
        
        # Initialize authentication if config is available
        if self.auth_config:
            try:
                await self.initialize_auth()
            except Exception as e:
                logger.error(f"Failed to initialize auth: {e}")
                # Continue without auth for development
        
        yield
        
        # Cleanup
        logger.info(f"Shutting down {self.name}")
    
    def add_health_check(self) -> None:
        """Add a health check endpoint."""
        
        @self.server.tool()
        async def health_check() -> Dict[str, Any]:
            """Check server health status."""
            health_status = {
                "status": "healthy",
                "server": self.name,
                "version": self.version,
                "authenticated": self.auth_manager is not None,
            }
            
            # Check auth status
            if self.auth_manager:
                try:
                    client = await self.auth_manager.get_authenticated_client()
                    health_status["auth_status"] = "active"
                except Exception as e:
                    health_status["auth_status"] = f"error: {str(e)}"
                    health_status["status"] = "degraded"
            
            return health_status
    
    def run(self, **kwargs) -> None:
        """Run the server."""
        # Add health check by default
        self.add_health_check()
        
        # Run the server using FastMCP's run method
        self.server.run(**kwargs)
