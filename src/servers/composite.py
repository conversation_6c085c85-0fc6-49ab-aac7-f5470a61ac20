"""
Composite MCP Server for Sage Intacct.

This module provides a unified server that combines all individual Intacct modules
(AP, AR, GL) into a single MCP server instance with cross-module capabilities.
"""

import logging
from typing import Dict, Any, Optional, List, Set
from pathlib import Path
import importlib
import asyncio

from fastmcp import FastMCP
from fastmcp.resources import Resource
from fastmcp.exceptions import ToolError

from .core import BaseIntacctServer, IntacctServerError
from ..auth.interfaces import AuthConfig
from ..auth.token_manager import TokenManager
from ..utils.config_manager import ConfigManager
from .cross_module_tools import CrossModuleTools

logger = logging.getLogger(__name__)


class ModuleInfo:
    """Information about a loaded module."""
    
    def __init__(
        self,
        name: str,
        server_class: type,
        enabled: bool = True,
        config: Optional[Dict[str, Any]] = None
    ):
        """Initialize module info."""
        self.name = name
        self.server_class = server_class
        self.enabled = enabled
        self.config = config or {}
        self.instance: Optional[BaseIntacctServer] = None


class IntacctMCPServer(BaseIntacctServer):
    """
    Composite MCP server that integrates all Sage Intacct modules.
    
    This server:
    - Dynamically loads enabled modules
    - Provides cross-module tools
    - Manages namespacing for module tools
    - Offers unified authentication and configuration
    """
    
    # Available modules and their import paths
    AVAILABLE_MODULES = {
        "ap": {
            "module": "src.servers.ap.accounts_payable",
            "class": "AccountsPayableServer",
            "display_name": "Accounts Payable"
        },
        "ar": {
            "module": "src.servers.ar.accounts_receivable", 
            "class": "AccountsReceivableServer",
            "display_name": "Accounts Receivable"
        },
        "gl": {
            "module": "src.servers.gl.general_ledger",
            "class": "GeneralLedgerServer",
            "display_name": "General Ledger"
        }
    }
    
    def __init__(
        self,
        auth_config: Optional[AuthConfig] = None,
        token_manager: Optional[TokenManager] = None,
        config_manager: Optional[ConfigManager] = None,
        enabled_modules: Optional[List[str]] = None,
        **kwargs
    ):
        """
        Initialize the composite server.
        
        Args:
            auth_config: Authentication configuration
            token_manager: Token manager instance
            config_manager: Configuration manager
            enabled_modules: List of modules to enable (None = all)
            **kwargs: Additional FastMCP configuration
        """
        super().__init__(
            name="sage-intacct-mcp",
            version="1.0.0",
            auth_config=auth_config,
            token_manager=token_manager,
            **kwargs
        )
        
        self.config_manager = config_manager
        self.modules: Dict[str, ModuleInfo] = {}
        self.enabled_modules = enabled_modules or list(self.AVAILABLE_MODULES.keys())
        
        # Initialize cross-module tools handler
        self.cross_module_tools = CrossModuleTools(self)
        
        # Initialize composite server with cross-module capabilities
        self._initialize_cross_module_tools()
        self._initialize_cross_module_resources()
    
    def _initialize_cross_module_tools(self) -> None:
        """Initialize tools that work across modules."""
        
        @self.server.tool()
        async def search_across_modules(
            query: str,
            modules: Optional[List[str]] = None,
            limit: int = 20
        ) -> Dict[str, Any]:
            """
            Search across multiple Intacct modules.
            
            Args:
                query: Search query string
                modules: List of modules to search (None = all enabled)
                limit: Maximum results per module
                
            Returns:
                Search results organized by module
            """
            if not self.auth_manager:
                raise ToolError("Authentication not initialized")
            
            search_modules = modules or list(self.modules.keys())
            results = {}
            
            # Search each module in parallel
            tasks = []
            for module_name in search_modules:
                if module_name in self.modules and self.modules[module_name].enabled:
                    tasks.append(
                        self._search_module(module_name, query, limit)
                    )
            
            if tasks:
                module_results = await asyncio.gather(*tasks, return_exceptions=True)
                for i, module_name in enumerate(search_modules):
                    if isinstance(module_results[i], Exception):
                        logger.error(f"Search failed for {module_name}: {module_results[i]}")
                        results[module_name] = {"error": str(module_results[i])}
                    else:
                        results[module_name] = module_results[i]
            
            return {
                "query": query,
                "modules_searched": search_modules,
                "results": results
            }
        
        @self.server.tool()
        async def get_financial_summary(
            start_date: Optional[str] = None,
            end_date: Optional[str] = None,
            include_modules: Optional[List[str]] = None
        ) -> Dict[str, Any]:
            """
            Get a financial summary across modules.
            
            Args:
                start_date: Start date (YYYY-MM-DD)
                end_date: End date (YYYY-MM-DD)
                include_modules: Modules to include (default: all)
                
            Returns:
                Financial summary data
            """
            if not self.auth_manager:
                raise ToolError("Authentication not initialized")
            
            summary = {
                "period": {
                    "start": start_date,
                    "end": end_date
                },
                "modules": {}
            }
            
            # Gather summary from each module
            modules = include_modules or list(self.modules.keys())
            
            for module_name in modules:
                if module_name in self.modules and self.modules[module_name].enabled:
                    try:
                        module_summary = await self._get_module_summary(
                            module_name, start_date, end_date
                        )
                        summary["modules"][module_name] = module_summary
                    except Exception as e:
                        logger.error(f"Failed to get summary for {module_name}: {e}")
                        summary["modules"][module_name] = {"error": str(e)}
            
            return summary
        
        @self.server.tool()
        async def execute_month_end_close(
            period: str,
            modules: Optional[List[str]] = None,
            dry_run: bool = True
        ) -> Dict[str, Any]:
            """
            Execute month-end close procedures across modules.
            
            Args:
                period: Period to close (YYYY-MM)
                modules: Modules to include (default: all)
                dry_run: If True, simulate without making changes
                
            Returns:
                Month-end close results
            """
            return await self.cross_module_tools.execute_month_end_close(
                period=period,
                modules=modules,
                dry_run=dry_run
            )
        
        @self.server.tool()
        async def generate_consolidated_report(
            report_type: str,
            start_date: str,
            end_date: str,
            include_modules: Optional[List[str]] = None,
            format: str = "json"
        ) -> Dict[str, Any]:
            """
            Generate a consolidated report across modules.
            
            Args:
                report_type: Type of report (financial_summary, cash_flow, etc.)
                start_date: Start date (YYYY-MM-DD)
                end_date: End date (YYYY-MM-DD)
                include_modules: Modules to include
                format: Output format (json, csv, pdf)
                
            Returns:
                Consolidated report data
            """
            return await self.cross_module_tools.generate_consolidated_report(
                report_type=report_type,
                start_date=start_date,
                end_date=end_date,
                include_modules=include_modules,
                format=format
            )
        
        @self.server.tool()
        async def list_enabled_modules() -> Dict[str, Any]:
            """
            List all enabled modules and their status.
            
            Returns:
                Module status information
            """
            return {
                "enabled_modules": [
                    {
                        "name": name,
                        "display_name": self.AVAILABLE_MODULES[name]["display_name"],
                        "loaded": name in self.modules,
                        "status": "active" if info.instance else "not_initialized"
                    }
                    for name, info in self.modules.items()
                    if info.enabled
                ],
                "available_modules": [
                    {
                        "name": name,
                        "display_name": config["display_name"]
                    }
                    for name, config in self.AVAILABLE_MODULES.items()
                ]
            }
    
    def _initialize_cross_module_resources(self) -> None:
        """Initialize resources that aggregate data across modules."""
        
        @self.server.resource("intacct://financial-overview")
        async def get_financial_overview() -> str:
            """Get current financial overview across all modules."""
            try:
                # This would fetch real-time data in production
                return """
                # Financial Overview
                
                ## Accounts Payable
                - Outstanding Bills: $125,430.00
                - Upcoming Payments: $45,200.00
                
                ## Accounts Receivable  
                - Outstanding Invoices: $234,567.00
                - Overdue Amount: $12,340.00
                
                ## General Ledger
                - Current Period: 2024-Q4
                - Trial Balance: Balanced
                """
            except Exception as e:
                return f"Error fetching financial overview: {str(e)}"
    
    async def initialize_modules(self) -> None:
        """
        Initialize all enabled modules.
        
        This method:
        1. Dynamically imports enabled modules
        2. Creates module instances
        3. Registers module tools with namespacing
        """
        logger.info(f"Initializing modules: {self.enabled_modules}")
        
        for module_name in self.enabled_modules:
            if module_name not in self.AVAILABLE_MODULES:
                logger.warning(f"Unknown module: {module_name}")
                continue
            
            try:
                await self._load_module(module_name)
            except Exception as e:
                logger.error(f"Failed to load module {module_name}: {e}")
                if self.config_manager and self.config_manager.get("strict_module_loading"):
                    raise
    
    async def _load_module(self, module_name: str) -> None:
        """Load a single module."""
        module_config = self.AVAILABLE_MODULES[module_name]
        
        # Import the module
        try:
            module = importlib.import_module(module_config["module"])
            server_class = getattr(module, module_config["class"])
        except (ImportError, AttributeError) as e:
            raise IntacctServerError(f"Failed to import {module_name}: {e}")
        
        # Get module-specific configuration
        module_settings = {}
        if self.config_manager:
            module_settings = self.config_manager.get(f"modules.{module_name}", {})
        
        # Create module info
        module_info = ModuleInfo(
            name=module_name,
            server_class=server_class,
            enabled=True,
            config=module_settings
        )
        
        # Create module instance
        module_info.instance = server_class(
            auth_config=self.auth_config,
            token_manager=self.token_manager,
            **module_settings
        )
        
        self.modules[module_name] = module_info
        logger.info(f"Loaded module: {module_name}")
    
    async def _search_module(
        self, 
        module_name: str, 
        query: str, 
        limit: int
    ) -> Dict[str, Any]:
        """Search within a specific module."""
        # This is a placeholder - actual implementation would call
        # module-specific search endpoints
        return {
            "module": module_name,
            "query": query,
            "count": 0,
            "results": []
        }
    
    async def _get_module_summary(
        self,
        module_name: str,
        start_date: Optional[str],
        end_date: Optional[str]
    ) -> Dict[str, Any]:
        """Get financial summary for a specific module."""
        # Placeholder implementation
        return {
            "module": module_name,
            "period": {"start": start_date, "end": end_date},
            "data": {}
        }
    
    def run_server(self, **kwargs) -> None:
        """
        Run the composite server with module initialization.
        
        This method handles the complete server lifecycle.
        """
        # Default to stdio if no transport specified
        if 'transport' not in kwargs and not kwargs:
            # Run with stdio transport (default for Claude Desktop)
            self.run()
        else:
            # Run the server using parent class method with provided kwargs
            self.run(**kwargs)
    
    def _add_composite_health_check(self) -> None:
        """Add comprehensive health check for all modules."""
        
        @self.server.tool()
        async def composite_health_check() -> Dict[str, Any]:
            """Check health status of all modules."""
            health = {
                "status": "healthy",
                "server": self.name,
                "version": self.version,
                "modules": {}
            }
            
            # Check each module
            for name, info in self.modules.items():
                module_health = {
                    "enabled": info.enabled,
                    "loaded": info.instance is not None
                }
                
                if info.instance:
                    try:
                        # Try to check module-specific health
                        module_health["status"] = "active"
                    except Exception as e:
                        module_health["status"] = "error"
                        module_health["error"] = str(e)
                        health["status"] = "degraded"
                
                health["modules"][name] = module_health
            
            return health


def create_composite_server(
    config_path: Optional[str] = None,
    enabled_modules: Optional[List[str]] = None,
    **kwargs
) -> IntacctMCPServer:
    """
    Create a composite Intacct MCP server.
    
    Args:
        config_path: Path to configuration file
        enabled_modules: List of modules to enable
        **kwargs: Additional server configuration
        
    Returns:
        Configured composite server instance
    """
    # Load configuration
    config_manager = None
    if config_path:
        from ..utils.config_manager import ConfigManager
        config_manager = ConfigManager(config_path)
    
    # Create auth config
    auth_config = None
    if config_manager:
        auth_settings = config_manager.get("authentication", {})
        if auth_settings:
            from ..auth.interfaces import AuthConfig
            auth_config = AuthConfig(**auth_settings)
    
    # Create server
    server = IntacctMCPServer(
        auth_config=auth_config,
        config_manager=config_manager,
        enabled_modules=enabled_modules,
        **kwargs
    )
    
    return server
