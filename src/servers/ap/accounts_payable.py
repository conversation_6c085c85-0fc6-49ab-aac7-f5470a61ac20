"""
Accounts Payable MCP Server implementation.

This module provides an MCP server for Sage Intacct Accounts Payable operations,
including bills, payments, vendors, and related functionality.
"""

import logging
import os
from pathlib import Path
from typing import Optional, Dict, Any

import httpx
from fastmcp import FastMCP
from fastmcp.server.openapi import RouteMap, MCPType, HTTPRoute
from fastmcp.server.openapi import (
    OpenAPITool, 
    OpenAPIResource, 
    OpenAPIResourceTemplate
)

from ..core import BaseIntacctServer, IntacctServerError
from ...auth.interfaces import AuthConfig
from ...auth.token_manager import TokenManager

logger = logging.getLogger(__name__)


class AccountsPayableServer(BaseIntacctServer):
    """
    MCP server for Sage Intacct Accounts Payable operations.
    
    Provides access to:
    - Bills and bill management
    - Payments and payment processing
    - Vendors and vendor management
    - Adjustments and advances
    - Terms and account groups
    """
    
    def __init__(
        self,
        auth_config: Optional[AuthConfig] = None,
        token_manager: Optional[TokenManager] = None,
        **kwargs
    ):
        """Initialize the Accounts Payable server."""
        super().__init__(
            name="sage-intacct-ap",
            version="1.0.0",
            auth_config=auth_config,
            token_manager=token_manager,
            **kwargs
        )
        
        # Initialize the MCP server
        self.mcp_server: Optional[FastMCP] = None
        
        # OpenAPI spec path
        self.spec_path = Path(__file__).parent.parent.parent.parent / "openapi" / "accounts-payable.openapi.yaml"
        
        # Custom route maps for AP-specific behavior
        self.route_maps = [
            # Bills - keep as resources for read operations
            RouteMap(
                methods=["GET"],
                pattern=r"^/objects/accounts-payable\.bill.*",
                mcp_type=MCPType.RESOURCE
            ),
            
            # Payments - all operations as tools (including GET)
            RouteMap(
                methods=["*"],
                pattern=r"^/objects/accounts-payable\.payment.*",
                mcp_type=MCPType.TOOL
            ),
            
            # Vendors - keep standard behavior
            RouteMap(
                methods=["GET"],
                pattern=r"^/objects/accounts-payable\.vendor.*",
                mcp_type=MCPType.RESOURCE
            ),
            
            # Summary operations - always tools
            RouteMap(
                methods=["*"],
                pattern=r".*summary.*",
                mcp_type=MCPType.TOOL
            ),
            
            # Exclude internal/admin endpoints
            RouteMap(
                tags={"internal", "admin"},
                mcp_type=MCPType.EXCLUDE
            ),
        ]
        
        # Component name mappings for better usability
        self.mcp_names = {
            # Bills
            "list_accounts_payable_bill": "list_bills",
            "get_accounts_payable_bill": "get_bill",
            "create_accounts_payable_bill": "create_bill",
            "update_accounts_payable_bill": "update_bill",
            "delete_accounts_payable_bill": "delete_bill",
            
            # Payments
            "list_accounts_payable_payment": "list_payments",
            "get_accounts_payable_payment": "get_payment",
            "create_accounts_payable_payment": "create_payment",
            "update_accounts_payable_payment": "update_payment",
            "delete_accounts_payable_payment": "delete_payment",
            
            # Vendors
            "list_accounts_payable_vendor": "list_vendors",
            "get_accounts_payable_vendor": "get_vendor",
            "create_accounts_payable_vendor": "create_vendor",
            "update_accounts_payable_vendor": "update_vendor",
            "delete_accounts_payable_vendor": "delete_vendor",
        }
    
    async def initialize(self) -> None:
        """Initialize the AP server with authentication and OpenAPI spec."""
        logger.info("Initializing Accounts Payable server...")
        
        # Initialize authentication
        await self.initialize_auth()
        
        # Get authenticated client
        client = await self.auth_manager.get_authenticated_client()
        
        # Load OpenAPI spec
        if not self.spec_path.exists():
            raise IntacctServerError(f"OpenAPI spec not found: {self.spec_path}")
        
        # Create MCP server from OpenAPI spec
        self.mcp_server = FastMCP.from_openapi(
            openapi_spec=self._load_openapi_spec(),
            client=client,
            name=self.name,
            route_maps=self.route_maps,
            mcp_names=self.mcp_names,
            mcp_component_fn=self._customize_component,
            timeout=30.0  # 30 second timeout for API requests
        )
        
        # Add custom tools
        self._add_custom_tools()
        
        logger.info("Accounts Payable server initialized successfully")
    
    def _load_openapi_spec(self) -> Dict[str, Any]:
        """Load and parse the OpenAPI specification."""
        import yaml
        
        with open(self.spec_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def _customize_component(
        self,
        route: HTTPRoute,
        component: OpenAPITool | OpenAPIResource | OpenAPIResourceTemplate
    ) -> None:
        """Customize MCP components for better usability."""
        # Add AP-specific tags
        component.tags.add("accounts-payable")
        
        # Enhance descriptions based on component type
        if isinstance(component, OpenAPITool):
            component.description = f"🔧 AP Operation: {component.description}"
        
        elif isinstance(component, OpenAPIResource):
            component.description = f"📊 AP Data: {component.description}"
            component.tags.add("ap-data")
        
        # Add specific tags based on path
        if "bill" in route.path:
            component.tags.add("bills")
        elif "payment" in route.path:
            component.tags.add("payments")
        elif "vendor" in route.path:
            component.tags.add("vendors")
        elif "term" in route.path:
            component.tags.add("terms")
    
    def _add_custom_tools(self) -> None:
        """Add custom tools specific to AP operations."""
        
        @self.mcp_server.tool()
        async def ap_health_check() -> Dict[str, Any]:
            """Check Accounts Payable server health and authentication status."""
            health = {
                "server": "accounts-payable",
                "status": "healthy",
                "authenticated": False,
                "capabilities": []
            }
            
            # Check authentication
            try:
                client = await self.auth_manager.get_authenticated_client()
                health["authenticated"] = True
                
                # List available capabilities
                tools = await self.mcp_server.get_tools()
                resources = await self.mcp_server.get_resources()
                templates = await self.mcp_server.get_resource_templates()
                
                health["capabilities"] = {
                    "tools": len(tools),
                    "resources": len(resources),
                    "resource_templates": len(templates),
                    "total": len(tools) + len(resources) + len(templates)
                }
            except Exception as e:
                health["status"] = "degraded"
                health["error"] = str(e)
            
            return health
        
        @self.mcp_server.tool()
        async def ap_quick_summary() -> Dict[str, Any]:
            """Get a quick summary of AP status including bills and payments."""
            summary = {
                "bills": {"total": 0, "unpaid": 0, "overdue": 0},
                "payments": {"total": 0, "recent": 0},
                "vendors": {"total": 0, "active": 0}
            }
            
            try:
                client = await self.auth_manager.get_authenticated_client()
                
                # This is a placeholder - actual implementation would query the API
                # Example: response = await client.get("/objects/accounts-payable.bill")
                
                summary["status"] = "available"
            except Exception as e:
                summary["status"] = "error"
                summary["error"] = str(e)
            
            return summary
    
    async def run_async(self) -> None:
        """Run the server asynchronously."""
        await self.initialize()
        await self.mcp_server.run_async()
    
    def run(self, **kwargs) -> None:
        """Run the server synchronously."""
        import asyncio
        
        # Initialize first
        asyncio.run(self.initialize())
        
        # Then run the MCP server
        self.mcp_server.run(**kwargs)


# Convenience function for running the server
def run_ap_server(
    auth_config: Optional[AuthConfig] = None,
    **kwargs
) -> None:
    """Run the Accounts Payable MCP server."""
    server = AccountsPayableServer(auth_config=auth_config, **kwargs)
    server.run()


if __name__ == "__main__":
    # Run with default configuration
    run_ap_server()
