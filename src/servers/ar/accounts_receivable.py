"""
Accounts Receivable MCP Server implementation.

This module provides an MCP server for Sage Intacct Accounts Receivable operations,
including invoices, payments, customers, and related functionality.
"""

import logging
import os
from pathlib import Path
from typing import Optional, Dict, Any

import httpx
from fastmcp import FastMCP
from fastmcp.server.openapi import RouteMap, MCPType, HTTPRoute
from fastmcp.server.openapi import (
    OpenAPITool, 
    OpenAPIResource, 
    OpenAPIResourceTemplate
)

from ..core import BaseIntacctServer, IntacctServerError
from ...auth.interfaces import AuthConfig
from ...auth.token_manager import TokenManager

logger = logging.getLogger(__name__)


class AccountsReceivableServer(BaseIntacctServer):
    """
    MCP server for Sage Intacct Accounts Receivable operations.
    
    Provides access to:
    - Invoices and invoice management
    - Customer payments and receipts
    - Customer management
    - Adjustments and advances
    - Customer types and groups
    """
    
    def __init__(
        self,
        auth_config: Optional[AuthConfig] = None,
        token_manager: Optional[TokenManager] = None,
        **kwargs
    ):
        """Initialize the Accounts Receivable server."""
        super().__init__(
            name="sage-intacct-ar",
            version="1.0.0",
            auth_config=auth_config,
            token_manager=token_manager,
            **kwargs
        )
        
        # Initialize the MCP server
        self.mcp_server: Optional[FastMCP] = None
        
        # OpenAPI spec path
        self.spec_path = Path(__file__).parent.parent.parent.parent / "openapi" / "accounts-receivable.openapi.yaml"
        
        # Custom route maps for AR-specific behavior
        self.route_maps = [
            # Invoices - keep as resources for read operations
            RouteMap(
                methods=["GET"],
                pattern=r"^/objects/accounts-receivable\.invoice.*",
                mcp_type=MCPType.RESOURCE
            ),
            
            # Customer payments - all operations as tools
            RouteMap(
                methods=["*"],
                pattern=r"^/objects/accounts-receivable\.payment.*",
                mcp_type=MCPType.TOOL
            ),
            
            # Customers - standard REST behavior
            RouteMap(
                methods=["GET"],
                pattern=r"^/objects/accounts-receivable\.customer.*",
                mcp_type=MCPType.RESOURCE
            ),
            
            # Adjustments - all operations as tools
            RouteMap(
                methods=["*"],
                pattern=r"^/objects/accounts-receivable\.adjustment.*",
                mcp_type=MCPType.TOOL
            ),
            
            # Invoice summaries - always tools
            RouteMap(
                methods=["*"],
                pattern=r".*invoice-summary.*",
                mcp_type=MCPType.TOOL
            ),
            
            # Exclude internal/admin endpoints
            RouteMap(
                tags={"internal", "admin"},
                mcp_type=MCPType.EXCLUDE
            ),
        ]
        
        # Component name mappings for better usability
        self.mcp_names = {
            # Invoices
            "list_accounts_receivable_invoice": "list_invoices",
            "get_accounts_receivable_invoice": "get_invoice",
            "create_accounts_receivable_invoice": "create_invoice",
            "update_accounts_receivable_invoice": "update_invoice",
            "delete_accounts_receivable_invoice": "delete_invoice",
            
            # Payments
            "list_accounts_receivable_payment": "list_receipts",
            "get_accounts_receivable_payment": "get_receipt",
            "create_accounts_receivable_payment": "create_receipt",
            "update_accounts_receivable_payment": "update_receipt",
            "delete_accounts_receivable_payment": "delete_receipt",
            
            # Customers
            "list_accounts_receivable_customer": "list_customers",
            "get_accounts_receivable_customer": "get_customer",
            "create_accounts_receivable_customer": "create_customer",
            "update_accounts_receivable_customer": "update_customer",
            "delete_accounts_receivable_customer": "delete_customer",
            
            # Adjustments
            "list_accounts_receivable_adjustment": "list_adjustments",
            "get_accounts_receivable_adjustment": "get_adjustment",
            "create_accounts_receivable_adjustment": "create_adjustment",
        }
    
    async def initialize(self) -> None:
        """Initialize the AR server with authentication and OpenAPI spec."""
        logger.info("Initializing Accounts Receivable server...")
        
        # Initialize authentication
        await self.initialize_auth()
        
        # Get authenticated client
        client = await self.auth_manager.get_authenticated_client()
        
        # Load OpenAPI spec
        if not self.spec_path.exists():
            raise IntacctServerError(f"OpenAPI spec not found: {self.spec_path}")
        
        # Create MCP server from OpenAPI spec
        self.mcp_server = FastMCP.from_openapi(
            openapi_spec=self._load_openapi_spec(),
            client=client,
            name=self.name,
            route_maps=self.route_maps,
            mcp_names=self.mcp_names,
            mcp_component_fn=self._customize_component,
            timeout=30.0  # 30 second timeout for API requests
        )
        
        # Add custom tools
        self._add_custom_tools()
        
        logger.info("Accounts Receivable server initialized successfully")
    
    def _load_openapi_spec(self) -> Dict[str, Any]:
        """Load and parse the OpenAPI specification."""
        import yaml
        
        with open(self.spec_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def _customize_component(
        self,
        route: HTTPRoute,
        component: OpenAPITool | OpenAPIResource | OpenAPIResourceTemplate
    ) -> None:
        """Customize MCP components for better usability."""
        # Add AR-specific tags
        component.tags.add("accounts-receivable")
        
        # Enhance descriptions based on component type
        if isinstance(component, OpenAPITool):
            component.description = f"💰 AR Operation: {component.description}"
        
        elif isinstance(component, OpenAPIResource):
            component.description = f"📊 AR Data: {component.description}"
            component.tags.add("ar-data")
        
        # Add specific tags based on path
        if "invoice" in route.path:
            component.tags.add("invoices")
        elif "payment" in route.path:
            component.tags.add("receipts")
        elif "customer" in route.path:
            component.tags.add("customers")
        elif "adjustment" in route.path:
            component.tags.add("adjustments")
        elif "advance" in route.path:
            component.tags.add("advances")
    
    def _add_custom_tools(self) -> None:
        """Add custom tools specific to AR operations."""
        
        @self.mcp_server.tool()
        async def ar_health_check() -> Dict[str, Any]:
            """Check Accounts Receivable server health and authentication status."""
            health = {
                "server": "accounts-receivable",
                "status": "healthy",
                "authenticated": False,
                "capabilities": []
            }
            
            # Check authentication
            try:
                client = await self.auth_manager.get_authenticated_client()
                health["authenticated"] = True
                
                # List available capabilities
                tools = await self.mcp_server.get_tools()
                resources = await self.mcp_server.get_resources()
                templates = await self.mcp_server.get_resource_templates()
                
                health["capabilities"] = {
                    "tools": len(tools),
                    "resources": len(resources),
                    "resource_templates": len(templates),
                    "total": len(tools) + len(resources) + len(templates)
                }
            except Exception as e:
                health["status"] = "degraded"
                health["error"] = str(e)
            
            return health
        
        @self.mcp_server.tool()
        async def ar_dashboard() -> Dict[str, Any]:
            """Get AR dashboard with key metrics and outstanding balances."""
            dashboard = {
                "invoices": {"total": 0, "unpaid": 0, "overdue": 0, "total_outstanding": 0},
                "receipts": {"total": 0, "recent": 0, "total_amount": 0},
                "customers": {"total": 0, "active": 0, "new_this_month": 0},
                "aging": {"current": 0, "30_days": 0, "60_days": 0, "90_plus": 0}
            }
            
            try:
                client = await self.auth_manager.get_authenticated_client()
                
                # This is a placeholder - actual implementation would query the API
                # Example: response = await client.get("/objects/accounts-receivable.invoice")
                
                dashboard["status"] = "available"
            except Exception as e:
                dashboard["status"] = "error"
                dashboard["error"] = str(e)
            
            return dashboard
        
        @self.mcp_server.tool()
        async def ar_aging_report(
            as_of_date: Optional[str] = None,
            customer_id: Optional[str] = None
        ) -> Dict[str, Any]:
            """
            Generate an accounts receivable aging report.
            
            Args:
                as_of_date: Date for the aging report (YYYY-MM-DD)
                customer_id: Optional customer ID to filter by
            """
            report = {
                "as_of_date": as_of_date or "current",
                "summary": {
                    "current": {"count": 0, "amount": 0},
                    "1_30_days": {"count": 0, "amount": 0},
                    "31_60_days": {"count": 0, "amount": 0},
                    "61_90_days": {"count": 0, "amount": 0},
                    "over_90_days": {"count": 0, "amount": 0},
                    "total": {"count": 0, "amount": 0}
                },
                "details": []
            }
            
            try:
                client = await self.auth_manager.get_authenticated_client()
                
                # Placeholder for actual implementation
                report["status"] = "generated"
            except Exception as e:
                report["status"] = "error"
                report["error"] = str(e)
            
            return report
    
    async def run_async(self) -> None:
        """Run the server asynchronously."""
        await self.initialize()
        await self.mcp_server.run_async()
    
    def run(self, **kwargs) -> None:
        """Run the server synchronously."""
        import asyncio
        
        # Initialize first
        asyncio.run(self.initialize())
        
        # Then run the MCP server
        self.mcp_server.run(**kwargs)


# Convenience function for running the server
def run_ar_server(
    auth_config: Optional[AuthConfig] = None,
    **kwargs
) -> None:
    """Run the Accounts Receivable MCP server."""
    server = AccountsReceivableServer(auth_config=auth_config, **kwargs)
    server.run()


if __name__ == "__main__":
    # Run with default configuration
    run_ar_server()
