"""
General Ledger MCP Server implementation.

This module provides an MCP server for Sage Intacct General Ledger operations,
including journal entries, accounts, budgets, and financial reporting.
"""

import logging
import os
from pathlib import Path
from typing import Optional, Dict, Any

import httpx
from fastmcp import FastMCP
from fastmcp.server.openapi import RouteMap, MCPType, HTTPRoute
from fastmcp.server.openapi import (
    OpenAPITool, 
    OpenAPIResource, 
    OpenAPIResourceTemplate
)

from ..core import BaseIntacctServer, IntacctServerError
from ...auth.interfaces import AuthConfig
from ...auth.token_manager import TokenManager

logger = logging.getLogger(__name__)


class GeneralLedgerServer(BaseIntacctServer):
    """
    MCP server for Sage Intacct General Ledger operations.
    
    Provides access to:
    - Journal entries and journal management
    - Chart of accounts
    - Account balances and transactions
    - Budget management
    - Statistical accounts and journals
    - Financial reporting
    """
    
    def __init__(
        self,
        auth_config: Optional[AuthConfig] = None,
        token_manager: Optional[TokenManager] = None,
        **kwargs
    ):
        """Initialize the General Ledger server."""
        super().__init__(
            name="sage-intacct-gl",
            version="1.0.0",
            auth_config=auth_config,
            token_manager=token_manager,
            **kwargs
        )
        
        # Initialize the MCP server
        self.mcp_server: Optional[FastMCP] = None
        
        # OpenAPI spec path
        self.spec_path = Path(__file__).parent.parent.parent.parent / "openapi" / "general-ledger.openapi.yaml"
        
        # Custom route maps for GL-specific behavior
        self.route_maps = [
            # Journal entries - all operations as tools (critical GL operations)
            RouteMap(
                methods=["*"],
                pattern=r"^/objects/general-ledger\.journal.*",
                mcp_type=MCPType.TOOL
            ),
            
            # Accounts - keep as resources for read operations
            RouteMap(
                methods=["GET"],
                pattern=r"^/objects/general-ledger\.account.*",
                mcp_type=MCPType.RESOURCE
            ),
            
            # Account balances - always resources (read-only data)
            RouteMap(
                methods=["*"],
                pattern=r".*account-balance.*",
                mcp_type=MCPType.RESOURCE
            ),
            
            # Budgets - tools for modifications
            RouteMap(
                methods=["POST", "PUT", "PATCH", "DELETE"],
                pattern=r"^/objects/general-ledger\.budget.*",
                mcp_type=MCPType.TOOL
            ),
            
            # Statistical accounts and journals - all as tools
            RouteMap(
                methods=["*"],
                pattern=r"^/objects/general-ledger\.statistical.*",
                mcp_type=MCPType.TOOL
            ),
            
            # Transaction details - resources for viewing
            RouteMap(
                methods=["GET"],
                pattern=r".*transaction.*",
                mcp_type=MCPType.RESOURCE
            ),
            
            # Trial balance and reports - resources
            RouteMap(
                methods=["*"],
                pattern=r".*trial-balance.*",
                mcp_type=MCPType.RESOURCE
            ),
            
            # Exclude internal/admin endpoints
            RouteMap(
                methods=["*"],
                pattern=r".*/admin/.*",
                mcp_type=MCPType.EXCLUDE
            ),
            
            # Default: GET = resource, others = tool
            RouteMap(
                methods=["GET"],
                pattern=r".*",
                mcp_type=MCPType.RESOURCE
            ),
            RouteMap(
                methods=["POST", "PUT", "PATCH", "DELETE"],
                pattern=r".*",
                mcp_type=MCPType.TOOL
            )
        ]
        
        # Component name mappings for better usability
        self.mcp_names = {
            # Journal entries
            "list_general_ledger_journal": "list_journals",
            "get_general_ledger_journal": "get_journal",
            "create_general_ledger_journal": "create_journal",
            "update_general_ledger_journal": "update_journal",
            "delete_general_ledger_journal": "delete_journal",
            
            # Accounts
            "list_general_ledger_account": "list_accounts",
            "get_general_ledger_account": "get_account",
            "create_general_ledger_account": "create_account",
            "update_general_ledger_account": "update_account",
            "delete_general_ledger_account": "delete_account",
            
            # Budgets
            "list_general_ledger_budget": "list_budgets",
            "get_general_ledger_budget": "get_budget",
            "create_general_ledger_budget": "create_budget",
            "update_general_ledger_budget": "update_budget",
            "delete_general_ledger_budget": "delete_budget",
            
            # Statistical journals
            "list_general_ledger_statistical_journal": "list_statistical_journals",
            "get_general_ledger_statistical_journal": "get_statistical_journal",
            "create_general_ledger_statistical_journal": "create_statistical_journal",
            
            # Statistical accounts
            "list_general_ledger_statistical_account": "list_statistical_accounts",
            "get_general_ledger_statistical_account": "get_statistical_account",
            "create_general_ledger_statistical_account": "create_statistical_account",
        }
    
    async def initialize_server(self) -> FastMCP:
        """
        Initialize and configure the GL MCP server.
        
        Returns:
            FastMCP: Configured server instance
        """
        if self.mcp_server:
            return self.mcp_server
            
        logger.info("Initializing General Ledger MCP server...")
        
        # Get authenticated client
        client = await self.get_authenticated_client()
        
        # Create the MCP server
        self.mcp_server = FastMCP(
            name=self.config.name
        )
        
        # Add server metadata
        self.mcp_server.server.name = self.config.name
        self.mcp_server.server.version = self.config.version
        
        # Load OpenAPI spec and configure tools/resources
        if self.spec_path.exists():
            logger.info(f"Loading GL OpenAPI spec from {self.spec_path}")
            try:
                # Configure the server from OpenAPI spec
                # We'll use the authenticated client for all API calls
                tools, resources = self._configure_from_openapi(client)
                
                # Register tools
                for tool in tools:
                    self.mcp_server.tool(tool.name)(tool.endpoint)
                    logger.debug(f"Registered GL tool: {tool.name}")
                
                # Register resources
                for resource in resources:
                    self.mcp_server.resource(resource.uri)(resource.endpoint)
                    logger.debug(f"Registered GL resource: {resource.uri}")
                
                logger.info(f"GL server initialized with {len(tools)} tools and {len(resources)} resources")
                
            except Exception as e:
                logger.error(f"Failed to load GL OpenAPI spec: {e}")
                raise IntacctServerError(f"Failed to initialize GL server: {e}")
        else:
            logger.warning(f"GL OpenAPI spec not found at {self.spec_path}")
        
        return self.mcp_server
    
    def _configure_from_openapi(self, client: httpx.AsyncClient) -> tuple[list[OpenAPITool], list[OpenAPIResource]]:
        """
        Configure tools and resources from OpenAPI spec.
        
        Args:
            client: Authenticated HTTP client
            
        Returns:
            Tuple of (tools, resources)
        """
        from fastmcp import FastMCP
        
        # Create a temporary FastMCP instance to use from_openapi
        temp_mcp = FastMCP(name="temp")
        
        # Configure from OpenAPI with our custom route maps
        temp_mcp.from_openapi(
            spec_path=str(self.spec_path),
            base_url=self.config.api_base_url,
            client=client,
            route_maps=self.route_maps
        )
        
        # Extract tools and resources
        tools = []
        resources = []
        
        # Get tools from the temporary server
        for tool_name, tool_func in temp_mcp._tools.items():
            tool = OpenAPITool(
                name=tool_name,
                endpoint=tool_func
            )
            tools.append(tool)
        
        # Get resources from the temporary server
        for resource_uri, resource_func in temp_mcp._resources.items():
            resource = OpenAPIResource(
                uri=resource_uri,
                endpoint=resource_func
            )
            resources.append(resource)
        
        return tools, resources


def create_general_ledger_server(
    auth_config: Optional[AuthConfig] = None,
    token_manager: Optional[TokenManager] = None,
    **kwargs
) -> GeneralLedgerServer:
    """
    Factory function to create a General Ledger MCP server.
    
    Args:
        auth_config: Authentication configuration
        token_manager: Token manager instance
        **kwargs: Additional server configuration
        
    Returns:
        GeneralLedgerServer: Configured GL server instance
    """
    return GeneralLedgerServer(
        auth_config=auth_config,
        token_manager=token_manager,
        **kwargs
    )
