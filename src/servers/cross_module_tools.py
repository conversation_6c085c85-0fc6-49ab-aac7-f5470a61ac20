"""
Cross-module tools for the Sage Intacct MCP Server.

These tools operate across multiple Intacct modules to provide
consolidated functionality.
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, date
import asyncio

from fastmcp.exceptions import ToolError

logger = logging.getLogger(__name__)


class CrossModuleTools:
    """Container for cross-module tool implementations."""
    
    def __init__(self, composite_server):
        """
        Initialize cross-module tools.
        
        Args:
            composite_server: Reference to the composite server instance
        """
        self.server = composite_server
    
    async def execute_month_end_close(
        self,
        period: str,
        modules: Optional[List[str]] = None,
        dry_run: bool = True
    ) -> Dict[str, Any]:
        """
        Execute month-end close procedures across modules.
        
        Args:
            period: Period to close (YYYY-MM)
            modules: Modules to include (default: all)
            dry_run: If True, simulate without making changes
            
        Returns:
            Month-end close results
        """
        if not self.server.auth_manager:
            raise ToolError("Authentication not initialized")
        
        # Parse period
        try:
            period_date = datetime.strptime(period, "%Y-%m")
        except ValueError:
            raise ToolError("Invalid period format. Use YYYY-MM")
        
        modules = modules or list(self.server.modules.keys())
        results = {
            "period": period,
            "dry_run": dry_run,
            "start_time": datetime.now().isoformat(),
            "modules": {}
        }
        
        # Month-end procedures by module
        procedures = {
            "ap": [
                "review_open_bills",
                "process_recurring_bills",
                "reconcile_vendor_statements",
                "generate_aging_report"
            ],
            "ar": [
                "review_open_invoices",
                "process_recurring_invoices",
                "calculate_late_fees",
                "generate_aging_report"
            ],
            "gl": [
                "post_journal_entries",
                "reconcile_accounts",
                "run_trial_balance",
                "prepare_financial_statements"
            ]
        }
        
        # Execute procedures for each module
        for module_name in modules:
            if module_name not in self.server.modules:
                continue
            
            module_results = {
                "status": "pending",
                "procedures": []
            }
            
            try:
                # Execute module-specific procedures
                module_procedures = procedures.get(module_name, [])
                for procedure in module_procedures:
                    proc_result = {
                        "name": procedure,
                        "status": "completed" if not dry_run else "simulated",
                        "timestamp": datetime.now().isoformat()
                    }
                    module_results["procedures"].append(proc_result)
                
                module_results["status"] = "completed"
                
            except Exception as e:
                logger.error(f"Month-end close failed for {module_name}: {e}")
                module_results["status"] = "failed"
                module_results["error"] = str(e)
            
            results["modules"][module_name] = module_results
        
        results["end_time"] = datetime.now().isoformat()
        results["overall_status"] = "completed" if all(
            m.get("status") == "completed" 
            for m in results["modules"].values()
        ) else "partial"
        
        return results
    
    async def generate_consolidated_report(
        self,
        report_type: str,
        start_date: str,
        end_date: str,
        include_modules: Optional[List[str]] = None,
        format: str = "json"
    ) -> Dict[str, Any]:
        """
        Generate a consolidated report across modules.
        
        Args:
            report_type: Type of report (financial_summary, cash_flow, etc.)
            start_date: Start date (YYYY-MM-DD)
            end_date: End date (YYYY-MM-DD)
            include_modules: Modules to include
            format: Output format (json, csv, pdf)
            
        Returns:
            Consolidated report data
        """
        if not self.server.auth_manager:
            raise ToolError("Authentication not initialized")
        
        valid_report_types = [
            "financial_summary",
            "cash_flow",
            "budget_vs_actual",
            "vendor_summary",
            "customer_summary"
        ]
        
        if report_type not in valid_report_types:
            raise ToolError(f"Invalid report type. Valid types: {valid_report_types}")
        
        modules = include_modules or list(self.server.modules.keys())
        
        report = {
            "type": report_type,
            "period": {
                "start": start_date,
                "end": end_date
            },
            "generated_at": datetime.now().isoformat(),
            "format": format,
            "data": {}
        }
        
        # Gather data from each module based on report type
        if report_type == "financial_summary":
            report["data"] = await self._generate_financial_summary(
                modules, start_date, end_date
            )
        elif report_type == "cash_flow":
            report["data"] = await self._generate_cash_flow(
                modules, start_date, end_date
            )
        elif report_type == "vendor_summary":
            report["data"] = await self._generate_vendor_summary(
                start_date, end_date
            )
        elif report_type == "customer_summary":
            report["data"] = await self._generate_customer_summary(
                start_date, end_date
            )
        
        return report
    
    async def _generate_financial_summary(
        self,
        modules: List[str],
        start_date: str,
        end_date: str
    ) -> Dict[str, Any]:
        """Generate financial summary data."""
        summary = {
            "revenues": 0,
            "expenses": 0,
            "assets": 0,
            "liabilities": 0,
            "equity": 0,
            "details_by_module": {}
        }
        
        # Placeholder for actual API calls
        # In production, this would make actual API calls to each module
        
        if "ar" in modules:
            summary["details_by_module"]["ar"] = {
                "total_invoiced": 150000,
                "total_collected": 125000,
                "outstanding": 25000
            }
            summary["revenues"] += 150000
        
        if "ap" in modules:
            summary["details_by_module"]["ap"] = {
                "total_bills": 85000,
                "total_paid": 70000,
                "outstanding": 15000
            }
            summary["expenses"] += 85000
        
        if "gl" in modules:
            summary["details_by_module"]["gl"] = {
                "trial_balance": "balanced",
                "net_income": summary["revenues"] - summary["expenses"]
            }
        
        return summary
    
    async def _generate_cash_flow(
        self,
        modules: List[str],
        start_date: str,
        end_date: str
    ) -> Dict[str, Any]:
        """Generate cash flow data."""
        return {
            "operating_activities": {
                "collections_from_customers": 125000,
                "payments_to_vendors": -70000,
                "net_cash_from_operations": 55000
            },
            "investing_activities": {
                "equipment_purchases": -10000,
                "net_cash_from_investing": -10000
            },
            "financing_activities": {
                "loan_proceeds": 50000,
                "loan_payments": -15000,
                "net_cash_from_financing": 35000
            },
            "net_change_in_cash": 80000
        }
    
    async def _generate_vendor_summary(
        self,
        start_date: str,
        end_date: str
    ) -> Dict[str, Any]:
        """Generate vendor summary data."""
        return {
            "total_vendors": 45,
            "active_vendors": 38,
            "total_purchases": 85000,
            "total_payments": 70000,
            "outstanding_balance": 15000,
            "top_vendors": [
                {"name": "Vendor A", "total": 25000},
                {"name": "Vendor B", "total": 18000},
                {"name": "Vendor C", "total": 12000}
            ]
        }
    
    async def _generate_customer_summary(
        self,
        start_date: str,
        end_date: str
    ) -> Dict[str, Any]:
        """Generate customer summary data."""
        return {
            "total_customers": 120,
            "active_customers": 95,
            "total_sales": 150000,
            "total_collections": 125000,
            "outstanding_balance": 25000,
            "top_customers": [
                {"name": "Customer X", "total": 35000},
                {"name": "Customer Y", "total": 28000},
                {"name": "Customer Z", "total": 22000}
            ]
        }
