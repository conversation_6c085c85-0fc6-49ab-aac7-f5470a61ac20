"""
Intacct authentication manager for creating authenticated HTTP clients.
"""

import logging
from typing import Optional, Dict, Any, List
from pathlib import Path
import yaml

from ..auth import AuthFlow as AuthenticationFlow, AuthorizationCodeFlow, ClientCredentialsFlow
from ..auth.token_manager import TokenManager
from ..auth import AuthConfig
from .http_client import AuthenticatedClient

logger = logging.getLogger(__name__)


class IntacctAuthManager:
    """
    Manager for Intacct authentication and HTTP client creation.
    
    This class handles:
    - Authentication flow selection and execution
    - Token management
    - Authenticated HTTP client creation
    - Multi-entity support
    """
    
    def __init__(
        self,
        auth_config: AuthConfig,
        token_storage_path: Optional[Path] = None,
        base_url: str = "https://api.intacct.com"
    ):
        """
        Initialize the auth manager.
        
        Args:
            auth_config: Authentication configuration
            token_storage_path: Path for token storage (defaults to .tokens/)
            base_url: Base URL for API requests
        """
        self.auth_config = auth_config
        self.base_url = base_url
        
        # Initialize authentication flow based on config
        self.auth_flow = self._create_auth_flow()
        
        # Initialize token manager
        storage_path = token_storage_path or Path(".tokens")
        from ..auth.storage import FileTokenStorage
        storage = FileTokenStorage(storage_path)
        self.token_manager = TokenManager(storage=storage)
        
        # Cache of authenticated clients per entity
        self._client_cache: Dict[Optional[str], AuthenticatedClient] = {}
        
    def _create_auth_flow(self) -> AuthenticationFlow:
        """Create appropriate authentication flow based on config."""
        if self.auth_config.auth_type == "authorization_code":
            return AuthorizationCodeFlow(self.auth_config)
        elif self.auth_config.auth_type == "client_credentials":
            return ClientCredentialsFlow(self.auth_config)
        else:
            raise ValueError(f"Unsupported auth type: {self.auth_config.auth_type}")
            
    async def initialize(self):
        """
        Initialize the auth manager.
        
        This may trigger authentication if no valid token exists.
        """
        # Try to load existing token
        token = await self.token_manager.get_token(config=self.auth_config)
        
        if not token:
            logger.info("No existing token found, initiating authentication")
            if self.auth_config.auth_type == "authorization_code":
                # For authorization code flow, we need user interaction
                auth_url = self.auth_flow.get_auth_url(self.auth_config)
                print(f"\nPlease visit this URL to authenticate:\n{auth_url.authorization_url}\n")
                print("After authorization, you'll be redirected to a callback URL.")
                print("Please run the 'complete-auth' command with the callback URL.")
                raise AuthenticationError(
                    "Authorization required. Please complete the OAuth flow."
                )
            else:
                # For client credentials, we can authenticate directly
                await self.auth_flow.authenticate()
                
    async def complete_authorization(self, callback_url: str):
        """
        Complete the authorization code flow.
        
        Args:
            callback_url: The callback URL after user authorization
        """
        if self.auth_config.auth_type != "authorization_code":
            raise ValueError("This method is only for authorization code flow")
            
        await self.auth_flow.handle_callback(callback_url)
        logger.info("Authorization completed successfully")        
    async def get_authenticated_client(
        self,
        entity_id: Optional[str] = None,
        timeout: float = 30.0,
        max_retries: int = 3
    ) -> AuthenticatedClient:
        """
        Get an authenticated HTTP client.
        
        Args:
            entity_id: Intacct entity ID for multi-entity support
            timeout: Request timeout in seconds
            max_retries: Maximum retry attempts for 401 errors
            
        Returns:
            Authenticated HTTP client
            
        Raises:
            AuthenticationError: If authentication fails
        """
        # Check cache first
        if entity_id in self._client_cache:
            client = self._client_cache[entity_id]
            # Ensure client is still valid
            if not client._client or not client._client.is_closed:
                return client
                
        # Create new client
        client = AuthenticatedClient(
            auth_config=self.auth_config,
            auth_flow=self.auth_flow,
            token_manager=self.token_manager,
            entity_id=entity_id,
            base_url=self.base_url,
            timeout=timeout,
            max_retries=max_retries
        )
        
        # Cache the client
        self._client_cache[entity_id] = client
        
        return client
        
    async def refresh_token(self):
        """Manually refresh the access token."""
        return await self.token_manager.refresh_token()
        
    async def revoke_token(self):
        """Revoke the current token."""
        await self.token_manager.revoke_token()
        
    async def get_token_info(self) -> Optional[Dict[str, Any]]:
        """
        Get information about the current token.
        
        Returns:
            Token information or None if no token exists
        """
        token = await self.token_manager.get_token(config=self.auth_config)
        if token:
            return {
                "access_token": token.access_token[:10] + "...",  # Partial for security
                "expires_at": token.expires_at.isoformat(),
                "token_type": token.token_type,
                "scope": token.scope,
                "is_expired": token.is_expired
            }
        return None        
    async def list_entities(self) -> List[str]:
        """
        List available Intacct entities for the authenticated user.
        
        Returns:
            List of entity IDs
            
        Note: This requires a valid token and makes an API call.
        """
        async with await self.get_authenticated_client() as client:
            response = await client.get("/entities")
            data = response.json()
            return [entity["id"] for entity in data.get("entities", [])]
            
    async def close(self):
        """Close all cached clients."""
        for client in self._client_cache.values():
            await client.close()
        self._client_cache.clear()
        
    @classmethod
    def from_config_file(
        cls,
        config_path: Path,
        token_storage_path: Optional[Path] = None
    ) -> "IntacctAuthManager":
        """
        Create an auth manager from a configuration file.
        
        Args:
            config_path: Path to auth configuration file
            token_storage_path: Path for token storage
            
        Returns:
            Configured auth manager
        """
        with open(config_path, 'r') as f:
            config_data = yaml.safe_load(f)
            
        auth_config = AuthConfig(**config_data)
        return cls(auth_config, token_storage_path)


# Import at the end to avoid circular imports
from ..auth.exceptions import AuthorizationError as AuthenticationError