"""
Authenticated HTTP client with automatic token refresh and retry logic.
"""

import logging
from typing import Optional, Dict, Any, Union, TYPE_CHECKING
from datetime import datetime, timezone
import httpx
from httpx import Response, Request

from ..auth.token_manager import TokenManager, Token
from ..auth.exceptions import TokenExpiredError, AuthorizationError as AuthenticationError

if TYPE_CHECKING:
    from ..auth import AuthConfig, AuthFlow as AuthenticationFlow

logger = logging.getLogger(__name__)


class AuthenticatedClient:
    """HTTP client with automatic authentication and token refresh."""
    
    def __init__(
        self,
        auth_config: 'AuthConfig',
        auth_flow: 'AuthenticationFlow',
        token_manager: TokenManager,
        entity_id: Optional[str] = None,
        base_url: str = "https://api.intacct.com",
        timeout: float = 30.0,
        max_retries: int = 3
    ):
        """
        Initialize authenticated client.
        
        Args:
            auth_config: Authentication configuration
            auth_flow: Authentication flow instance
            token_manager: Token manager instance
            entity_id: Intacct entity ID for multi-entity support
            base_url: Base URL for API requests
            timeout: Request timeout in seconds
            max_retries: Maximum number of retry attempts for 401 errors
        """
        self.auth_config = auth_config
        self.auth_flow = auth_flow
        self.token_manager = token_manager
        self.entity_id = entity_id
        self.base_url = base_url
        self.timeout = timeout
        self.max_retries = max_retries
        self._client: Optional[httpx.AsyncClient] = None
        
    async def __aenter__(self):
        """Async context manager entry."""
        await self._ensure_client()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()
        
    async def close(self):
        """Close the HTTP client."""
        if self._client:
            await self._client.aclose()
            self._client = None
            
    async def _ensure_client(self):
        """Ensure HTTP client is initialized."""
        if not self._client or self._client.is_closed:
            self._client = httpx.AsyncClient(
                base_url=self.base_url,
                timeout=httpx.Timeout(self.timeout),
                event_hooks={
                    "request": [self._auth_interceptor],
                    "response": [self._response_interceptor]
                }
            )
            
    async def _auth_interceptor(self, request: Request):
        """Add authentication headers to requests."""
        token = await self._get_valid_token()
        
        # Add Authorization header
        request.headers["Authorization"] = f"Bearer {token.access_token}"
        
        # Add entity ID header if specified
        if self.entity_id:
            request.headers["X-Intacct-Entity-ID"] = self.entity_id
            
        # Add standard headers
        request.headers.update({
            "Accept": "application/json",
            "Content-Type": "application/json",
            "User-Agent": "Sage-Intacct-MCP-Server/1.0"
        })
        
        logger.debug(f"Added auth headers to request: {request.method} {request.url}")
        
    async def _response_interceptor(self, response: Response):
        """Handle response errors and retry logic."""
        if response.status_code == 401:
            logger.warning("Received 401 Unauthorized response")
            # Token might be expired, will retry with refreshed token
            
    async def _get_valid_token(self) -> Token:
        """Get a valid token, refreshing if necessary."""
        try:
            token = await self.token_manager.get_token(
                config=self.auth_config,
                flow=self.auth_flow,
                entity_id=self.entity_id
            )
            return token
        except TokenExpiredError:
            logger.info("Token expired, attempting refresh")
            return await self.token_manager.refresh_token(
                config=self.auth_config,
                flow=self.auth_flow,
                entity_id=self.entity_id
            )
            
    async def request(
        self,
        method: str,
        url: str,
        **kwargs
    ) -> Response:
        """
        Make an authenticated HTTP request with retry logic.
        
        Args:
            method: HTTP method
            url: Request URL (relative to base_url)
            **kwargs: Additional request parameters
            
        Returns:
            HTTP response
            
        Raises:
            AuthenticationError: If authentication fails after retries
            httpx.HTTPStatusError: For other HTTP errors
        """
        await self._ensure_client()
        
        last_error = None
        for attempt in range(self.max_retries):
            try:
                response = await self._client.request(method, url, **kwargs)
                
                # If we get a 401, refresh token and retry
                if response.status_code == 401:
                    if attempt < self.max_retries - 1:
                        logger.info(f"Got 401, refreshing token (attempt {attempt + 1}/{self.max_retries})")
                        try:
                            await self.token_manager.refresh_token(
                                config=self.auth_config,
                                flow=self.auth_flow,
                                entity_id=self.entity_id
                            )
                            continue
                        except AuthenticationError as e:
                            logger.error(f"Token refresh failed: {e}")
                            last_error = e
                            break
                            
                response.raise_for_status()
                return response
                
            except httpx.HTTPStatusError as e:
                if e.response.status_code != 401:
                    raise
                last_error = e
                
            except Exception as e:
                logger.error(f"Request failed: {e}")
                last_error = e
                if attempt == self.max_retries - 1:
                    break
                    
        # All retries exhausted
        raise AuthenticationError(f"Authentication failed after {self.max_retries} attempts: {last_error}")
        
    # Convenience methods
    async def get(self, url: str, **kwargs) -> Response:
        """Make a GET request."""
        return await self.request("GET", url, **kwargs)
        
    async def post(self, url: str, **kwargs) -> Response:
        """Make a POST request."""
        return await self.request("POST", url, **kwargs)
        
    async def put(self, url: str, **kwargs) -> Response:
        """Make a PUT request."""
        return await self.request("PUT", url, **kwargs)
        
    async def patch(self, url: str, **kwargs) -> Response:
        """Make a PATCH request."""
        return await self.request("PATCH", url, **kwargs)
        
    async def delete(self, url: str, **kwargs) -> Response:
        """Make a DELETE request."""
        return await self.request("DELETE", url, **kwargs)
