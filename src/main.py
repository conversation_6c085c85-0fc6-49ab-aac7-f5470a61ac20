"""
Main entry point for Sage Intacct MCP Server.

This module provides the command-line interface and server initialization.
"""

import argparse
import logging
import os
import sys
from pathlib import Path
from typing import Optional, List

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.servers.composite import create_composite_server
from src.auth.interfaces import AuthConfig
from src.utils.config_manager import ConfigManager


def setup_logging(log_level: str = "INFO") -> None:
    """Setup logging configuration."""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stderr),  # Changed to stderr to avoid stdout pollution
            logging.FileHandler('intacct-mcp.log', mode='a')
        ]
    )


def parse_arguments() -> argparse.Namespace:
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(
        description="Sage Intacct MCP Server",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run with all modules using config file
  python -m src.main --config config/server_config.yaml
  
  # Run with specific modules
  python -m src.main --modules ap ar --config config/server_config.yaml
  
  # Run in development mode with debug logging
  python -m src.main --mode development --log-level DEBUG
  
  # Run with environment-based auth  python -m src.main --auth-mode env
  
  # Run with HTTP transport (for non-Claude Desktop usage)
  python -m src.main --transport http --port 8080
"""
    )
    
    # Configuration file
    parser.add_argument(
        '--config', '-c',
        type=str,
        default='config/server_config.yaml',
        help='Path to configuration file'
    )
    
    parser.add_argument(
        '--modules', '-m',
        nargs='+',
        choices=['ap', 'ar', 'gl'],
        help='Modules to enable (default: all)'
    )
    
    parser.add_argument(
        '--mode',
        choices=['development', 'production', 'debug'],
        default='production',
        help='Server mode (default: production)'
    )
    
    parser.add_argument(
        '--transport',
        choices=['stdio', 'streamable-http', 'sse'],
        default='stdio',
        help='Transport mode (default: stdio for Claude Desktop)'
    )
    
    # Authentication options
    parser.add_argument(
        '--auth-mode',
        choices=['config', 'env', 'interactive'],
        default='config',
        help='Authentication mode (default: config)'
    )
    
    parser.add_argument(
        '--client-id',
        help='OAuth client ID (overrides config)'
    )
    
    parser.add_argument(        '--client-secret',
        help='OAuth client secret (overrides config)'
    )
    
    # Server options
    parser.add_argument(
        '--host',
        default='localhost',
        help='Server host (default: localhost)'
    )
    
    parser.add_argument(
        '--port',
        type=int,
        help='Server port (auto-assigned if not specified)'
    )
    
    # Logging
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='Logging level (default: INFO)'
    )
    
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Validate configuration without starting server'
    )
    
    return parser.parse_args()


def load_auth_config(args: argparse.Namespace, config_manager: Optional[ConfigManager]) -> Optional[AuthConfig]:
    """
    Load authentication configuration from various sources.
    
    Args:
        args: Command-line arguments
        config_manager: Configuration manager instance
        
    Returns:
        AuthConfig instance or None
    """
    # Command-line overrides everything
    if args.client_id and args.client_secret:
        return AuthConfig(            client_id=args.client_id,
            client_secret=args.client_secret,
            redirect_uri=os.getenv('INTACCT_REDIRECT_URI', 'http://localhost:8080/callback'),
            authorization_url=os.getenv('INTACCT_AUTHORIZATION_URL', 'https://api.intacct.com/ia/oauth2/authorize'),
            token_url=os.getenv('INTACCT_TOKEN_URL', 'https://api.intacct.com/ia/oauth2/token'),
            scope='full'
        )
    
    # Environment variables
    if args.auth_mode == 'env':
        client_id = os.getenv('INTACCT_CLIENT_ID')
        client_secret = os.getenv('INTACCT_CLIENT_SECRET')
        
        if client_id and client_secret:
            return AuthConfig(
                client_id=client_id,
                client_secret=client_secret,
                redirect_uri=os.getenv('INTACCT_REDIRECT_URI', 'http://localhost:8080/callback'),
                authorization_url=os.getenv('INTACCT_AUTHORIZATION_URL', 'https://api.intacct.com/ia/oauth2/authorize'),
                token_url=os.getenv('INTACCT_TOKEN_URL', 'https://api.intacct.com/ia/oauth2/token'),
                scope='full'
            )
    
    # Configuration file
    if config_manager and args.auth_mode == 'config':
        auth_config = config_manager.get('authentication')
        if auth_config:
            return AuthConfig(**auth_config)
    
    return None


def main() -> None:
    """Main entry point."""
    args = parse_arguments()
    
    # Setup logging first - to stderr
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)
    
    logger.info("Starting Sage Intacct MCP Server")
    logger.info(f"Mode: {args.mode}")
    logger.info(f"Transport: {args.transport}")
    
    # Load configuration
    config_path = args.config
    config_manager = None    
    if Path(config_path).exists():
        # Try to load configuration file
        from dotenv import load_dotenv
        load_dotenv()  # Load .env file
        
        try:
            config_manager = ConfigManager(config_path)
            logger.info(f"Loaded configuration from {config_path}")
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            sys.exit(1)
    
    # Load authentication configuration
    auth_config = load_auth_config(args, config_manager)
    if not auth_config and args.mode != 'development':
        logger.error("No authentication configuration provided. Use --auth-mode or provide config file.")
        sys.exit(1)
    
    # Validate configuration in dry-run mode
    if args.dry_run:
        logger.info("Dry run mode - validating configuration")
        if config_manager:
            if config_manager.validate():
                logger.info("Configuration is valid")
            else:
                logger.error("Configuration validation failed")
                sys.exit(1)
        logger.info("Dry run complete")
        return
    
    # Create server
    try:
        server = create_composite_server(
            config_path=args.config,
            enabled_modules=args.modules
        )
        
        # Override auth config if provided
        if auth_config:
            server.auth_config = auth_config
        
        # Configure based on mode
        if args.mode == 'development':
            logger.info("Running in development mode")
            os.environ['FASTMCP_DEBUG'] = '1'
        elif args.mode == 'debug':
            logger.info("Running in debug mode with verbose logging")
            logging.getLogger().setLevel(logging.DEBUG)
        
        # Start server
        logger.info(f"Starting server on {args.host}:{args.port or 'auto'}")
        
        # Run server with correct transport
        # For stdio transport (Claude Desktop), don't pass host/port
        if args.transport == 'stdio':
            logger.info("Running in stdio mode for Claude Desktop")
            server.run_server()
        else:
            # For HTTP transport
            server.run_server(
                host=args.host,
                port=args.port,
                transport=args.transport
            )
        
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.exception(f"Server error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
