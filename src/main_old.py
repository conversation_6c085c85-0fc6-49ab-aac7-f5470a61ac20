"""
Main entry point for Sage Intacct MCP Server.

This module provides the command-line interface and server initialization.
"""

import argparse
import logging
import os
import sys
from pathlib import Path
from typing import Optional, List

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.servers.composite import create_composite_server
from src.auth.interfaces import AuthConfig
from src.utils.config_manager import ConfigManager


def setup_logging(log_level: str = "INFO") -> None:
    """Setup logging configuration."""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('intacct-mcp.log', mode='a')
        ]
    )


def parse_arguments() -> argparse.Namespace:
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(
        description="Sage Intacct MCP Server",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run with all modules using config file
  python -m src.main --config config/server_config.yaml
  
  # Run with specific modules
  python -m src.main --modules ap ar --config config/server_config.yaml
  
  # Run in development mode with debug logging
  python -m src.main --mode development --log-level DEBUG
  
  # Run with environment-based auth
  python -m src.main --auth-mode env
        """
    )    
    # Server configuration
    parser.add_argument(
        '--config', '-c',
        type=str,
        help='Path to configuration file'
    )
    
    parser.add_argument(
        '--modules', '-m',
        nargs='+',
        choices=['ap', 'ar', 'gl'],
        help='Modules to enable (default: all)'
    )
    
    parser.add_argument(
        '--mode',
        choices=['development', 'production', 'debug'],
        default='production',
        help='Server mode (default: production)'
    )
    
    # Authentication options
    parser.add_argument(
        '--auth-mode',
        choices=['config', 'env', 'interactive'],
        default='config',
        help='Authentication mode (default: config)'
    )
    
    parser.add_argument(
        '--client-id',
        help='OAuth client ID (overrides config)'
    )
    
    parser.add_argument(
        '--client-secret',
        help='OAuth client secret (overrides config)'
    )
    
    # Server options
    parser.add_argument(
        '--host',
        default='localhost',
        help='Server host (default: localhost)'
    )
    
    parser.add_argument(
        '--port',
        type=int,
        help='Server port (auto-assigned if not specified)'
    )    
    # Logging
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='Logging level (default: INFO)'
    )
    
    # Other options
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Validate configuration without starting server'
    )
    
    return parser.parse_args()


def load_auth_config(args: argparse.Namespace, config_manager: Optional[ConfigManager]) -> Optional[AuthConfig]:
    """
    Load authentication configuration from various sources.
    
    Args:
        args: Command-line arguments
        config_manager: Configuration manager instance
        
    Returns:
        AuthConfig instance or None
    """
    auth_config_dict = {}
    
    # Load from config file first
    if config_manager:
        auth_config_dict = config_manager.get("authentication", {})
    
    # Override with environment variables
    if args.auth_mode in ['env', 'interactive']:
        env_mapping = {
            'client_id': 'INTACCT_CLIENT_ID',
            'client_secret': 'INTACCT_CLIENT_SECRET',
            'redirect_uri': 'INTACCT_REDIRECT_URI',
            'scope': 'INTACCT_SCOPE',
            'authorization_url': 'INTACCT_AUTHORIZE_URL',
            'token_url': 'INTACCT_TOKEN_URL'
        }        
        for key, env_var in env_mapping.items():
            value = os.getenv(env_var)
            if value:
                auth_config_dict[key] = value
    
    # Override with command-line arguments
    if args.client_id:
        auth_config_dict['client_id'] = args.client_id
    if args.client_secret:
        auth_config_dict['client_secret'] = args.client_secret
    
    # Interactive mode
    if args.auth_mode == 'interactive' and not auth_config_dict.get('client_id'):
        print("\n=== Interactive Authentication Setup ===")
        auth_config_dict['client_id'] = input("Client ID: ").strip()
        auth_config_dict['client_secret'] = input("Client Secret: ").strip()
        
        redirect_uri = input("Redirect URI (default: http://localhost:8080/callback): ").strip()
        if redirect_uri:
            auth_config_dict['redirect_uri'] = redirect_uri
    
    # Create AuthConfig if we have minimum required fields
    if auth_config_dict.get('client_id') and auth_config_dict.get('client_secret'):
        # Set defaults for optional fields
        auth_config_dict.setdefault('redirect_uri', 'http://localhost:8080/callback')
        auth_config_dict.setdefault('scope', 'full')
        auth_config_dict.setdefault('authorization_url', 'https://secure.intacct.com/ia/webapp/authz/oauth2/v1/authorize')
        auth_config_dict.setdefault('token_url', 'https://secure.intacct.com/ia/webapp/authz/oauth2/v1/token')
        
        return AuthConfig(**auth_config_dict)
    
    return None

def main():
    """Main application entry point."""
    args = parse_arguments()
    
    # Setup logging
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)
    
    logger.info("Starting Sage Intacct MCP Server")
    logger.info(f"Mode: {args.mode}")
    
    # Load configuration
    config_manager = None
    if args.config:
        config_path = Path(args.config)
        if not config_path.exists():
            logger.error(f"Configuration file not found: {config_path}")
            sys.exit(1)
        
        try:
            config_manager = ConfigManager(config_path)
            logger.info(f"Loaded configuration from {config_path}")
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            sys.exit(1)
    
    # Load authentication configuration
    auth_config = load_auth_config(args, config_manager)
    if not auth_config and args.mode != 'development':
        logger.error("No authentication configuration provided. Use --auth-mode or provide config file.")
        sys.exit(1)
    
    # Validate configuration in dry-run mode
    if args.dry_run:
        logger.info("Dry run mode - validating configuration")
        if config_manager:
            if config_manager.validate():
                logger.info("Configuration is valid")
            else:
                logger.error("Configuration validation failed")
                sys.exit(1)
        logger.info("Dry run complete")
        return    
    # Create server
    try:
        server = create_composite_server(
            config_path=args.config,
            enabled_modules=args.modules
        )
        
        # Override auth config if provided
        if auth_config:
            server.auth_config = auth_config
        
        # Configure based on mode
        if args.mode == 'development':
            logger.info("Running in development mode")
            os.environ['FASTMCP_DEBUG'] = '1'
        elif args.mode == 'debug':
            logger.info("Running in debug mode with verbose logging")
            logging.getLogger().setLevel(logging.DEBUG)
        
        # Start server
        logger.info(f"Starting server on {args.host}:{args.port or 'auto'}")
        
        # Run server
        server.run_server(
            host=args.host,
            port=args.port,
            transport=args.mode  # Use mode to determine transport
        )
        
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.exception(f"Server error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()