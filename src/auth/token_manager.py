"""
Token management system for Sage Intacct authentication.

This module provides the TokenManager class that handles token lifecycle,
including storage, retrieval, and automatic refresh.
"""

import asyncio
from typing import Optional, Dict, Any, Union, Callable
from datetime import datetime, timedelta
import logging

from .interfaces import (
    AuthFlow,
    AuthConfig,
    TokenResponse,
    TokenStorage,
    TokenType,
)
from .exceptions import (
    TokenExpiredError,
    TokenRefreshError,
    StorageError,
    InvalidConfigError,
)
from .models import Token, AuthMethod
from .storage import FileTokenStorage, InMemoryTokenStorage
from .flows import AuthorizationCodeFlow, ClientCredentialsFlow


logger = logging.getLogger(__name__)


class TokenManager:
    """
    Manages OAuth tokens with automatic refresh and multi-entity support.
    """
    
    def __init__(
        self,
        storage: Optional[TokenStorage] = None,
        refresh_buffer_seconds: int = 300
    ):
        """Initialize the token manager."""
        self.storage = storage or FileTokenStorage()
        self.refresh_buffer_seconds = refresh_buffer_seconds
        self._flows: Dict[str, AuthFlow] = {}
        self._configs: Dict[str, AuthConfig] = {}
        self._refresh_tasks: Dict[str, asyncio.Task] = {}
    
    def _get_token_key(self, config: AuthConfig, entity_id: Optional[str] = None) -> str:
        """
        Generate a unique key for token storage.
        
        Args:
            config: Authentication configuration
            entity_id: Optional entity ID for multi-entity support
            
        Returns:
            Unique token key
        """
        parts = [config.client_id]
        if config.company_id:
            parts.append(config.company_id)
        if entity_id:
            parts.append(entity_id)
        elif config.entities and len(config.entities) > 0:
            parts.append(config.entities[0].entity_id)
        return ":".join(parts)
    
    async def get_token(
        self,
        config: AuthConfig,
        flow: Optional[AuthFlow] = None,
        entity_id: Optional[str] = None,
        force_refresh: bool = False
    ) -> TokenResponse:
        """
        Get a valid access token, refreshing if necessary.
        
        Args:
            config: Authentication configuration
            flow: Authentication flow to use (auto-detected if not provided)
            entity_id: Optional entity ID for multi-entity support
            force_refresh: Force token refresh even if not expired
            
        Returns:
            Valid TokenResponse
            
        Raises:
            TokenExpiredError: If token is expired and refresh fails
            InvalidConfigError: If configuration is invalid
        """
        key = self._get_token_key(config, entity_id)
        
        # Try to get existing token
        if not force_refresh:
            token = await self.storage.get_token(key)
            if token and self._is_token_valid(token):
                return token
        
        # Determine flow if not provided
        if not flow:
            flow = self._determine_flow(config)
        
        # Store flow and config for later use
        self._flows[key] = flow
        self._configs[key] = config
        
        # Authenticate or refresh
        if token and token.refresh_token:
            # Try to refresh existing token
            try:
                new_token = await self._refresh_token(key, token, flow)
                return new_token
            except TokenRefreshError:
                # Fall back to re-authentication
                pass
        
        # Perform fresh authentication
        if isinstance(flow, ClientCredentialsFlow):
            token = await flow.authenticate(config)
        else:
            raise InvalidConfigError(
                "Authorization Code Flow requires user interaction. "
                "Use the flow directly for initial authentication."
            )
        
        # Save token
        await self.storage.save_token(key, token)
        
        # Schedule automatic refresh if applicable
        if token.expires_in:
            await self._schedule_refresh(key, token)
        
        return token
    
    def _is_token_valid(self, token: TokenResponse) -> bool:
        """Check if token is still valid with buffer."""
        if not token.expires_at:
            return True  # No expiry, assume valid
        
        buffer = timedelta(seconds=self.refresh_buffer_seconds)
        return datetime.utcnow() < (token.expires_at - buffer)
    
    def is_token_valid(self, token: Token) -> bool:
        """
        Public method to check if token is valid.
        
        Args:
            token: Token to validate
            
        Returns:
            True if token is valid, False otherwise
        """
        # Convert Token to TokenResponse if needed
        if isinstance(token, Token):
            token_response = TokenResponse(
                access_token=token.access_token,
                token_type=token.token_type.value if isinstance(token.token_type, TokenType) else token.token_type,
                expires_in=token.expires_in,
                refresh_token=token.refresh_token,
                scope=token.scope,
                issued_at=token.issued_at
            )
            return self._is_token_valid(token_response)
        return self._is_token_valid(token)
    
    def _determine_flow(self, config: AuthConfig) -> AuthFlow:
        """Determine appropriate flow based on configuration."""
        if config.redirect_uri:
            return AuthorizationCodeFlow()
        else:
            return ClientCredentialsFlow()
    
    async def _refresh_token(
        self,
        key: str,
        token: TokenResponse,
        flow: AuthFlow
    ) -> TokenResponse:
        """Refresh an expired or expiring token."""
        if not token.refresh_token:
            raise TokenRefreshError("No refresh token available")
        
        try:
            new_token = await flow.refresh_token(token.refresh_token)
            await self.storage.save_token(key, new_token)
            
            # Reschedule refresh
            if new_token.expires_in:
                await self._schedule_refresh(key, new_token)
            
            return new_token
        except Exception as e:
            raise TokenRefreshError(f"Token refresh failed: {str(e)}")
    
    async def _schedule_refresh(self, key: str, token: TokenResponse) -> None:
        """Schedule automatic token refresh."""
        # Cancel existing refresh task
        if key in self._refresh_tasks:
            self._refresh_tasks[key].cancel()
        
        # Calculate when to refresh
        if token.expires_in:
            delay = max(
                token.expires_in - self.refresh_buffer_seconds,
                60  # Minimum 1 minute
            )
            
            # Schedule refresh task
            task = asyncio.create_task(
                self._auto_refresh(key, delay)
            )
            self._refresh_tasks[key] = task
    
    async def _auto_refresh(self, key: str, delay: float) -> None:
        """Automatically refresh token after delay."""
        try:
            await asyncio.sleep(delay)
            
            # Get current token and refresh
            token = await self.storage.get_token(key)
            if token and key in self._flows:
                flow = self._flows[key]
                await self._refresh_token(key, token, flow)
                logger.info(f"Successfully auto-refreshed token for {key}")
        except asyncio.CancelledError:
            raise
        except Exception as e:
            logger.error(f"Auto-refresh failed for {key}: {str(e)}")
    
    async def revoke_token(self, config: AuthConfig, entity_id: Optional[str] = None) -> None:
        """
        Revoke and remove a token.
        
        Args:
            config: Authentication configuration
            entity_id: Optional entity ID
        """
        key = self._get_token_key(config, entity_id)
        
        # Cancel refresh task
        if key in self._refresh_tasks:
            self._refresh_tasks[key].cancel()
            del self._refresh_tasks[key]
        
        # Remove from storage
        await self.storage.delete_token(key)
        
        # Clean up flow and config
        self._flows.pop(key, None)
        self._configs.pop(key, None)
    
    async def get_all_tokens(self) -> Dict[str, TokenResponse]:
        """Get all stored tokens."""
        tokens = {}
        for key in await self.storage.list_tokens():
            token = await self.storage.get_token(key)
            if token:
                tokens[key] = token
        return tokens
    
    async def cleanup_expired(self) -> int:
        """Remove expired tokens from storage."""
        removed = 0
        for key in await self.storage.list_tokens():
            token = await self.storage.get_token(key)
            if token and not self._is_token_valid(token) and not token.refresh_token:
                await self.storage.delete_token(key)
                removed += 1
        return removed
