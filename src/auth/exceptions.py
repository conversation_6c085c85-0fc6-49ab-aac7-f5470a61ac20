"""
Custom exception classes for Sage Intacct authentication errors.

This module provides specific exception types for different authentication
failure scenarios, making error handling more precise and informative.
"""

from typing import Optional, Dict, Any


class IntacctAuthError(Exception):
    """Base exception for all Intacct authentication errors."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.details = details or {}
    
    def __str__(self) -> str:
        if self.details:
            return f"{self.message} - Details: {self.details}"
        return self.message


class InvalidCredentialsError(IntacctAuthError):
    """Raised when authentication credentials are invalid."""
    
    def __init__(self, message: str = "Invalid credentials provided"):
        super().__init__(message)


class TokenExpiredError(IntacctAuthError):
    """Raised when an access token has expired."""
    
    def __init__(self, message: str = "Access token has expired"):
        super().__init__(message)


class TokenRefreshError(IntacctAuthError):
    """Raised when token refresh fails."""
    
    def __init__(self, message: str = "Failed to refresh access token", 
                 details: Optional[Dict[str, Any]] = None):
        super().__init__(message, details)


class AuthorizationError(IntacctAuthError):
    """Raised when authorization fails."""
    
    def __init__(self, message: str = "Authorization failed", 
                 error_code: Optional[str] = None,
                 error_description: Optional[str] = None):
        details = {}
        if error_code:
            details["error_code"] = error_code
        if error_description:
            details["error_description"] = error_description
        super().__init__(message, details)


class InvalidConfigError(IntacctAuthError):
    """Raised when authentication configuration is invalid."""
    
    def __init__(self, message: str = "Invalid authentication configuration",
                 missing_fields: Optional[list] = None):
        details = {}
        if missing_fields:
            details["missing_fields"] = missing_fields
        super().__init__(message, details)


class StorageError(IntacctAuthError):
    """Raised when token storage operations fail."""
    
    def __init__(self, message: str = "Token storage operation failed",
                 operation: Optional[str] = None):
        details = {}
        if operation:
            details["operation"] = operation
        super().__init__(message, details)


class RateLimitError(IntacctAuthError):
    """Raised when API rate limits are exceeded."""
    
    def __init__(self, message: str = "Rate limit exceeded",
                 retry_after: Optional[int] = None):
        details = {}
        if retry_after:
            details["retry_after_seconds"] = retry_after
        super().__init__(message, details)
