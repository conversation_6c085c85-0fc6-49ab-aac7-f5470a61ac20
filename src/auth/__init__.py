"""
Sage Intacct Authentication Module

This module provides OAuth 2.0 authentication for Sage Intacct integration,
supporting both Authorization Code Flow and Client Credentials Flow.
"""

from .interfaces import (
    AuthFlow,
    TokenStorage,
    TokenResponse,
    AuthConfig,
    AuthorizationRequest,
    TokenRequest,
    TokenType,
    AuthMethod,
    EntityScope,
)
from .exceptions import (
    IntacctAuthError,
    InvalidCredentialsError,
    TokenExpiredError,
    TokenRefreshError,
    AuthorizationError,
    InvalidConfigError,
    StorageError,
    RateLimitError,
)
from .models import (
    Token,
)
from .flows import (
    AuthorizationCodeFlow,
    ClientCredentialsFlow,
)
from .storage import (
    FileTokenStorage,
    InMemoryTokenStorage,
)
from .token_manager import (
    TokenManager,
)

__all__ = [
    # Interfaces
    "AuthFlow",
    "TokenStorage",
    "TokenResponse",
    "AuthConfig",
    "AuthorizationRequest",
    "TokenRequest",
    "TokenType",
    "AuthMethod",
    "EntityScope",
    # Exceptions
    "IntacctAuthError",
    "InvalidCredentialsError",
    "TokenExpiredError",
    "TokenRefreshError",
    "AuthorizationError",
    "InvalidConfigError",
    "StorageError",
    "RateLimitError",
    # Models
    "Token",
    # Flows
    "AuthorizationCodeFlow",
    "ClientCredentialsFlow",
    # Storage
    "FileTokenStorage",
    "InMemoryTokenStorage",
    # Manager
    "TokenManager",
]
