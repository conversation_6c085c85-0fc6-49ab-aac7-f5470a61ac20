"""
Authenticated HTTP client manager for Sage Intacct API.

Provides HTTP clients with automatic authentication, token refresh,
and retry logic.
"""

import logging
from typing import Optional, Dict, Any
from datetime import datetime, timedelta

import httpx
from httpx import Auth, Request, Response

from ..flows import AuthorizationCode<PERSON>low, ClientCredentialsFlow
from ..models import Token
from ..token_manager import TokenManager
from ..exceptions import IntacctAuthError, TokenRefreshError
from .interceptors import AuthInterceptor, RetryInterceptor

logger = logging.getLogger(__name__)


class IntacctAuthManager:
    """
    Manages authenticated HTTP clients for Sage Intacct API access.
    
    Handles:
    - Creating HTTP clients with authentication
    - Automatic token refresh
    - Request/response interceptors
    - Retry logic for transient failures
    """
    
    def __init__(
        self,
        token_manager: TokenManager,
        auth_flow: Optional[AuthorizationCodeFlow | ClientCredentialsFlow] = None,
        base_url: str = "https://api.intacct.com",
        timeout: float = 30.0,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        custom_headers: Optional[Dict[str, str]] = None
    ):
        """
        Initialize the authenticated HTTP client manager.
        
        Args:
            token_manager: Token manager for storing/retrieving tokens
            auth_flow: Optional auth flow for token refresh
            base_url: Base URL for Intacct API
            timeout: Request timeout in seconds
            max_retries: Maximum number of retry attempts
            retry_delay: Initial delay between retries (exponential backoff)
            custom_headers: Additional headers to include in requests
        """
        self.token_manager = token_manager
        self.auth_flow = auth_flow
        self.base_url = base_url.rstrip("/")
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.custom_headers = custom_headers or {}
        
        # Track active tokens and their expiry
        self._active_tokens: Dict[str, Token] = {}
        
    def get_authenticated_client(
        self,
        entity_id: Optional[str] = None,
        force_refresh: bool = False
    ) -> httpx.AsyncClient:
        """
        Get an authenticated HTTP client for the specified entity.
        
        Args:
            entity_id: Entity ID for multi-entity authentication
            force_refresh: Force token refresh even if not expired
            
        Returns:
            Configured httpx.AsyncClient with authentication
            
        Raises:
            IntacctAuthError: If authentication fails
            TokenRefreshError: If token refresh fails
        """
        # Get or refresh token
        token = self._get_valid_token(entity_id, force_refresh)
        
        # Create base headers
        headers = {
            "User-Agent": "Sage-Intacct-MCP-Server/1.0",
            "Accept": "application/json",
            "Content-Type": "application/json",
            **self.custom_headers
        }
        
        # Create auth interceptor
        auth_interceptor = AuthInterceptor(
            token=token,
            token_manager=self.token_manager,
            auth_flow=self.auth_flow,
            entity_id=entity_id
        )
        
        # Create retry interceptor
        retry_interceptor = RetryInterceptor(
            max_retries=self.max_retries,
            retry_delay=self.retry_delay,
            retry_on_401=True  # Retry after token refresh
        )
        
        # Create client with interceptors
        client = httpx.AsyncClient(
            base_url=self.base_url,
            headers=headers,
            timeout=httpx.Timeout(self.timeout),
            auth=auth_interceptor,
            event_hooks={
                "response": [retry_interceptor.on_response]
            }
        )
        
        return client
        
    def _get_valid_token(
        self,
        entity_id: Optional[str] = None,
        force_refresh: bool = False
    ) -> Token:
        """
        Get a valid token, refreshing if necessary.
        
        Args:
            entity_id: Entity ID for multi-entity authentication
            force_refresh: Force token refresh
            
        Returns:
            Valid Token
            
        Raises:
            IntacctAuthError: If no valid token found
            TokenRefreshError: If refresh fails
        """
        cache_key = entity_id or "default"
        
        # Check in-memory cache first
        if not force_refresh and cache_key in self._active_tokens:
            token = self._active_tokens[cache_key]
            if self._is_token_valid(token):
                return token
                
        # Try to load from storage
        try:
            token = self.token_manager.get_token(entity_id)
            if token and not force_refresh and self._is_token_valid(token):
                self._active_tokens[cache_key] = token
                return token
        except Exception as e:
            logger.warning(f"Failed to load token from storage: {e}")
            
        # Token needs refresh
        if token and token.refresh_token:
            return self._refresh_token(token, entity_id)
        elif self.auth_flow and isinstance(self.auth_flow, ClientCredentialsFlow):
            # Client credentials can get new token directly
            return self._get_new_client_credentials_token(entity_id)
        else:
            raise IntacctAuthError(
                "No valid token found and no refresh mechanism available. "
                "Please re-authenticate."
            )
            
    def _is_token_valid(self, token: Token, buffer_minutes: int = 5) -> bool:
        """
        Check if token is still valid with a buffer.
        
        Args:
            token: Token to check
            buffer_minutes: Minutes before expiry to consider invalid
            
        Returns:
            True if token is valid
        """
        if not token.expires_at:
            # No expiry, assume valid
            return True
            
        # Add buffer to current time
        buffer_time = datetime.utcnow() + timedelta(minutes=buffer_minutes)
        return token.expires_at > buffer_time
        
    def _refresh_token(
        self,
        token: Token,
        entity_id: Optional[str] = None
    ) -> Token:
        """
        Refresh an expired token.
        
        Args:
            token: Token to refresh
            entity_id: Entity ID for multi-entity authentication
            
        Returns:
            New Token
            
        Raises:
            TokenRefreshError: If refresh fails
        """
        if not token.refresh_token:
            raise TokenRefreshError("No refresh token available")
            
        if not self.auth_flow:
            raise TokenRefreshError("No auth flow configured for refresh")
            
        try:
            logger.info(f"Refreshing token for entity: {entity_id or 'default'}")
            
            # Use the auth flow to refresh
            if hasattr(self.auth_flow, 'refresh_token'):
                new_token = self.auth_flow.refresh_token(token.refresh_token)
            else:
                raise TokenRefreshError("Auth flow does not support token refresh")
                
            # Save new token
            self.token_manager.save_token(new_token, entity_id)
            
            # Update cache
            cache_key = entity_id or "default"
            self._active_tokens[cache_key] = new_token
            
            return new_token
            
        except Exception as e:
            logger.error(f"Token refresh failed: {e}")
            raise TokenRefreshError(f"Failed to refresh token: {str(e)}")
            
    def _get_new_client_credentials_token(
        self,
        entity_id: Optional[str] = None
    ) -> Token:
        """
        Get a new token using client credentials flow.
        
        Args:
            entity_id: Entity ID for multi-entity authentication
            
        Returns:
            New Token
            
        Raises:
            IntacctAuthError: If authentication fails
        """
        if not isinstance(self.auth_flow, ClientCredentialsFlow):
            raise IntacctAuthError("Client credentials flow not configured")
            
        try:
            logger.info(f"Getting new token for entity: {entity_id or 'default'}")
            
            # Get new token
            token = self.auth_flow.get_token(entity_id=entity_id)
            
            # Save token
            self.token_manager.save_token(token, entity_id)
            
            # Update cache
            cache_key = entity_id or "default"
            self._active_tokens[cache_key] = token
            
            return token
            
        except Exception as e:
            logger.error(f"Failed to get new token: {e}")
            raise IntacctAuthError(f"Failed to get new token: {str(e)}")
            
    def clear_token_cache(self, entity_id: Optional[str] = None):
        """
        Clear cached tokens.
        
        Args:
            entity_id: Clear specific entity token or all if None
        """
        if entity_id:
            cache_key = entity_id or "default"
            self._active_tokens.pop(cache_key, None)
        else:
            self._active_tokens.clear()
            
    async def close_all_clients(self):
        """
        Close all active HTTP clients.
        
        Should be called during shutdown.
        """
        # Clear token cache
        self._active_tokens.clear()
        
        # Note: Individual clients should be closed by their users
        logger.info("Auth manager cleaned up")
