"""
HTTP request/response interceptors for authentication and retry logic.
"""

import logging
import asyncio
from typing import Optional, Callable, Any, Generator
from datetime import datetime

import httpx
from httpx import Request, Response, Auth

from ..models import Token
from ..token_manager import TokenManager
from ..exceptions import TokenRefreshError

logger = logging.getLogger(__name__)


class AuthInterceptor(Auth):
    """
    HTTPX Auth handler that adds authentication headers and handles token refresh.
    
    Implements the httpx.Auth interface for automatic authentication.
    """
    
    def __init__(
        self,
        token: Token,
        token_manager: Optional[TokenManager] = None,
        auth_flow: Optional[Any] = None,
        entity_id: Optional[str] = None
    ):
        """
        Initialize the auth interceptor.
        
        Args:
            token: Initial token response
            token_manager: Optional token manager for updates
            auth_flow: Optional auth flow for refresh
            entity_id: Entity ID for multi-entity auth
        """
        self.token = token
        self.token_manager = token_manager
        self.auth_flow = auth_flow
        self.entity_id = entity_id
        self._refresh_lock = asyncio.Lock()
        
    def auth_flow(self, request: Request) -> Generator[Request, Response, None]:
        """
        HTTPX auth flow implementation.
        
        Adds authorization header and handles 401 responses.
        """
        # Add authorization header
        request.headers["Authorization"] = f"Bearer {self.token.access_token}"
        
        # Send the request
        response = yield request
        
        # Handle 401 Unauthorized
        if response.status_code == 401 and self._can_refresh():
            # Try to refresh token
            if self._try_refresh_sync():
                # Update the request with new token
                request.headers["Authorization"] = f"Bearer {self.token.access_token}"
                # Retry the request
                response = yield request
                
        # Return the response
        return response
        
    def _can_refresh(self) -> bool:
        """Check if token can be refreshed."""
        return bool(
            self.token.refresh_token and 
            self.auth_flow and 
            hasattr(self.auth_flow, 'refresh_token')
        )
        
    def _try_refresh_sync(self) -> bool:
        """
        Try to refresh the token synchronously.
        
        Returns:
            True if refresh succeeded
        """
        try:
            # Refresh token
            new_token = self.auth_flow.refresh_token(self.token.refresh_token)
            
            # Update stored token
            if self.token_manager:
                self.token_manager.save_token(new_token, self.entity_id)
                
            # Update current token
            self.token = new_token
            
            logger.info("Token refreshed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Token refresh failed: {e}")
            return False



class RetryInterceptor:
    """
    HTTP response interceptor that implements retry logic with exponential backoff.
    """
    
    def __init__(
        self,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        retry_on_401: bool = True,
        retry_status_codes: Optional[set] = None
    ):
        """
        Initialize the retry interceptor.
        
        Args:
            max_retries: Maximum number of retry attempts
            retry_delay: Initial delay between retries
            retry_on_401: Whether to retry on 401 after auth refresh
            retry_status_codes: Additional status codes to retry
        """
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.retry_on_401 = retry_on_401
        
        # Default retryable status codes
        self.retry_status_codes = retry_status_codes or {502, 503, 504}
        if retry_on_401:
            self.retry_status_codes.add(401)
            
        self._retry_counts = {}
            
    async def on_response(self, response: Response):
        """
        Handle response and implement retry logic.
        
        This is called as an httpx event hook.
        """
        request = response.request
        request_id = id(request)
        
        # Check if we should retry
        if response.status_code in self.retry_status_codes:
            retry_count = self._retry_counts.get(request_id, 0)
            
            if retry_count < self.max_retries:
                # Calculate delay with exponential backoff
                delay = self.retry_delay * (2 ** retry_count)
                
                logger.warning(
                    f"Request failed with status {response.status_code}. "
                    f"Retrying in {delay}s (attempt {retry_count + 1}/{self.max_retries})"
                )
                
                # Update retry count
                self._retry_counts[request_id] = retry_count + 1
                
                # Wait before retry
                await asyncio.sleep(delay)
                
                # Note: Actual retry is handled by the auth interceptor
                # or by the calling code
            else:
                # Max retries exceeded, clean up
                self._retry_counts.pop(request_id, None)
                logger.error(
                    f"Max retries ({self.max_retries}) exceeded for request"
                )
        else:
            # Success or non-retryable error, clean up
            self._retry_counts.pop(request_id, None)
