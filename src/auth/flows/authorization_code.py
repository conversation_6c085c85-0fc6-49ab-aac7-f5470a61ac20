"""
OAuth 2.0 Authorization Code Flow implementation for Sage Intacct.

This module implements the Authorization Code Flow with PKCE support,
which is the recommended flow for web applications and desktop clients.
"""

import base64
import hashlib
import secrets
import urllib.parse
from typing import Optional, Dict, Any, Tuple
from datetime import datetime
import httpx

from ..interfaces import (
    AuthFlow,
    AuthConfig,
    TokenResponse,
    AuthorizationRequest,
    TokenRequest,
)
from ..exceptions import (
    AuthorizationError,
    InvalidConfigError,
    TokenRefreshError,
    InvalidCredentialsError,
)
from ..models import Token, TokenType


class AuthorizationCodeFlow(AuthFlow):
    """
    Implements OAuth 2.0 Authorization Code Flow with PKCE for Sage Intacct.
    
    This flow is suitable for:
    - Web applications with a backend
    - Desktop applications
    - Mobile applications
    """
    
    # Sage Intacct OAuth endpoints
    AUTHORIZE_URL = "https://api.intacct.com/ia/api/v1/oauth2/authorize"
    TOKEN_URL = "https://api.intacct.com/ia/api/v1/oauth2/token"
    
    def __init__(self, http_client: Optional[httpx.AsyncClient] = None):
        """Initialize the Authorization Code Flow."""
        self.http_client = http_client or httpx.AsyncClient()
        self._pending_requests: Dict[str, Dict[str, Any]] = {}

    
    def _generate_pkce_pair(self) -> Tuple[str, str, str]:
        """
        Generate PKCE code verifier and challenge.
        
        Returns:
            Tuple of (code_verifier, code_challenge, challenge_method)
        """
        # Generate code verifier (43-128 characters)
        code_verifier = base64.urlsafe_b64encode(
            secrets.token_bytes(32)
        ).decode('utf-8').rstrip('=')
        
        # Generate code challenge using SHA256
        code_challenge = base64.urlsafe_b64encode(
            hashlib.sha256(code_verifier.encode('utf-8')).digest()
        ).decode('utf-8').rstrip('=')
        
        return code_verifier, code_challenge, 'S256'
    
    def _generate_state(self) -> str:
        """Generate a secure random state parameter."""
        return base64.urlsafe_b64encode(
            secrets.token_bytes(32)
        ).decode('utf-8').rstrip('=')
    
    def get_auth_url(self, config: AuthConfig) -> AuthorizationRequest:
        """
        Generate the authorization URL for user consent.
        
        Args:
            config: Authentication configuration
            
        Returns:
            AuthorizationRequest with URL and PKCE parameters
            
        Raises:
            InvalidConfigError: If required configuration is missing
        """
        if not config.client_id:
            raise InvalidConfigError("client_id is required")
        if not config.redirect_uri:
            raise InvalidConfigError("redirect_uri is required")
        
        # Generate PKCE parameters
        code_verifier, code_challenge, challenge_method = self._generate_pkce_pair()
        state = self._generate_state()
        
        # Build authorization URL parameters
        params = {
            'response_type': 'code',
            'client_id': config.client_id,
            'redirect_uri': config.redirect_uri,
            'state': state,
            'code_challenge': code_challenge,
            'code_challenge_method': challenge_method,
        }
        
        # Add optional parameters
        if config.scope:
            params['scope'] = config.scope
            
        # Store PKCE verifier and config for later use
        self._pending_requests[state] = {
            'code_verifier': code_verifier,
            'config': config,
            'created_at': datetime.utcnow()
        }
        
        # Build authorization URL
        auth_url = f"{self.AUTHORIZE_URL}?{urllib.parse.urlencode(params)}"
        
        return AuthorizationRequest(
            authorization_url=auth_url,
            state=state,
            code_verifier=code_verifier,
            code_challenge=code_challenge,
            code_challenge_method=challenge_method
        )
    
    async def exchange_code(self, code: str, state: str,
                          code_verifier: Optional[str] = None) -> TokenResponse:
        """
        Exchange authorization code for tokens.
        
        Args:
            code: Authorization code from callback
            state: State parameter from callback
            code_verifier: Optional PKCE code verifier
            
        Returns:
            TokenResponse with access and refresh tokens
            
        Raises:
            AuthorizationError: If code exchange fails
        """
        # Retrieve stored request data
        request_data = self._pending_requests.get(state)
        if not request_data and not code_verifier:
            raise AuthorizationError("Invalid state or missing code_verifier")
        
        # Use stored verifier or provided one
        if request_data:
            code_verifier = request_data['code_verifier']
            config = request_data['config']
            # Clean up stored request
            del self._pending_requests[state]
        else:
            # Must have config in current context
            raise AuthorizationError("Missing configuration context")
        
        # Prepare token request
        token_data = {
            'grant_type': 'authorization_code',
            'code': code,
            'redirect_uri': config.redirect_uri,
            'client_id': config.client_id,
            'client_secret': config.client_secret,
            'code_verifier': code_verifier
        }
        
        try:
            # Make token request
            response = await self.http_client.post(
                self.TOKEN_URL,
                data=token_data,
                headers={'Content-Type': 'application/x-www-form-urlencoded'}
            )
            response.raise_for_status()
            
            # Parse response
            token_response = response.json()
            
            # Create TokenResponse
            return TokenResponse(
                access_token=token_response['access_token'],
                token_type=token_response.get('token_type', 'Bearer'),
                expires_in=token_response.get('expires_in'),
                refresh_token=token_response.get('refresh_token'),
                scope=token_response.get('scope'),
                issued_at=datetime.utcnow()
            )
            
        except httpx.HTTPStatusError as e:
            error_data = e.response.json() if e.response.content else {}
            raise AuthorizationError(
                f"Token exchange failed: {e.response.status_code}",
                error_code=error_data.get('error'),
                error_description=error_data.get('error_description')
            )
        except Exception as e:
            raise AuthorizationError(f"Token exchange error: {str(e)}")
    
    async def refresh_token(self, refresh_token: str) -> TokenResponse:
        """
        Refresh access token using refresh token.
        
        Args:
            refresh_token: The refresh token
            
        Returns:
            TokenResponse with new access token
            
        Raises:
            TokenRefreshError: If refresh fails
        """
        token_data = {
            'grant_type': 'refresh_token',
            'refresh_token': refresh_token
        }
        
        try:
            response = await self.http_client.post(
                self.TOKEN_URL,
                data=token_data,
                headers={'Content-Type': 'application/x-www-form-urlencoded'}
            )
            response.raise_for_status()
            
            # Parse response
            token_response = response.json()
            
            return TokenResponse(
                access_token=token_response['access_token'],
                token_type=token_response.get('token_type', 'Bearer'),
                expires_in=token_response.get('expires_in'),
                refresh_token=token_response.get('refresh_token', refresh_token),
                scope=token_response.get('scope'),
                issued_at=datetime.utcnow()
            )
            
        except httpx.HTTPStatusError as e:
            error_data = e.response.json() if e.response.content else {}
            raise TokenRefreshError(
                f"Token refresh failed: {e.response.status_code}",
                {'error': error_data.get('error'), 
                 'error_description': error_data.get('error_description')}
            )
        except Exception as e:
            raise TokenRefreshError(f"Token refresh error: {str(e)}")
    
    async def authenticate(self, config: AuthConfig) -> TokenResponse:
        """
        Perform authentication and return tokens.
        
        This method is not directly usable for Authorization Code Flow
        as it requires user interaction. Use get_auth_url() and 
        exchange_code() instead.
        
        Args:
            config: Authentication configuration
            
        Raises:
            NotImplementedError: This flow requires user interaction
        """
        raise NotImplementedError(
            "Authorization Code Flow requires user interaction. "
            "Use get_auth_url() and exchange_code() methods instead."
        )
    
    
    async def validate_callback(self, code: str, state: str) -> Dict[str, Any]:
        """
        Validate OAuth callback parameters.
        
        Args:
            code: Authorization code from callback
            state: State parameter from callback
            
        Returns:
            Dict containing validation results
            
        Raises:
            AuthorizationError: If validation fails
        """
        # Check if state exists in pending requests
        if state not in self._pending_requests:
            raise AuthorizationError("Invalid state parameter")
        
        request_data = self._pending_requests[state]
        
        return {
            "valid": True,
            "code": code,
            "code_verifier": request_data['code_verifier'],
            "config": request_data['config']
        }
    
    async def close(self):
        """Close the HTTP client."""
        await self.http_client.aclose()
    
    def cleanup_expired_requests(self, timeout_minutes: int = 10):
        """
        Clean up expired pending requests.
        
        Args:
            timeout_minutes: Minutes after which to consider request expired
        """
        current_time = datetime.utcnow()
        expired_states = []
        
        for state, data in self._pending_requests.items():
            created_at = data['created_at']
            if (current_time - created_at).total_seconds() > timeout_minutes * 60:
                expired_states.append(state)
        
        for state in expired_states:
            del self._pending_requests[state]
