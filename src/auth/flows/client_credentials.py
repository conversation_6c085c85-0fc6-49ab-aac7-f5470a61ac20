"""
OAuth 2.0 Client Credentials Flow implementation for Sage Intacct.

This module implements the Client Credentials Flow, which is used for
server-to-server authentication without user interaction.
"""

from typing import Optional, Dict, Any
from datetime import datetime
import httpx
import base64

from ..interfaces import (
    AuthFlow,
    AuthConfig,
    TokenResponse,
    AuthorizationRequest,
)
from ..exceptions import (
    AuthorizationError,
    InvalidConfigError,
    TokenRefreshError,
    InvalidCredentialsError,
)
from ..models import Token, TokenType


class ClientCredentialsFlow(AuthFlow):
    """
    Implements OAuth 2.0 Client Credentials Flow for Sage Intacct.
    
    This flow is suitable for:
    - Server-to-server authentication
    - Backend services and batch jobs
    - Automated processes without user interaction
    - Web Services user authentication
    """
    
    # Sage Intacct OAuth endpoints
    TOKEN_URL = "https://api.intacct.com/ia/api/v1/oauth2/token"
    
    def __init__(self, http_client: Optional[httpx.AsyncClient] = None):
        """
        Initialize the Client Credentials Flow.
        
        Args:
            http_client: Optional HTTP client for making requests
        """
        self.http_client = http_client or httpx.AsyncClient()
    
    async def authenticate(self, config: AuthConfig) -> TokenResponse:
        """
        Perform client credentials authentication and return tokens.
        
        Args:
            config: Authentication configuration with client credentials
            
        Returns:
            TokenResponse with access token
            
        Raises:
            InvalidConfigError: If required configuration is missing
            InvalidCredentialsError: If authentication fails
        """
        # Validate configuration
        if not config.client_id:
            raise InvalidConfigError("client_id is required")
        if not config.client_secret:
            raise InvalidConfigError("client_secret is required")
        
        # Prepare token request data
        token_data = {
            'grant_type': 'client_credentials',
            'client_id': config.client_id,
            'client_secret': config.client_secret,
        }
        
        # Add optional scope
        if config.scope:
            token_data['scope'] = config.scope
        
        # Add Web Services user credentials if provided
        # This is for Intacct's Web Services authentication
        if config.web_services_user and config.web_services_password:
            token_data['web_services_user'] = config.web_services_user
            token_data['web_services_password'] = config.web_services_password
        
        # Add company ID if provided
        if config.company_id:
            token_data['company_id'] = config.company_id
            
        # Add entity/location ID if provided
        if config.entities and len(config.entities) > 0:
            # For Client Credentials, typically use first entity
            token_data['entity_id'] = config.entities[0].entity_id
        
        try:
            # Make token request
            response = await self.http_client.post(
                self.TOKEN_URL,
                data=token_data,
                headers={'Content-Type': 'application/x-www-form-urlencoded'}
            )
            response.raise_for_status()
            
            # Parse response
            token_response = await response.json()
            
            # Create TokenResponse
            return TokenResponse(
                access_token=token_response['access_token'],
                token_type=token_response.get('token_type', 'Bearer'),
                expires_in=token_response.get('expires_in'),
                refresh_token=token_response.get('refresh_token'),
                scope=token_response.get('scope'),
                issued_at=datetime.utcnow()
            )
            
        except httpx.HTTPStatusError as e:
            error_data = {}
            try:
                error_data = e.response.json()
            except:
                pass
                
            if e.response.status_code == 401:
                raise InvalidCredentialsError(
                    f"Authentication failed: {error_data.get('error_description', 'Invalid credentials')}"
                )
            else:
                raise AuthorizationError(
                    f"Token request failed: {e.response.status_code}",
                    error_code=error_data.get('error'),
                    error_description=error_data.get('error_description')
                )
        except Exception as e:
            raise AuthorizationError(f"Authentication error: {str(e)}")
    
    async def refresh_token(self, refresh_token: str) -> TokenResponse:
        """
        Refresh access token using refresh token.
        
        Note: Client Credentials Flow typically doesn't use refresh tokens.
        Instead, request a new access token when the current one expires.
        
        Args:
            refresh_token: The refresh token (may not be supported)
            
        Returns:
            TokenResponse with new access token
        """        # Try standard refresh token flow
        token_data = {
            'grant_type': 'refresh_token',
            'refresh_token': refresh_token
        }
        
        try:
            response = await self.http_client.post(
                self.TOKEN_URL,
                data=token_data,
                headers={'Content-Type': 'application/x-www-form-urlencoded'}
            )
            response.raise_for_status()
            
            token_response = response.json()
            
            return TokenResponse(
                access_token=token_response['access_token'],
                token_type=token_response.get('token_type', 'Bearer'),
                expires_in=token_response.get('expires_in'),
                refresh_token=token_response.get('refresh_token', refresh_token),
                scope=token_response.get('scope'),
                issued_at=datetime.utcnow()
            )
            
        except httpx.HTTPStatusError:
            raise TokenRefreshError(
                "Refresh token not supported for Client Credentials Flow. "
                "Request a new access token instead."
            )
    
    def get_auth_url(self, config: AuthConfig) -> AuthorizationRequest:
        """
        Client Credentials Flow does not use authorization URLs.
        
        Raises:
            NotImplementedError: This flow doesn't require user authorization
        """
        raise NotImplementedError(
            "Client Credentials Flow does not require user authorization. "
            "Use authenticate() method directly."
        )
    
    async def exchange_code(self, code: str, state: str,
                          code_verifier: Optional[str] = None) -> TokenResponse:
        """
        Client Credentials Flow does not use authorization codes.
        
        Raises:
            NotImplementedError: This flow doesn't use authorization codes
        """
        raise NotImplementedError(
            "Client Credentials Flow does not use authorization codes. "
            "Use authenticate() method directly."
        )
    
    async def close(self):
        """Close the HTTP client."""
        await self.http_client.aclose()
    
    def _create_basic_auth_header(self, client_id: str, client_secret: str) -> str:
        """
        Create Basic Authentication header value.
        
        Some OAuth servers require Basic Auth instead of form parameters.
        
        Args:
            client_id: Client identifier
            client_secret: Client secret
            
        Returns:
            Basic auth header value
        """
        credentials = f"{client_id}:{client_secret}"
        encoded = base64.b64encode(credentials.encode('utf-8')).decode('utf-8')
        return f"Basic {encoded}"
