"""
Token storage implementation for Sage Intacct authentication.

This module provides different token storage backends for securely
storing and retrieving OAuth tokens.
"""

import json
import os
from pathlib import Path
from typing import Optional, List, Dict, Any
from datetime import datetime
import asyncio
from abc import ABC, abstractmethod

from .interfaces import TokenStorage, TokenResponse
from .exceptions import StorageError
from .models import Token


class FileTokenStorage(TokenStorage):
    """
    File-based token storage implementation.
    
    Stores tokens as JSON files in a specified directory.
    Suitable for development and single-instance deployments.
    """
    
    def __init__(self, storage_path: str = ".tokens"):
        """
        Initialize file-based token storage.
        
        Args:
            storage_path: Directory path for storing token files
        """
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(parents=True, exist_ok=True)
        self._lock = asyncio.Lock()
    
    def _get_token_path(self, key: str) -> Path:
        """Get the file path for a token key."""
        # Sanitize key to be filesystem-safe
        safe_key = key.replace('/', '_').replace('\\', '_').replace(':', '_')
        return self.storage_path / f"{safe_key}.json"
    
    async def save_token(self, key: str, token: TokenResponse) -> None:
        """
        Save token to file storage.
        
        Args:
            key: Unique identifier for the token
            token: Token to save
        """
        async with self._lock:
            try:
                token_path = self._get_token_path(key)
                
                # Convert TokenResponse to Token model for better serialization
                token_model = Token(
                    access_token=token.access_token,
                    token_type=token.token_type,
                    expires_in=token.expires_in,
                    refresh_token=token.refresh_token,
                    scope=token.scope,
                    issued_at=token.issued_at or datetime.utcnow()
                )
                
                # Write token data to file
                token_data = token_model.to_dict()
                
                with open(token_path, 'w') as f:
                    json.dump(token_data, f, indent=2)
                    
            except Exception as e:
                raise StorageError(
                    f"Failed to save token: {str(e)}",
                    operation="save"
                )
    
    async def get_token(self, key: str) -> Optional[TokenResponse]:
        """
        Retrieve token from file storage.
        
        Args:
            key: Unique identifier for the token
            
        Returns:
            TokenResponse if found, None otherwise
        """
        async with self._lock:
            try:
                token_path = self._get_token_path(key)
                
                if not token_path.exists():
                    return None
                
                with open(token_path, 'r') as f:
                    token_data = json.load(f)
                
                # Convert to Token model
                token_model = Token.from_dict(token_data)
                
                # Convert to TokenResponse
                return TokenResponse(
                    access_token=token_model.access_token,
                    token_type=token_model.token_type.value,
                    expires_in=token_model.expires_in,
                    refresh_token=token_model.refresh_token,
                    scope=token_model.scope,
                    issued_at=token_model.issued_at
                )
                
            except FileNotFoundError:
                return None
            except Exception as e:
                raise StorageError(
                    f"Failed to retrieve token: {str(e)}",
                    operation="get"
                )
    
    async def delete_token(self, key: str) -> None:
        """
        Delete token from storage.
        
        Args:
            key: Unique identifier for the token
        """
        async with self._lock:
            try:
                token_path = self._get_token_path(key)
                if token_path.exists():
                    token_path.unlink()
            except Exception as e:
                raise StorageError(
                    f"Failed to delete token: {str(e)}",
                    operation="delete"
                )
    
    async def list_tokens(self) -> List[str]:
        """List all token keys in storage."""
        async with self._lock:
            try:
                tokens = []
                for token_file in self.storage_path.glob("*.json"):
                    # Remove .json extension and reverse sanitization
                    key = token_file.stem
                    tokens.append(key)
                return tokens
            except Exception as e:
                raise StorageError(
                    f"Failed to list tokens: {str(e)}",
                    operation="list"
                )


class InMemoryTokenStorage(TokenStorage):
    """
    In-memory token storage implementation.
    
    Stores tokens in memory. Suitable for testing and
    single-instance applications where persistence is not required.
    """
    
    def __init__(self):
        """Initialize in-memory token storage."""
        self._tokens: Dict[str, TokenResponse] = {}
        self._lock = asyncio.Lock()
    
    async def save_token(self, key: str, token: TokenResponse) -> None:
        """Save token to memory."""
        async with self._lock:
            self._tokens[key] = token
    
    async def get_token(self, key: str) -> Optional[TokenResponse]:
        """Retrieve token from memory."""
        async with self._lock:
            return self._tokens.get(key)
    
    async def delete_token(self, key: str) -> None:
        """Delete token from memory."""
        async with self._lock:
            self._tokens.pop(key, None)
    
    async def list_tokens(self) -> List[str]:
        """List all token keys."""
        async with self._lock:
            return list(self._tokens.keys())
