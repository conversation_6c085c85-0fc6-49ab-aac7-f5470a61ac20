"""
Authentication interfaces and protocols for Sage Intacct OAuth 2.0.

This module defines the abstract interfaces that all authentication
implementations must follow.
"""

from abc import ABC, abstractmethod
from typing import Protocol, Optional, Dict, Any, List
from datetime import datetime
from dataclasses import dataclass
from enum import Enum
import httpx


class TokenType(str, Enum):
    """Token type enumeration."""
    BEARER = "Bearer"
    BASIC = "Basic"


class AuthMethod(str, Enum):
    """Authentication method enumeration."""
    AUTHORIZATION_CODE = "authorization_code"
    CLIENT_CREDENTIALS = "client_credentials"
    WEB_SERVICES_USER = "web_services_user"


@dataclass
class EntityScope:
    """Represents an Intacct entity scope for multi-entity access."""
    entity_id: str
    entity_name: Optional[str] = None
    permissions: Optional[List[str]] = None


@dataclass
class AuthConfig:
    """Base authentication configuration."""
    client_id: str
    client_secret: str
    auth_type: str = "client_credentials"  # "authorization_code" or "client_credentials"
    redirect_uri: Optional[str] = None
    scope: Optional[str] = None
    entities: Optional[List[EntityScope]] = None
    
    # OAuth URLs
    authorization_url: Optional[str] = None
    token_url: Optional[str] = None
    
    # Intacct specific
    sender_id: Optional[str] = None
    sender_password: Optional[str] = None
    web_services_user: Optional[str] = None
    web_services_password: Optional[str] = None
    company_id: Optional[str] = None
    user_id: Optional[str] = None


@dataclass
class AuthorizationRequest:
    """OAuth 2.0 authorization request parameters."""
    authorization_url: str
    state: str
    code_verifier: Optional[str] = None  # For PKCE
    code_challenge: Optional[str] = None  # For PKCE
    code_challenge_method: Optional[str] = None  # For PKCE


@dataclass
class TokenRequest:
    """OAuth 2.0 token request parameters."""
    grant_type: str
    code: Optional[str] = None
    refresh_token: Optional[str] = None
    username: Optional[str] = None
    password: Optional[str] = None
    scope: Optional[str] = None
    code_verifier: Optional[str] = None  # For PKCE


@dataclass
class TokenResponse:
    """OAuth 2.0 token response."""
    access_token: str
    token_type: str
    expires_in: Optional[int] = None
    refresh_token: Optional[str] = None
    scope: Optional[str] = None
    issued_at: Optional[datetime] = None
    
    @property
    def expires_at(self) -> Optional[datetime]:
        """Calculate expiration time."""
        if self.issued_at and self.expires_in:
            from datetime import timedelta
            return self.issued_at + timedelta(seconds=self.expires_in)
        return None
    
    @property
    def is_expired(self) -> bool:
        """Check if token is expired."""
        if self.expires_at:
            return datetime.utcnow() > self.expires_at
        return False


class AuthFlow(ABC):
    """Abstract base class for authentication flows."""
    
    @abstractmethod
    async def authenticate(self, config: AuthConfig) -> TokenResponse:
        """Perform authentication and return tokens."""
        pass
    
    @abstractmethod
    async def refresh_token(self, refresh_token: str) -> TokenResponse:
        """Refresh access token using refresh token."""
        pass
    
    @abstractmethod
    def get_auth_url(self, config: AuthConfig) -> AuthorizationRequest:
        """Get authorization URL for user consent."""
        pass
    
    @abstractmethod
    async def exchange_code(self, code: str, state: str, 
                          code_verifier: Optional[str] = None) -> TokenResponse:
        """Exchange authorization code for tokens."""
        pass


class TokenStorage(Protocol):
    """Protocol for token storage implementations."""
    
    async def save_token(self, key: str, token: TokenResponse) -> None:
        """Save token to storage."""
        ...
    
    async def get_token(self, key: str) -> Optional[TokenResponse]:
        """Retrieve token from storage."""
        ...
    
    async def delete_token(self, key: str) -> None:
        """Delete token from storage."""
        ...
    
    async def list_tokens(self) -> List[str]:
        """List all token keys in storage."""
        ...
