"""
Data models for Sage Intacct authentication.

This module contains the core data models used throughout the authentication
system, including tokens and authentication configurations.
"""

from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from enum import Enum

# Re-export enums from interfaces for convenience
from .interfaces import TokenType, AuthMethod, EntityScope


@dataclass
class Token:
    """Represents an OAuth 2.0 token with metadata."""
    
    access_token: str
    token_type: TokenType = TokenType.BEARER
    expires_in: Optional[int] = None
    refresh_token: Optional[str] = None
    scope: Optional[str] = None
    issued_at: datetime = field(default_factory=datetime.utcnow)
    
    # Intacct specific fields
    session_id: Optional[str] = None
    entity_id: Optional[str] = None
    user_id: Optional[str] = None
    company_id: Optional[str] = None
    
    # Additional metadata
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def expires_at(self) -> Optional[datetime]:
        """Calculate token expiration time."""
        if self.expires_in:
            return self.issued_at + timedelta(seconds=self.expires_in)
        return None
    
    @property
    def is_expired(self) -> bool:
        """Check if the token has expired."""
        if self.expires_at:
            # Add 60 second buffer for clock skew
            return datetime.utcnow() > (self.expires_at - timedelta(seconds=60))
        return False
    
    @property
    def time_until_expiry(self) -> Optional[timedelta]:
        """Get time remaining until token expires."""
        if self.expires_at:
            return self.expires_at - datetime.utcnow()
        return None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert token to dictionary for serialization."""
        data = {
            "access_token": self.access_token,
            "token_type": self.token_type.value,
            "expires_in": self.expires_in,
            "refresh_token": self.refresh_token,
            "scope": self.scope,
            "issued_at": self.issued_at.isoformat(),
            "session_id": self.session_id,
            "entity_id": self.entity_id,
            "user_id": self.user_id,
            "company_id": self.company_id,
            "metadata": self.metadata
        }
        # Remove None values
        return {k: v for k, v in data.items() if v is not None}
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Token":
        """Create token from dictionary."""
        # Convert ISO format string back to datetime
        if "issued_at" in data and isinstance(data["issued_at"], str):
            data["issued_at"] = datetime.fromisoformat(data["issued_at"])
        
        # Convert token_type string to enum
        if "token_type" in data and isinstance(data["token_type"], str):
            data["token_type"] = TokenType(data["token_type"])
        
        return cls(**data)
