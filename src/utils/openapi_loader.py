"""
OpenAPI specification loader and utilities.

This module provides functionality to load, validate, and merge OpenAPI
specifications for the Sage Intacct API.
"""

import os
import yaml
import logging
from typing import Dict, Any, List, Optional, Union
from pathlib import Path
from dataclasses import dataclass
import json

logger = logging.getLogger(__name__)


@dataclass
class OpenAPISpec:
    """Container for an OpenAPI specification."""
    
    spec: Dict[str, Any]
    source_path: Optional[Path] = None
    
    @property
    def openapi_version(self) -> str:
        """Get the OpenAPI version."""
        return self.spec.get("openapi", "3.0.0")
    
    @property
    def info(self) -> Dict[str, Any]:
        """Get the info section."""
        return self.spec.get("info", {})
    
    @property
    def paths(self) -> Dict[str, Any]:
        """Get the paths section."""
        return self.spec.get("paths", {})
    
    @property
    def components(self) -> Dict[str, Any]:
        """Get the components section."""
        return self.spec.get("components", {})
    
    @property
    def servers(self) -> List[Dict[str, Any]]:
        """Get the servers section."""
        return self.spec.get("servers", [])
    
    def get_operations(self) -> List[Dict[str, Any]]:
        """Extract all operations from the spec."""
        operations = []
        for path, path_item in self.paths.items():
            for method in ["get", "post", "put", "patch", "delete"]:
                if method in path_item:
                    operation = path_item[method].copy()
                    operation["_path"] = path
                    operation["_method"] = method.upper()
                    operations.append(operation)
        return operations


class OpenAPILoader:
    """Loads and manages OpenAPI specifications."""
    
    def __init__(self, base_path: Optional[Union[str, Path]] = None):
        """
        Initialize the loader.
        
        Args:
            base_path: Base directory for resolving relative paths
        """
        self.base_path = Path(base_path) if base_path else Path.cwd()
    
    def load_spec(self, path: Union[str, Path]) -> OpenAPISpec:
        """
        Load an OpenAPI specification from a file.
        
        Args:
            path: Path to the OpenAPI spec file
            
        Returns:
            Loaded OpenAPI specification
            
        Raises:
            FileNotFoundError: If the file doesn't exist
            ValueError: If the file format is invalid
        """
        file_path = self._resolve_path(path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"OpenAPI spec not found: {file_path}")
        
        logger.info(f"Loading OpenAPI spec from {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                if file_path.suffix.lower() in ['.yaml', '.yml']:
                    spec_data = yaml.safe_load(f)
                elif file_path.suffix.lower() == '.json':
                    spec_data = json.load(f)
                else:
                    raise ValueError(f"Unsupported file format: {file_path.suffix}")
            
            spec = OpenAPISpec(spec=spec_data, source_path=file_path)
            
            # Basic validation
            validate_openapi_spec(spec)
            
            return spec
            
        except yaml.YAMLError as e:
            raise ValueError(f"Invalid YAML in {file_path}: {e}")
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in {file_path}: {e}")
    
    def load_multiple(self, paths: List[Union[str, Path]]) -> List[OpenAPISpec]:
        """
        Load multiple OpenAPI specifications.
        
        Args:
            paths: List of paths to OpenAPI spec files
            
        Returns:
            List of loaded specifications
        """
        specs = []
        for path in paths:
            try:
                spec = self.load_spec(path)
                specs.append(spec)
            except Exception as e:
                logger.error(f"Failed to load {path}: {e}")
                raise
        return specs
    
    def _resolve_path(self, path: Union[str, Path]) -> Path:
        """Resolve a path relative to the base path."""
        path = Path(path)
        if not path.is_absolute():
            path = self.base_path / path
        return path.resolve()


def validate_openapi_spec(spec: OpenAPISpec) -> None:
    """
    Validate an OpenAPI specification.
    
    Args:
        spec: The OpenAPI specification to validate
        
    Raises:
        ValueError: If the specification is invalid
    """
    # Check required fields
    if "openapi" not in spec.spec:
        raise ValueError("Missing 'openapi' field")
    
    if "info" not in spec.spec:
        raise ValueError("Missing 'info' field")
    
    if "paths" not in spec.spec:
        raise ValueError("Missing 'paths' field")
    
    # Validate OpenAPI version
    version = spec.openapi_version
    if not version.startswith("3."):
        raise ValueError(f"Unsupported OpenAPI version: {version}")
    
    # Validate info section
    info = spec.info
    if "title" not in info:
        raise ValueError("Missing 'info.title' field")
    if "version" not in info:
        raise ValueError("Missing 'info.version' field")
    
    logger.debug(f"Validated OpenAPI spec: {info.get('title')} v{info.get('version')}")


def merge_openapi_specs(specs: List[OpenAPISpec], title: Optional[str] = None) -> OpenAPISpec:
    """
    Merge multiple OpenAPI specifications into one.
    
    Args:
        specs: List of OpenAPI specifications to merge
        title: Optional title for the merged spec
        
    Returns:
        Merged OpenAPI specification
        
    Raises:
        ValueError: If specs are incompatible or empty
    """
    if not specs:
        raise ValueError("No specifications to merge")
    
    if len(specs) == 1:
        return specs[0]
    
    # Start with the first spec as base
    merged = {
        "openapi": specs[0].openapi_version,
        "info": {
            "title": title or "Merged API",
            "version": "1.0.0",
            "description": "Merged from multiple OpenAPI specifications"
        },
        "paths": {},
        "components": {
            "schemas": {},
            "responses": {},
            "parameters": {},
            "securitySchemes": {},
        },
        "servers": []
    }
    
    # Track sources for debugging
    sources = []
    
    # Merge each spec
    for spec in specs:
        # Add source info
        if spec.source_path:
            sources.append(str(spec.source_path))
        
        # Merge paths
        for path, path_item in spec.paths.items():
            if path in merged["paths"]:
                # Merge operations for existing path
                for method, operation in path_item.items():
                    if method in merged["paths"][path]:
                        logger.warning(f"Duplicate operation: {method.upper()} {path}")
                    merged["paths"][path][method] = operation
            else:
                merged["paths"][path] = path_item
        
        # Merge components
        for component_type in ["schemas", "responses", "parameters", "securitySchemes"]:
            if component_type in spec.components:
                merged["components"][component_type].update(
                    spec.components[component_type]
                )
        
        # Merge servers (avoid duplicates)
        for server in spec.servers:
            if server not in merged["servers"]:
                merged["servers"].append(server)
    
    # Add merge metadata
    merged["info"]["x-merged-from"] = sources
    
    return OpenAPISpec(spec=merged)
