"""
Configuration management for Sage Intacct MCP Server.

This module provides configuration loading, validation, and management
for the server and its modules.
"""

import os
import logging
from pathlib import Path
from typing import Dict, Any, Optional, Union, List
import yaml
from pydantic import BaseModel, Field, ValidationError

logger = logging.getLogger(__name__)


class ModuleConfig(BaseModel):
    """Configuration for an individual module."""
    enabled: bool = True
    settings: Dict[str, Any] = Field(default_factory=dict)


class ServerConfig(BaseModel):
    """Main server configuration."""
    
    # Server settings
    server_name: str = "sage-intacct-mcp"
    server_version: str = "1.0.0"
    log_level: str = "INFO"
    
    # Authentication settings
    authentication: Dict[str, Any] = Field(default_factory=dict)
    
    # Module configuration
    modules: Dict[str, ModuleConfig] = Field(default_factory=dict)
    
    # Performance settings
    performance: Dict[str, Any] = Field(default_factory=lambda: {
        "cache_enabled": True,
        "cache_ttl": 300,
        "max_concurrent_requests": 10,
        "request_timeout": 30
    })
    
    # Feature flags
    features: Dict[str, bool] = Field(default_factory=lambda: {
        "strict_module_loading": False,
        "auto_retry": True,
        "batch_operations": False
    })


class ConfigManager:
    """
    Manages configuration for the Intacct MCP server.
    
    Supports:
    - YAML configuration files
    - Environment variable overrides
    - Runtime configuration updates
    - Configuration validation
    """
    
    def __init__(self, config_path: Optional[Union[str, Path]] = None):
        """
        Initialize configuration manager.
        
        Args:
            config_path: Path to configuration file
        """
        self.config_path = Path(config_path) if config_path else None
        self._config: ServerConfig = ServerConfig()
        self._env_prefix = "INTACCT_MCP_"
        
        # Load configuration
        if self.config_path:
            self.load_config()
        
        # Apply environment overrides
        self._apply_env_overrides()
    
    def load_config(self) -> None:
        """Load configuration from file."""
        if not self.config_path or not self.config_path.exists():
            logger.warning(f"Configuration file not found: {self.config_path}")
            return
        
        try:
            with open(self.config_path, 'r') as f:
                data = yaml.safe_load(f)
            
            # Validate and load configuration
            self._config = ServerConfig(**data)
            logger.info(f"Loaded configuration from {self.config_path}")
            
        except yaml.YAMLError as e:
            logger.error(f"Failed to parse configuration file: {e}")
            raise
        except ValidationError as e:
            logger.error(f"Configuration validation failed: {e}")
            raise
    
    def _apply_env_overrides(self) -> None:
        """Apply environment variable overrides."""
        # Map of environment variables to config paths
        env_mappings = {
            f"{self._env_prefix}LOG_LEVEL": "log_level",
            f"{self._env_prefix}AUTH_CLIENT_ID": "authentication.client_id",
            f"{self._env_prefix}AUTH_CLIENT_SECRET": "authentication.client_secret",
            f"{self._env_prefix}AUTH_REDIRECT_URI": "authentication.redirect_uri",
            f"{self._env_prefix}CACHE_ENABLED": "performance.cache_enabled",
            f"{self._env_prefix}CACHE_TTL": "performance.cache_ttl",
        }
        
        for env_var, config_path in env_mappings.items():
            value = os.getenv(env_var)
            if value is not None:
                self._set_nested(config_path, value)
                logger.debug(f"Applied environment override: {env_var}")
    
    def _set_nested(self, path: str, value: Any) -> None:
        """Set a nested configuration value."""
        parts = path.split('.')
        current = self._config
        
        # Navigate to the parent
        for part in parts[:-1]:
            if hasattr(current, part):
                current = getattr(current, part)
            else:
                # Handle dict attributes
                if isinstance(current, dict):
                    if part not in current:
                        current[part] = {}
                    current = current[part]
        
        # Set the value
        final_key = parts[-1]
        if hasattr(current, final_key):
            # Convert value type if needed
            attr_type = type(getattr(current, final_key))
            if attr_type == bool:
                value = value.lower() in ('true', '1', 'yes')
            elif attr_type == int:
                value = int(value)
            
            setattr(current, final_key, value)
        elif isinstance(current, dict):
            current[final_key] = value
    
    def get(self, path: str, default: Any = None) -> Any:
        """
        Get a configuration value by path.
        
        Args:
            path: Dot-separated path to config value
            default: Default value if not found
            
        Returns:
            Configuration value or default
        """
        parts = path.split('.')
        current = self._config
        
        try:
            for part in parts:
                if hasattr(current, part):
                    current = getattr(current, part)
                elif isinstance(current, dict) and part in current:
                    current = current[part]
                else:
                    return default
            return current
        except Exception:
            return default
    
    def set(self, path: str, value: Any) -> None:
        """
        Set a configuration value at runtime.
        
        Args:
            path: Dot-separated path to config value
            value: Value to set
        """
        self._set_nested(path, value)
        logger.debug(f"Updated configuration: {path} = {value}")
    
    def get_module_config(self, module_name: str) -> Optional[ModuleConfig]:
        """
        Get configuration for a specific module.
        
        Args:
            module_name: Name of the module
            
        Returns:
            Module configuration or None
        """
        return self._config.modules.get(module_name)
    
    def is_module_enabled(self, module_name: str) -> bool:
        """
        Check if a module is enabled.
        
        Args:
            module_name: Name of the module
            
        Returns:
            True if module is enabled
        """
        module_config = self.get_module_config(module_name)
        return module_config.enabled if module_config else True
    
    def get_enabled_modules(self) -> List[str]:
        """
        Get list of enabled modules.
        
        Returns:
            List of enabled module names
        """
        return [
            name for name, config in self._config.modules.items()
            if config.enabled
        ]
    
    def validate(self) -> bool:
        """
        Validate the current configuration.
        
        Returns:
            True if configuration is valid
        """
        try:
            # Re-validate the configuration
            ServerConfig(**self._config.dict())
            return True
        except ValidationError as e:
            logger.error(f"Configuration validation failed: {e}")
            return False
    
    def save(self, path: Optional[Union[str, Path]] = None) -> None:
        """
        Save configuration to file.
        
        Args:
            path: Path to save to (uses original path if not provided)
        """
        save_path = Path(path) if path else self.config_path
        if not save_path:
            raise ValueError("No path provided for saving configuration")
        
        # Ensure directory exists
        save_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Convert to dict and save
        config_dict = self._config.dict(exclude_none=True)
        
        with open(save_path, 'w') as f:
            yaml.dump(config_dict, f, default_flow_style=False)
        
        logger.info(f"Saved configuration to {save_path}")
    
    def to_dict(self) -> Dict[str, Any]:
        """Get configuration as dictionary."""
        return self._config.dict()
