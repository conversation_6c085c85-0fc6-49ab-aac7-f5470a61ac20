"""
Route mapping utilities for Sage Intacct API endpoints.

This module provides functionality to map OpenAPI paths to MCP tool names
and categorize endpoints for better organization.
"""

import re
import logging
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class EndpointCategory(Enum):
    """Categories for Intacct API endpoints."""
    ACCOUNTING = "accounting"
    ACCOUNTS_PAYABLE = "accounts_payable"
    ACCOUNTS_RECEIVABLE = "accounts_receivable"
    GENERAL_LEDGER = "general_ledger"
    INVENTORY = "inventory"
    ORDER_MANAGEMENT = "order_management"
    PURCHASING = "purchasing"
    PROJECTS = "projects"
    REPORTING = "reporting"
    SYSTEM = "system"
    COMPANY = "company"
    CONTACTS = "contacts"
    UNKNOWN = "unknown"


@dataclass
class RouteMappingRule:
    """Rule for mapping routes to tool names."""
    
    pattern: str
    tool_name_template: str
    category: EndpointCategory
    description: Optional[str] = None
    
    def matches(self, path: str) -> bool:
        """Check if this rule matches the given path."""
        return bool(re.match(self.pattern, path))
    
    def generate_tool_name(self, path: str, method: str) -> str:
        """Generate a tool name based on this rule."""
        # Extract path parameters
        match = re.match(self.pattern, path)
        if not match:
            return ""
        
        # Get method prefix
        method_prefix = {
            "GET": "get",
            "POST": "create",
            "PUT": "update",
            "PATCH": "patch",
            "DELETE": "delete"
        }.get(method.upper(), method.lower())
        
        # Apply template with method prefix
        tool_name = self.tool_name_template.format(
            method=method_prefix,
            **match.groupdict()
        )
        
        return tool_name


class RouteMapper:
    """Maps Intacct API routes to MCP tool names."""
    
    def __init__(self):
        """Initialize with default mapping rules."""
        self.rules = self._create_default_rules()
    
    def _create_default_rules(self) -> List[RouteMappingRule]:
        """Create default mapping rules for Intacct endpoints."""
        return [
            # Accounts Payable
            RouteMappingRule(
                pattern=r"/services/api/purchase/invoices/?$",
                tool_name_template="{method}_purchase_invoices",
                category=EndpointCategory.ACCOUNTS_PAYABLE
            ),
            RouteMappingRule(
                pattern=r"/services/api/purchase/invoices/(?P<id>\d+)/?$",
                tool_name_template="{method}_purchase_invoice_by_id",
                category=EndpointCategory.ACCOUNTS_PAYABLE
            ),
            RouteMappingRule(
                pattern=r"/services/api/purchase/credit-notes/?$",
                tool_name_template="{method}_purchase_credit_notes",
                category=EndpointCategory.ACCOUNTS_PAYABLE
            ),
            
            # Accounts Receivable
            RouteMappingRule(
                pattern=r"/services/api/sales/invoices/?$",
                tool_name_template="{method}_sales_invoices",
                category=EndpointCategory.ACCOUNTS_RECEIVABLE
            ),
            RouteMappingRule(
                pattern=r"/services/api/sales/invoices/(?P<id>\d+)/?$",
                tool_name_template="{method}_sales_invoice_by_id",
                category=EndpointCategory.ACCOUNTS_RECEIVABLE
            ),
            RouteMappingRule(
                pattern=r"/services/api/sales/credit-notes/?$",
                tool_name_template="{method}_sales_credit_notes",
                category=EndpointCategory.ACCOUNTS_RECEIVABLE
            ),
            
            # General Ledger
            RouteMappingRule(
                pattern=r"/services/api/ledger/accounts/?$",
                tool_name_template="{method}_ledger_accounts",
                category=EndpointCategory.GENERAL_LEDGER
            ),
            RouteMappingRule(
                pattern=r"/services/api/ledger/journals/?$",
                tool_name_template="{method}_journals",
                category=EndpointCategory.GENERAL_LEDGER
            ),
            RouteMappingRule(
                pattern=r"/services/api/ledger/transactions/?$",
                tool_name_template="{method}_transactions",
                category=EndpointCategory.GENERAL_LEDGER
            ),
            
            # Company and Contacts
            RouteMappingRule(
                pattern=r"/services/api/contacts/?$",
                tool_name_template="{method}_contacts",
                category=EndpointCategory.CONTACTS
            ),
            RouteMappingRule(
                pattern=r"/services/api/contacts/(?P<id>\d+)/?$",
                tool_name_template="{method}_contact_by_id",
                category=EndpointCategory.CONTACTS
            ),
            RouteMappingRule(
                pattern=r"/services/api/companies/?$",
                tool_name_template="{method}_companies",
                category=EndpointCategory.COMPANY
            ),
            
            # Generic fallback pattern
            RouteMappingRule(
                pattern=r"/services/api/(?P<module>\w+)/(?P<resource>\w+)/?$",
                tool_name_template="{method}_{module}_{resource}",
                category=EndpointCategory.UNKNOWN
            ),
        ]
    
    def map_endpoint(self, path: str, method: str) -> Tuple[str, EndpointCategory]:
        """
        Map an endpoint to a tool name and category.
        
        Args:
            path: The API endpoint path
            method: The HTTP method
            
        Returns:
            Tuple of (tool_name, category)
        """
        # Normalize path
        path = path.rstrip("/")
        if not path.startswith("/"):
            path = "/" + path
        
        # Find matching rule
        for rule in self.rules:
            if rule.matches(path):
                tool_name = rule.generate_tool_name(path, method)
                return (tool_name, rule.category)
        
        # Fallback
        logger.warning(f"No mapping rule found for {method} {path}")
        return (generate_tool_name(path, method), EndpointCategory.UNKNOWN)
    
    def add_rule(self, rule: RouteMappingRule) -> None:
        """Add a custom mapping rule."""
        # Insert at beginning to prioritize custom rules
        self.rules.insert(0, rule)
    
    def categorize_operations(
        self,
        operations: List[Dict[str, Any]]
    ) -> Dict[EndpointCategory, List[Dict[str, Any]]]:
        """
        Categorize a list of operations by endpoint category.
        
        Args:
            operations: List of OpenAPI operations
            
        Returns:
            Dictionary mapping categories to operations
        """
        categorized = {category: [] for category in EndpointCategory}
        
        for operation in operations:
            path = operation.get("_path", "")
            method = operation.get("_method", "GET")
            
            _, category = self.map_endpoint(path, method)
            categorized[category].append(operation)
        
        # Remove empty categories
        return {k: v for k, v in categorized.items() if v}


def generate_tool_name(path: str, method: str = "GET") -> str:
    """
    Generate a tool name from an API path.
    
    Args:
        path: The API endpoint path
        method: The HTTP method
        
    Returns:
        Generated tool name
    """
    # Clean up the path
    path = path.strip("/")
    path = re.sub(r"/services/api/", "", path)
    path = re.sub(r"\{[^}]+\}", "by_id", path)
    path = re.sub(r"[^\w/]+", "_", path)
    
    # Convert to snake_case
    parts = path.split("/")
    parts = [p for p in parts if p]
    
    # Add method prefix
    method_prefix = {
        "GET": "get",
        "POST": "create",
        "PUT": "update",
        "PATCH": "patch",
        "DELETE": "delete"
    }.get(method.upper(), method.lower())
    
    tool_name = f"{method_prefix}_{'_'.join(parts)}"
    
    # Clean up double underscores
    tool_name = re.sub(r"_+", "_", tool_name)
    
    return tool_name.lower()


def categorize_endpoint(path: str) -> EndpointCategory:
    """
    Categorize an endpoint based on its path.
    
    Args:
        path: The API endpoint path
        
    Returns:
        Endpoint category
    """
    path_lower = path.lower()
    
    # Check for specific patterns
    if "purchase" in path_lower or "vendor" in path_lower or "payable" in path_lower:
        return EndpointCategory.ACCOUNTS_PAYABLE
    elif "sales" in path_lower or "customer" in path_lower or "receivable" in path_lower:
        return EndpointCategory.ACCOUNTS_RECEIVABLE
    elif "ledger" in path_lower or "journal" in path_lower or "transaction" in path_lower:
        return EndpointCategory.GENERAL_LEDGER
    elif "contact" in path_lower:
        return EndpointCategory.CONTACTS
    elif "company" in path_lower or "companies" in path_lower:
        return EndpointCategory.COMPANY
    elif "inventory" in path_lower or "item" in path_lower:
        return EndpointCategory.INVENTORY
    elif "project" in path_lower:
        return EndpointCategory.PROJECTS
    elif "report" in path_lower:
        return EndpointCategory.REPORTING
    else:
        return EndpointCategory.UNKNOWN
