# Connecting Sage Intacct MCP Server to <PERSON>

## 📋 Prerequisites

1. **Sage Intacct OAuth Credentials**: You need a Client ID and Client Secret from Sage Intacct
2. **Python 3.9+**: Already confirmed installed (Python 3.13.3)
3. **<PERSON> Des<PERSON>op**: Must be installed and running

## 🚀 Quick Start

### Step 1: Configure Your Credentials

Edit the `.env` file in the project root and add your Sage Intacct OAuth credentials:

```bash
INTACCT_CLIENT_ID=your-actual-client-id
INTACCT_CLIENT_SECRET=your-actual-client-secret
```

### Step 2: Install Dependencies

Open a terminal in the project directory and run:

```bash
cd C:\Users\<USER>\Documents\GitHub\sage-intacct-mcp-server
pip install -r requirements.txt
```

### Step 3: Start Claude Desktop

The configuration has already been updated! Simply:

1. **Close Claude Desktop** completely (check system tray)
2. **Restart Claude Desktop**
3. The Sage Intacct server should appear in the MCP servers list

### Step 4: Verify Connection

In Claude Des<PERSON>op, you should see:
- A new MCP server named "sage-intacct" in the servers dropdown
- When selected, it will show available tools from the Intacct API

## 🛠️ Troubleshooting

### Server Not Appearing
1. Make sure Claude Desktop is fully closed and restarted
2. Check the logs at: `C:\Users\<USER>\AppData\Roaming\Claude\logs\`

### Authentication Issues
1. Verify your OAuth credentials in the `.env` file
2. Make sure the redirect URI matches your Intacct app settings
3. Check `intacct-mcp.log` in the project directory

### Connection Errors
Run the server manually to check for errors:
```bash
cd C:\Users\<USER>\Documents\GitHub\sage-intacct-mcp-server
python -m src.main --mode development --log-level DEBUG
```

## 📝 Configuration Location

Your Claude Desktop configuration has been updated at:
```
C:\Users\<USER>\AppData\Roaming\Claude\claude_desktop_config.json
```

The server entry looks like:
```json
"sage-intacct": {
  "command": "cmd",
  "args": [
    "/c",
    "cd /d C:\\Users\\<USER>\\Documents\\GitHub\\sage-intacct-mcp-server && python -m src.main --mode development"
  ],
  "env": {
    "PYTHONPATH": "C:\\Users\\<USER>\\Documents\\GitHub\\sage-intacct-mcp-server"
  }
}
```

## 🔐 First-Time Authentication

When you first use the server, you'll need to:
1. Authorize the OAuth connection
2. A browser window will open for Intacct login
3. Grant permissions to the application
4. The token will be stored locally for future use

## 📚 Available Features

Once connected, you can:
- **Accounts Payable**: Manage vendors, bills, and payments
- **Accounts Receivable**: Handle customers, invoices, and receipts  
- **General Ledger**: Work with journal entries, accounts, and reports
- **Cross-Module**: Get financial summaries across all modules

## 🎯 Next Steps

1. Add your OAuth credentials to `.env`
2. Install dependencies if not already done
3. Restart Claude Desktop
4. Start using Intacct tools in your conversations!

## 💡 Example Prompts

Once connected, try these:
- "Show me all pending bills in Accounts Payable"
- "Get the current AR aging report"
- "Create a journal entry in the General Ledger"
- "Give me a financial overview across all modules"
