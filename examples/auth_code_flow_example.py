"""OAuth 2.0 Authorization Code Flow Example"""

import asyncio
from src.auth import AuthConfig, AuthorizationCodeFlow


async def main():
    # Configure authentication
    config = AuthConfig(
        client_id="your_client_id",
        client_secret="your_client_secret", 
        redirect_uri="http://localhost:8080/callback",
        scope="admin:*"
    )
    
    # Initialize flow and generate auth URL
    flow = AuthorizationCodeFlow()
    auth_request = flow.get_auth_url(config)
    
    print("=== Authorization Code Flow ===")
    print(f"\n1. Open URL in browser:\n{auth_request.authorization_url}")
    print(f"\n2. Save these values:")
    print(f"   State: {auth_request.state}")
    print(f"   Code Verifier: {auth_request.code_verifier}")
    
    # After user authorizes, exchange code for tokens
    print("\n3. After authorization, enter values from callback URL:")
    code = input("   Authorization code: ")
    state = input("   State parameter: ")
    
    try:
        token_response = await flow.exchange_code(code, state)
        print(f"\n✅ Success! Access token: {token_response.access_token[:20]}...")
    except Exception as e:
        print(f"\n❌ Error: {e}")
    finally:
        await flow.close()


if __name__ == "__main__":
    asyncio.run(main())
