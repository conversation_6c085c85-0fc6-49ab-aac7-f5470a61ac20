"""OAuth 2.0 Client Credentials Flow Example"""

import asyncio
from src.auth import AuthConfig, ClientCredentialsFlow


async def main():
    # Configure authentication for server-to-server
    config = AuthConfig(
        client_id="your_client_id",
        client_secret="your_client_secret",
        scope="api",  # Request specific scopes
        # Optional: Web Services credentials
        web_services_user="ws_user",
        web_services_password="ws_password",
        company_id="DEMO01"
    )
    
    # Initialize flow
    flow = ClientCredentialsFlow()
    
    try:
        # Authenticate directly (no user interaction)
        print("=== Client Credentials Flow ===")
        print("Authenticating with Sage Intacct...")
        
        token_response = await flow.authenticate(config)
        
        print("\n✅ Authentication Successful!")
        print(f"Access Token: {token_response.access_token[:20]}...")
        print(f"Token Type: {token_response.token_type}")
        print(f"Expires In: {token_response.expires_in} seconds")
        print(f"Scope: {token_response.scope}")
        
        # Use the access token for API calls
        # headers = {"Authorization": f"Bearer {token_response.access_token}"}
        
    except Exception as e:
        print(f"\n❌ Authentication Error: {e}")
    finally:
        await flow.close()


if __name__ == "__main__":
    asyncio.run(main())
