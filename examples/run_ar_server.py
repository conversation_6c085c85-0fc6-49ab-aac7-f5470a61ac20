"""
Example script for running the Accounts Receivable MCP server.

This demonstrates how to configure and run the AR server with authentication.
"""

import os
import logging
from dotenv import load_dotenv

from src.servers.ar import AccountsReceivableServer
from src.auth.interfaces import AuthConfig

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Load environment variables
load_dotenv()


def main():
    """Run the Accounts Receivable server."""
    # Create auth configuration from environment
    auth_config = AuthConfig(
        sender_id=os.getenv("INTACCT_SENDER_ID"),
        sender_password=os.getenv("INTACCT_SENDER_PASSWORD"),
        user_id=os.getenv("INTACCT_USER_ID"),
        company_id=os.getenv("INTACCT_COMPANY_ID"),
        user_password=os.getenv("INTACCT_USER_PASSWORD"),
        client_id=os.getenv("INTACCT_CLIENT_ID"),
        client_secret=os.getenv("INTACCT_CLIENT_SECRET"),
        redirect_uri=os.getenv("INTACCT_REDIRECT_URI", "http://localhost:8080/callback"),
        scope=os.getenv("INTACCT_SCOPE", "admin"),
        entity_id=os.getenv("INTACCT_ENTITY_ID"),
    )
    
    # Create and run the server
    server = AccountsReceivableServer(auth_config=auth_config)
    
    print("Starting Accounts Receivable MCP Server...")
    print("Server will be available at stdio transport")
    print("\nAvailable custom tools:")
    print("  - ar_health_check: Check server health")
    print("  - ar_dashboard: Get AR metrics dashboard")
    print("  - ar_aging_report: Generate aging report")
    print("\nPress Ctrl+C to stop")
    
    try:
        server.run()
    except KeyboardInterrupt:
        print("\nShutting down server...")


if __name__ == "__main__":
    main()
