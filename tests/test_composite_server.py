"""
Test suite for the composite MCP server.
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from pathlib import Path

from src.servers.composite import IntacctMCPServer, ModuleInfo, create_composite_server
from src.auth.interfaces import AuthConfig


class TestCompositeServer:
    """Test the composite server functionality."""
    
    @pytest.fixture
    def auth_config(self):
        """Create test auth configuration."""
        return AuthConfig(
            client_id="test_client",
            client_secret="test_secret",
            redirect_uri="http://localhost:8080/callback",
            scope="full",
            authorization_url="https://test.com/authorize",
            token_url="https://test.com/token"
        )
    
    def test_server_initialization(self, auth_config):
        """Test composite server initialization."""
        server = IntacctMCPServer(
            auth_config=auth_config,
            enabled_modules=["ap", "ar"]
        )
        
        assert server.name == "sage-intacct-mcp"
        assert server.version == "1.0.0"
        assert server.enabled_modules == ["ap", "ar"]
        assert server.auth_config == auth_config
    
    def test_available_modules(self):
        """Test that all expected modules are available."""
        server = IntacctMCPServer()
        
        assert "ap" in server.AVAILABLE_MODULES
        assert "ar" in server.AVAILABLE_MODULES
        assert "gl" in server.AVAILABLE_MODULES
        
        # Check module details
        ap_module = server.AVAILABLE_MODULES["ap"]
        assert ap_module["class"] == "AccountsPayableServer"
        assert ap_module["display_name"] == "Accounts Payable"
    
    @pytest.mark.asyncio
    async def test_module_loading(self, auth_config):
        """Test dynamic module loading."""
        server = IntacctMCPServer(
            auth_config=auth_config,
            enabled_modules=["ap"]
        )
        
        # Mock the module import
        with patch('importlib.import_module') as mock_import:
            mock_module = Mock()
            mock_server_class = Mock()
            mock_server_class.return_value = Mock()
            
            mock_module.AccountsPayableServer = mock_server_class
            mock_import.return_value = mock_module
            
            await server._load_module("ap")
            
            assert "ap" in server.modules
            assert server.modules["ap"].enabled
            assert server.modules["ap"].instance is not None
    
    def test_cross_module_tools_initialization(self):
        """Test that cross-module tools are properly initialized."""
        server = IntacctMCPServer()
        
        assert hasattr(server, 'cross_module_tools')
        assert server.cross_module_tools is not None
    
    @pytest.mark.asyncio
    async def test_composite_health_check(self, auth_config):
        """Test the composite health check functionality."""
        server = IntacctMCPServer(auth_config=auth_config)
        
        # Add some mock modules
        server.modules["ap"] = ModuleInfo(
            name="ap",
            server_class=Mock,
            enabled=True
        )
        server.modules["ap"].instance = Mock()
        
        server.modules["ar"] = ModuleInfo(
            name="ar",
            server_class=Mock,
            enabled=True
        )
        server.modules["ar"].instance = Mock()
        
        # Mock the health check tool
        server._add_composite_health_check()
        
        # Verify health check structure
        # Note: We can't easily test the actual tool execution without
        # running the full FastMCP server
    
    def test_create_composite_server_factory(self):
        """Test the create_composite_server factory function."""
        with patch('src.utils.config_manager.ConfigManager') as mock_config:
            mock_config.return_value.get.return_value = {
                "client_id": "test",
                "client_secret": "secret"
            }
            
            server = create_composite_server(
                config_path="/test/config.yaml",
                enabled_modules=["ap", "ar"]
            )
            
            assert isinstance(server, IntacctMCPServer)
            assert server.enabled_modules == ["ap", "ar"]
