"""
Tests for the General Ledger MCP server.
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch
from pathlib import Path

from src.servers.gl import GeneralLedgerServer
from src.auth.interfaces import AuthConfig
from src.servers.core import IntacctServerError


@pytest.fixture
def auth_config():
    """Create test auth configuration."""
    return AuthConfig(
        sender_id="test_sender",
        sender_password="test_password",
        user_id="test_user",
        company_id="test_company",
        user_password="test_user_pass",
        client_id="test_client",
        client_secret="test_secret",
        redirect_uri="http://localhost:8080/callback",
        scope="admin"
    )


@pytest.fixture
def mock_token_manager():
    """Create mock token manager."""
    manager = Mock()
    manager.get_token = AsyncMock(return_value="test_token")
    manager.save_token = AsyncMock()
    return manager


@pytest.fixture
def gl_server(auth_config, mock_token_manager):
    """Create GL server instance."""
    return GeneralLedgerServer(
        auth_config=auth_config,
        token_manager=mock_token_manager
    )


class TestGeneralLedgerServer:
    """Test cases for GeneralLedgerServer."""
    
    def test_initialization(self, gl_server, auth_config):
        """Test server initialization."""
        assert gl_server.name == "sage-intacct-gl"
        assert gl_server.version == "1.0.0"
        assert gl_server.auth_config == auth_config
        assert gl_server.mcp_server is None
        
        # Check spec path
        assert gl_server.spec_path.name == "general-ledger.openapi.yaml"
        assert gl_server.spec_path.parent.name == "openapi"
    
    def test_route_maps(self, gl_server):
        """Test custom route maps configuration."""
        assert len(gl_server.route_maps) >= 10
        
        # Check journal entries route map (should be tools)
        journal_map = gl_server.route_maps[0]
        assert journal_map.methods == ["*"]
        assert "journal" in journal_map.pattern
        
        # Check accounts route map (should be resources for GET)
        accounts_map = gl_server.route_maps[1]
        assert accounts_map.methods == ["GET"]
        assert "account" in accounts_map.pattern
        
        # Check budgets route map (tools for modifications)
        budgets_map = gl_server.route_maps[3]
        assert budgets_map.methods == ["POST", "PUT", "PATCH", "DELETE"]
        assert "budget" in budgets_map.pattern
        
        # Check statistical route map
        statistical_map = gl_server.route_maps[4]
        assert statistical_map.methods == ["*"]
        assert "statistical" in statistical_map.pattern
    
    def test_mcp_names(self, gl_server):
        """Test component name mappings."""
        # Journal entries
        assert gl_server.mcp_names["list_general_ledger_journal"] == "list_journals"
        assert gl_server.mcp_names["create_general_ledger_journal"] == "create_journal"
        
        # Accounts
        assert gl_server.mcp_names["list_general_ledger_account"] == "list_accounts"
        assert gl_server.mcp_names["get_general_ledger_account"] == "get_account"
        
        # Budgets
        assert gl_server.mcp_names["get_general_ledger_budget"] == "get_budget"
        assert gl_server.mcp_names["create_general_ledger_budget"] == "create_budget"
        
        # Statistical journals
        assert gl_server.mcp_names["list_general_ledger_statistical_journal"] == "list_statistical_journals"
    
    @pytest.mark.asyncio
    async def test_initialize_no_spec_file(self, gl_server):
        """Test initialization warns when spec file is missing."""
        # Mock spec path to not exist
        gl_server.spec_path = Path("/nonexistent/spec.yaml")
        
        # Unlike AR/AP, GL server should handle missing spec gracefully
        mcp = await gl_server.initialize_server()
        assert mcp is not None
        assert mcp.name == "sage-intacct-gl"
    
    @pytest.mark.asyncio
    async def test_initialize_with_spec(self, gl_server, mock_token_manager):
        """Test successful initialization with OpenAPI spec."""
        # Mock the spec path to exist
        with patch.object(Path, 'exists', return_value=True):
            # Mock the authenticated client
            mock_client = AsyncMock()
            mock_token_manager.get_token.return_value = "test_token"
            
            with patch.object(gl_server, 'get_authenticated_client', return_value=mock_client):
                # Mock the from_openapi method
                with patch('fastmcp.FastMCP.from_openapi') as mock_from_openapi:
                    mock_mcp = Mock()
                    mock_mcp._tools = {"test_tool": Mock()}
                    mock_mcp._resources = {"test_resource": Mock()}
                    mock_from_openapi.return_value = mock_mcp
                    
                    # Initialize server
                    mcp = await gl_server.initialize_server()
                    
                    # Verify initialization
                    assert mcp is not None
                    assert gl_server.mcp_server is not None
    
    def test_configure_from_openapi(self, gl_server):
        """Test OpenAPI configuration extraction."""
        # Mock client
        mock_client = Mock()
        
        # Mock FastMCP and its methods
        with patch('fastmcp.FastMCP') as mock_fastmcp_class:
            mock_mcp = Mock()
            mock_mcp._tools = {
                "create_journal": Mock(),
                "list_accounts": Mock()
            }
            mock_mcp._resources = {
                "account_balance": Mock(),
                "trial_balance": Mock()
            }
            mock_fastmcp_class.return_value = mock_mcp
            
            # Call configure method
            tools, resources = gl_server._configure_from_openapi(mock_client)
            
            # Verify results
            assert len(tools) == 2
            assert len(resources) == 2
            assert any(t.name == "create_journal" for t in tools)
            assert any(r.uri == "account_balance" for r in resources)
