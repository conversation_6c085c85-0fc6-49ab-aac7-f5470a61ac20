"""
Tests for the Accounts Payable MCP server.
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch
from pathlib import Path

from src.servers.ap import AccountsPayableServer
from src.auth.interfaces import AuthConfig
from src.servers.core import IntacctServerError


@pytest.fixture
def auth_config():
    """Create test auth configuration."""
    return AuthConfig(
        sender_id="test_sender",
        sender_password="test_password",
        user_id="test_user",
        company_id="test_company",
        user_password="test_user_pass",
        client_id="test_client",
        client_secret="test_secret",
        redirect_uri="http://localhost:8080/callback",
        scope="admin"
    )


@pytest.fixture
def mock_token_manager():
    """Create mock token manager."""
    manager = Mock()
    manager.get_token = AsyncMock(return_value="test_token")
    manager.save_token = AsyncMock()
    return manager


@pytest.fixture
def ap_server(auth_config, mock_token_manager):
    """Create AP server instance."""
    return AccountsPayableServer(
        auth_config=auth_config,
        token_manager=mock_token_manager
    )


class TestAccountsPayableServer:
    """Test cases for AccountsPayableServer."""
    
    def test_initialization(self, ap_server, auth_config):
        """Test server initialization."""
        assert ap_server.name == "sage-intacct-ap"
        assert ap_server.version == "1.0.0"
        assert ap_server.auth_config == auth_config
        assert ap_server.mcp_server is None
        
        # Check spec path
        assert ap_server.spec_path.name == "accounts-payable.openapi.yaml"
        assert ap_server.spec_path.parent.name == "openapi"
    
    def test_route_maps(self, ap_server):
        """Test custom route maps configuration."""
        assert len(ap_server.route_maps) == 5
        
        # Check bills route map
        bills_map = ap_server.route_maps[0]
        assert bills_map.methods == ["GET"]
        assert "bills" in bills_map.pattern
        
        # Check payments route map
        payments_map = ap_server.route_maps[1]
        assert payments_map.methods == ["*"]
        assert "payment" in payments_map.pattern
    
    def test_mcp_names(self, ap_server):
        """Test component name mappings."""
        assert ap_server.mcp_names["list_accounts_payable_bill"] == "list_bills"
        assert ap_server.mcp_names["create_accounts_payable_payment"] == "create_payment"
        assert ap_server.mcp_names["get_accounts_payable_vendor"] == "get_vendor"
    
    @pytest.mark.asyncio
    async def test_initialize_no_spec_file(self, ap_server):
        """Test initialization fails when spec file is missing."""
        # Mock spec path to not exist
        ap_server.spec_path = Path("/nonexistent/spec.yaml")
        
        with pytest.raises(IntacctServerError, match="OpenAPI spec not found"):
            await ap_server.initialize()
    
    @pytest.mark.asyncio
    @patch('src.servers.ap.accounts_payable.FastMCP.from_openapi')
    @patch('builtins.open', create=True)
    @patch('yaml.safe_load')
    async def test_initialize_success(
        self, 
        mock_yaml_load,
        mock_open,
        mock_from_openapi,
        ap_server
    ):
        """Test successful initialization."""
        # Mock spec loading
        mock_yaml_load.return_value = {"openapi": "3.0.0", "info": {"title": "Test"}}
        
        # Mock auth manager
        ap_server.auth_manager = Mock()
        ap_server.auth_manager.get_authenticated_client = AsyncMock(
            return_value=Mock(spec=["get", "post"])
        )
        
        # Mock FastMCP creation
        mock_mcp = Mock()
        mock_mcp.tool = Mock(return_value=lambda f: f)
        mock_from_openapi.return_value = mock_mcp
        
        # Initialize
        await ap_server.initialize()
        
        # Verify MCP server was created
        assert ap_server.mcp_server == mock_mcp
        mock_from_openapi.assert_called_once()
        
        # Check call arguments
        call_kwargs = mock_from_openapi.call_args.kwargs
        assert call_kwargs["name"] == "sage-intacct-ap"
        assert call_kwargs["timeout"] == 30.0
        assert call_kwargs["route_maps"] == ap_server.route_maps
        assert call_kwargs["mcp_names"] == ap_server.mcp_names
    
    def test_customize_component(self, ap_server):
        """Test component customization."""
        from fastmcp.server.openapi import HTTPRoute, OpenAPITool
        
        # Create mock route and component
        route = HTTPRoute(path="/objects/accounts-payable.bill", method="GET", tags=set())
        component = OpenAPITool(
            name="test_tool",
            description="Test description",
            tags=set(),
            handler=Mock()
        )
        
        # Customize
        ap_server._customize_component(route, component)
        
        # Check customizations
        assert "accounts-payable" in component.tags
        assert "bills" in component.tags
        assert component.description.startswith("🔧 AP Operation:")
    
    @pytest.mark.asyncio
    async def test_run_async(self, ap_server):
        """Test async run method."""
        # Mock initialization and MCP server
        ap_server.initialize = AsyncMock()
        mock_mcp = Mock()
        mock_mcp.run_async = AsyncMock()
        ap_server.mcp_server = mock_mcp
        
        # Run
        await ap_server.run_async()
        
        # Verify calls
        ap_server.initialize.assert_called_once()
        mock_mcp.run_async.assert_called_once()
