"""
Tests for the Accounts Receivable MCP server.
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch
from pathlib import Path

from src.servers.ar import AccountsReceivableServer
from src.auth.interfaces import AuthConfig
from src.servers.core import IntacctServerError


@pytest.fixture
def auth_config():
    """Create test auth configuration."""
    return AuthConfig(
        sender_id="test_sender",
        sender_password="test_password",
        user_id="test_user",
        company_id="test_company",
        user_password="test_user_pass",
        client_id="test_client",
        client_secret="test_secret",
        redirect_uri="http://localhost:8080/callback",
        scope="admin"
    )


@pytest.fixture
def mock_token_manager():
    """Create mock token manager."""
    manager = Mock()
    manager.get_token = AsyncMock(return_value="test_token")
    manager.save_token = AsyncMock()
    return manager


@pytest.fixture
def ar_server(auth_config, mock_token_manager):
    """Create AR server instance."""
    return AccountsReceivableServer(
        auth_config=auth_config,
        token_manager=mock_token_manager
    )


class TestAccountsReceivableServer:
    """Test cases for AccountsReceivableServer."""
    
    def test_initialization(self, ar_server, auth_config):
        """Test server initialization."""
        assert ar_server.name == "sage-intacct-ar"
        assert ar_server.version == "1.0.0"
        assert ar_server.auth_config == auth_config
        assert ar_server.mcp_server is None
        
        # Check spec path
        assert ar_server.spec_path.name == "accounts-receivable.openapi.yaml"
        assert ar_server.spec_path.parent.name == "openapi"
    
    def test_route_maps(self, ar_server):
        """Test custom route maps configuration."""
        assert len(ar_server.route_maps) == 6
        
        # Check invoices route map
        invoices_map = ar_server.route_maps[0]
        assert invoices_map.methods == ["GET"]
        assert "invoice" in invoices_map.pattern
        
        # Check payments route map
        payments_map = ar_server.route_maps[1]
        assert payments_map.methods == ["*"]
        assert "payment" in payments_map.pattern
        
        # Check adjustments route map
        adjustments_map = ar_server.route_maps[3]
        assert adjustments_map.methods == ["*"]
        assert "adjustment" in adjustments_map.pattern
    
    def test_mcp_names(self, ar_server):
        """Test component name mappings."""
        # Invoices
        assert ar_server.mcp_names["list_accounts_receivable_invoice"] == "list_invoices"
        assert ar_server.mcp_names["create_accounts_receivable_invoice"] == "create_invoice"
        
        # Payments (called receipts in AR)
        assert ar_server.mcp_names["list_accounts_receivable_payment"] == "list_receipts"
        assert ar_server.mcp_names["create_accounts_receivable_payment"] == "create_receipt"
        
        # Customers
        assert ar_server.mcp_names["get_accounts_receivable_customer"] == "get_customer"
    
    @pytest.mark.asyncio
    async def test_initialize_no_spec_file(self, ar_server):
        """Test initialization fails when spec file is missing."""
        # Mock spec path to not exist
        ar_server.spec_path = Path("/nonexistent/spec.yaml")
        
        with pytest.raises(IntacctServerError, match="OpenAPI spec not found"):
            await ar_server.initialize()
    
    @pytest.mark.asyncio
    @patch('src.servers.ar.accounts_receivable.FastMCP.from_openapi')
    @patch('builtins.open', create=True)
    @patch('yaml.safe_load')
    async def test_initialize_success(
        self, 
        mock_yaml_load,
        mock_open,
        mock_from_openapi,
        ar_server
    ):
        """Test successful initialization."""
        # Mock spec loading
        mock_yaml_load.return_value = {"openapi": "3.0.0", "info": {"title": "Test AR"}}
        
        # Mock auth manager
        ar_server.auth_manager = Mock()
        ar_server.auth_manager.get_authenticated_client = AsyncMock(
            return_value=Mock(spec=["get", "post"])
        )
        
        # Mock FastMCP creation
        mock_mcp = Mock()
        mock_mcp.tool = Mock(return_value=lambda f: f)
        mock_from_openapi.return_value = mock_mcp
        
        # Initialize
        await ar_server.initialize()
        
        # Verify MCP server was created
        assert ar_server.mcp_server == mock_mcp
        mock_from_openapi.assert_called_once()
        
        # Check call arguments
        call_kwargs = mock_from_openapi.call_args.kwargs
        assert call_kwargs["name"] == "sage-intacct-ar"
        assert call_kwargs["timeout"] == 30.0
        assert call_kwargs["route_maps"] == ar_server.route_maps
        assert call_kwargs["mcp_names"] == ar_server.mcp_names
    
    def test_customize_component(self, ar_server):
        """Test component customization."""
        from fastmcp.server.openapi import HTTPRoute, OpenAPITool, OpenAPIResource
        
        # Test tool customization
        route = HTTPRoute(path="/objects/accounts-receivable.invoice", method="POST", tags=set())
        tool = OpenAPITool(
            name="test_tool",
            description="Test description",
            tags=set(),
            handler=Mock()
        )
        
        ar_server._customize_component(route, tool)
        
        assert "accounts-receivable" in tool.tags
        assert "invoices" in tool.tags
        assert tool.description.startswith("💰 AR Operation:")
        
        # Test resource customization
        resource = OpenAPIResource(
            name="test_resource",
            description="Test resource",
            tags=set(),
            handler=Mock()
        )
        
        ar_server._customize_component(route, resource)
        
        assert "accounts-receivable" in resource.tags
        assert "ar-data" in resource.tags
        assert resource.description.startswith("📊 AR Data:")
    
    def test_custom_tools_added(self, ar_server):
        """Test that custom tools are defined."""
        # Check that custom tool methods exist
        assert hasattr(ar_server._add_custom_tools, '__call__')
        
        # When initialized, the server should have these custom tools
        # ar_health_check, ar_dashboard, ar_aging_report
        # (These would be tested more thoroughly with integration tests)
    
    @pytest.mark.asyncio
    async def test_run_async(self, ar_server):
        """Test async run method."""
        # Mock initialization and MCP server
        ar_server.initialize = AsyncMock()
        mock_mcp = Mock()
        mock_mcp.run_async = AsyncMock()
        ar_server.mcp_server = mock_mcp
        
        # Run
        await ar_server.run_async()
        
        # Verify calls
        ar_server.initialize.assert_called_once()
        mock_mcp.run_async.assert_called_once()
