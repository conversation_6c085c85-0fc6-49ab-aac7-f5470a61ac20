"""
Tests for base server architecture.
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch
from pathlib import Path
import tempfile
import yaml

from src.servers.core import BaseIntacctServer, IntacctServerError
from src.auth.interfaces import AuthConfig
from src.auth.token_manager import TokenManager
from src.utils.openapi_loader import OpenAPISpec
from src.utils.route_mapper import EndpointCategory


@pytest.fixture
def auth_config():
    """Create test auth configuration."""
    return AuthConfig(
        client_id="test_client_id",
        client_secret="test_client_secret",
        company_id="test_company",
        auth_type="client_credentials"
    )


@pytest.fixture
def base_server(auth_config):
    """Create base server instance."""
    return BaseIntacctServer(
        name="test-server",
        version="1.0.0",
        auth_config=auth_config
    )


@pytest.fixture
def sample_openapi_spec():
    """Create a sample OpenAPI spec for testing."""
    return {
        "openapi": "3.0.0",
        "info": {
            "title": "Test API",
            "version": "1.0.0"
        },
        "servers": [
            {"url": "https://api.intacct.com"}
        ],
        "paths": {
            "/services/api/accounts": {
                "get": {
                    "summary": "List accounts",
                    "operationId": "listAccounts",
                    "responses": {
                        "200": {"description": "Success"}
                    }
                }
            }
        }
    }


class TestBaseIntacctServer:
    """Test base server functionality."""
    
    def test_initialization(self, auth_config):
        """Test server initialization."""
        server = BaseIntacctServer(
            name="test-server",
            version="2.0.0",
            auth_config=auth_config
        )
        
        assert server.name == "test-server"
        assert server.version == "2.0.0"
        assert server.auth_config == auth_config
        assert server.token_manager is not None
        assert server.server is not None
        assert server.route_mapper is not None
    
    def test_initialization_with_token_manager(self, auth_config):
        """Test initialization with custom token manager."""
        custom_token_manager = TokenManager()
        
        server = BaseIntacctServer(
            name="test-server",
            version="1.0.0",
            auth_config=auth_config,
            token_manager=custom_token_manager
        )
        
        assert server.token_manager is custom_token_manager
    
    @pytest.mark.asyncio
    async def test_initialize_auth_success(self, base_server):
        """Test successful auth initialization."""
        with patch("src.servers.core.IntacctAuthManager") as mock_auth_manager_class:
            mock_auth_manager = AsyncMock()
            mock_auth_manager.get_authenticated_client = AsyncMock()
            mock_auth_manager_class.return_value = mock_auth_manager
            
            await base_server.initialize_auth()
            
            assert base_server.auth_manager is not None
            mock_auth_manager.get_authenticated_client.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_initialize_auth_no_config(self):
        """Test auth initialization without config."""
        server = BaseIntacctServer(
            name="test-server",
            version="1.0.0"
        )
        
        with pytest.raises(IntacctServerError, match="No authentication configuration"):
            await server.initialize_auth()
    
    @pytest.mark.asyncio
    async def test_initialize_auth_failure(self, base_server):
        """Test auth initialization failure."""
        with patch("src.servers.core.IntacctAuthManager") as mock_auth_manager_class:
            mock_auth_manager = AsyncMock()
            mock_auth_manager.get_authenticated_client = AsyncMock(
                side_effect=Exception("Auth failed")
            )
            mock_auth_manager_class.return_value = mock_auth_manager
            
            with pytest.raises(IntacctServerError, match="Authentication failed"):
                await base_server.initialize_auth()
    
    def test_load_openapi_spec(self, base_server, sample_openapi_spec):
        """Test loading OpenAPI spec."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(sample_openapi_spec, f)
            spec_path = f.name
        
        try:
            with patch("src.servers.core.OpenAPILoader") as mock_loader_class:
                mock_loader = Mock()
                mock_spec = OpenAPISpec(spec=sample_openapi_spec)
                mock_loader.load_spec.return_value = mock_spec
                mock_loader_class.return_value = mock_loader
                
                loaded_spec = base_server.load_openapi_spec(spec_path)
                
                assert loaded_spec == mock_spec
                assert loaded_spec in base_server.loaded_specs
                mock_loader.load_spec.assert_called_once()
        finally:
            Path(spec_path).unlink()
    
    def test_load_openapi_spec_with_base_url(self, base_server, sample_openapi_spec):
        """Test loading OpenAPI spec with base URL override."""
        custom_url = "https://custom.intacct.com"
        
        with patch("src.servers.core.OpenAPILoader") as mock_loader_class:
            mock_loader = Mock()
            mock_spec = OpenAPISpec(spec=sample_openapi_spec.copy())
            mock_loader.load_spec.return_value = mock_spec
            mock_loader_class.return_value = mock_loader
            
            loaded_spec = base_server.load_openapi_spec("test.yaml", base_url=custom_url)
            
            assert loaded_spec.servers[0]["url"] == custom_url
    
    def test_create_tools_from_spec(self, base_server, sample_openapi_spec):
        """Test creating tools from OpenAPI spec."""
        spec = OpenAPISpec(spec=sample_openapi_spec)
        
        with patch.object(base_server.route_mapper, 'categorize_operations') as mock_categorize:
            mock_categorize.return_value = {
                EndpointCategory.ACCOUNTING: [
                    {"_path": "/services/api/accounts", "_method": "GET"}
                ]
            }
            
            tools = base_server.create_tools_from_spec(spec)
            
            # In our implementation, _create_tool_from_operation returns None
            # So tools list should be empty for now
            assert tools == []
    
    def test_create_tools_with_category_filter(self, base_server, sample_openapi_spec):
        """Test creating tools with category filters."""
        spec = OpenAPISpec(spec=sample_openapi_spec)
        
        with patch.object(base_server.route_mapper, 'categorize_operations') as mock_categorize:
            mock_categorize.return_value = {
                EndpointCategory.ACCOUNTING: [{"_path": "/accounts", "_method": "GET"}],
                EndpointCategory.CONTACTS: [{"_path": "/contacts", "_method": "GET"}],
                EndpointCategory.SYSTEM: [{"_path": "/system", "_method": "GET"}]
            }
            
            # Include only accounting
            tools = base_server.create_tools_from_spec(
                spec,
                include_categories=[EndpointCategory.ACCOUNTING]
            )
            
            # Should process only accounting category
            # (though tools list is empty due to None return)
            assert tools == []
            
            # Exclude system
            tools = base_server.create_tools_from_spec(
                spec,
                exclude_categories=[EndpointCategory.SYSTEM]
            )
            
            # Should process accounting and contacts
            assert tools == []
    
    def test_add_health_check(self, base_server):
        """Test adding health check endpoint."""
        base_server.add_health_check()
        
        # Check that health_check tool was added
        # This is a bit tricky to test without running the server
        # We'd need to inspect the FastMCP server's registered tools
        assert True  # Placeholder for now
    
    @pytest.mark.asyncio
    async def test_lifespan_with_auth(self, base_server):
        """Test server lifespan with auth initialization."""
        with patch.object(base_server, 'initialize_auth') as mock_init_auth:
            mock_init_auth.return_value = None
            
            async with base_server.lifespan():
                pass
            
            mock_init_auth.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_lifespan_without_auth(self):
        """Test server lifespan without auth config."""
        server = BaseIntacctServer(name="test-server", version="1.0.0")
        
        # Should not raise, just continue without auth
        async with server.lifespan():
            pass
    
    @pytest.mark.asyncio
    async def test_lifespan_auth_failure(self, base_server):
        """Test server lifespan with auth failure."""
        with patch.object(base_server, 'initialize_auth') as mock_init_auth:
            mock_init_auth.side_effect = Exception("Auth failed")
            
            # Should not raise, just log error
            async with base_server.lifespan():
                pass
