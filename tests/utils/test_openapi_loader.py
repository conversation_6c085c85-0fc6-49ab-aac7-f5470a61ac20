"""
Tests for OpenAPI specification loader.
"""

import pytest
from pathlib import Path
import yaml
import json
import tempfile
from typing import Dict, Any

from src.utils.openapi_loader import (
    OpenAPILoader,
    OpenAPISpec,
    validate_openapi_spec,
    merge_openapi_specs,
)


@pytest.fixture
def sample_openapi_spec() -> Dict[str, Any]:
    """Create a sample OpenAPI specification."""
    return {
        "openapi": "3.0.0",
        "info": {
            "title": "Test API",
            "version": "1.0.0",
            "description": "Test API description"
        },
        "servers": [
            {"url": "https://api.example.com/v1"}
        ],
        "paths": {
            "/test": {
                "get": {
                    "summary": "Test endpoint",
                    "operationId": "getTest",
                    "responses": {
                        "200": {
                            "description": "Success",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "$ref": "#/components/schemas/TestResponse"
                                    }
                                }
                            }
                        }
                    }
                }
            }
        },
        "components": {
            "schemas": {
                "TestResponse": {
                    "type": "object",
                    "properties": {
                        "id": {"type": "string"},
                        "name": {"type": "string"}
                    }
                }
            }
        }
    }


@pytest.fixture
def sample_openapi_spec_2() -> Dict[str, Any]:
    """Create a second sample OpenAPI specification."""
    return {
        "openapi": "3.0.0",
        "info": {
            "title": "Test API 2",
            "version": "2.0.0",
            "description": "Second test API"
        },
        "servers": [
            {"url": "https://api.example.com/v2"}
        ],
        "paths": {
            "/users": {
                "get": {
                    "summary": "Get users",
                    "operationId": "getUsers",
                    "responses": {
                        "200": {
                            "description": "Success"
                        }
                    }
                }
            }
        },
        "components": {
            "schemas": {
                "User": {
                    "type": "object",
                    "properties": {
                        "id": {"type": "string"},
                        "email": {"type": "string"}
                    }
                }
            }
        }
    }


@pytest.fixture
def temp_spec_files(sample_openapi_spec, sample_openapi_spec_2):
    """Create temporary spec files."""
    with tempfile.TemporaryDirectory() as tmpdir:
        spec_dir = Path(tmpdir)
        
        # Write YAML spec
        yaml_file = spec_dir / "test.yaml"
        with open(yaml_file, 'w') as f:
            yaml.dump(sample_openapi_spec, f)
        
        # Write JSON spec
        json_file = spec_dir / "test2.json"
        with open(json_file, 'w') as f:
            json.dump(sample_openapi_spec_2, f)
            
        yield spec_dir, yaml_file, json_file


class TestOpenAPILoader:
    """Test the OpenAPI loader functionality."""
    
    def test_init_with_base_path(self):
        """Test loader initialization with base path."""
        loader = OpenAPILoader("/test/path")
        assert loader.base_path == Path("/test/path")
    
    def test_init_without_base_path(self):
        """Test loader initialization without base path."""
        loader = OpenAPILoader()
        assert loader.base_path == Path.cwd()
    
    def test_load_yaml_spec(self, temp_spec_files):
        """Test loading a YAML spec."""
        spec_dir, yaml_file, _ = temp_spec_files
        loader = OpenAPILoader(spec_dir)
        
        spec = loader.load_spec("test.yaml")
        
        assert isinstance(spec, OpenAPISpec)
        assert spec.openapi_version == "3.0.0"
        assert spec.info["title"] == "Test API"
        assert "/test" in spec.paths
        assert spec.source_path == yaml_file
    
    def test_load_json_spec(self, temp_spec_files):
        """Test loading a JSON spec."""
        spec_dir, _, json_file = temp_spec_files
        loader = OpenAPILoader(spec_dir)
        
        spec = loader.load_spec("test2.json")
        
        assert isinstance(spec, OpenAPISpec)
        assert spec.info["title"] == "Test API 2"
        assert "/users" in spec.paths
    
    def test_load_spec_not_found(self):
        """Test loading non-existent spec."""
        loader = OpenAPILoader()
        
        with pytest.raises(FileNotFoundError):
            loader.load_spec("missing.yaml")
    
    def test_load_spec_invalid_format(self, temp_spec_files):
        """Test loading spec with unsupported format."""
        spec_dir, _, _ = temp_spec_files
        loader = OpenAPILoader(spec_dir)
        
        # Create a .txt file
        txt_file = spec_dir / "test.txt"
        txt_file.write_text("Not a spec")
        
        with pytest.raises(ValueError, match="Unsupported file format"):
            loader.load_spec("test.txt")
    
    def test_load_multiple_specs(self, temp_spec_files):
        """Test loading multiple specs."""
        spec_dir, _, _ = temp_spec_files
        loader = OpenAPILoader(spec_dir)
        
        specs = loader.load_multiple(["test.yaml", "test2.json"])
        
        assert len(specs) == 2
        assert specs[0].info["title"] == "Test API"
        assert specs[1].info["title"] == "Test API 2"
    
    def test_spec_operations(self, sample_openapi_spec):
        """Test extracting operations from spec."""
        spec = OpenAPISpec(spec=sample_openapi_spec)
        
        operations = spec.get_operations()
        
        assert len(operations) == 1
        assert operations[0]["_path"] == "/test"
        assert operations[0]["_method"] == "GET"
        assert operations[0]["summary"] == "Test endpoint"


class TestOpenAPISpecValidation:
    """Test OpenAPI spec validation."""
    
    def test_validate_valid_spec(self, sample_openapi_spec):
        """Test validating a valid spec."""
        spec = OpenAPISpec(spec=sample_openapi_spec)
        
        # Should not raise
        validate_openapi_spec(spec)
    
    def test_validate_missing_openapi_version(self):
        """Test validation with missing openapi field."""
        spec = OpenAPISpec(spec={"info": {"title": "Test"}})
        
        with pytest.raises(ValueError, match="Missing 'openapi' field"):
            validate_openapi_spec(spec)
    
    def test_validate_missing_info(self):
        """Test validation with missing info field."""
        spec = OpenAPISpec(spec={"openapi": "3.0.0"})
        
        with pytest.raises(ValueError, match="Missing 'info' field"):
            validate_openapi_spec(spec)
    
    def test_validate_missing_paths(self):
        """Test validation with missing paths field."""
        spec = OpenAPISpec(spec={
            "openapi": "3.0.0",
            "info": {"title": "Test", "version": "1.0"}
        })
        
        with pytest.raises(ValueError, match="Missing 'paths' field"):
            validate_openapi_spec(spec)
    
    def test_validate_unsupported_version(self):
        """Test validation with unsupported OpenAPI version."""
        spec = OpenAPISpec(spec={
            "openapi": "2.0",
            "info": {"title": "Test", "version": "1.0"},
            "paths": {}
        })
        
        with pytest.raises(ValueError, match="Unsupported OpenAPI version"):
            validate_openapi_spec(spec)


class TestMergeOpenAPISpecs:
    """Test OpenAPI spec merging."""
    
    def test_merge_empty_list(self):
        """Test merging empty list of specs."""
        with pytest.raises(ValueError, match="No specifications to merge"):
            merge_openapi_specs([])
    
    def test_merge_single_spec(self, sample_openapi_spec):
        """Test merging single spec returns same spec."""
        spec = OpenAPISpec(spec=sample_openapi_spec)
        merged = merge_openapi_specs([spec])
        
        assert merged == spec
    
    def test_merge_multiple_specs(self, sample_openapi_spec, sample_openapi_spec_2):
        """Test merging multiple specs."""
        spec1 = OpenAPISpec(spec=sample_openapi_spec, source_path=Path("spec1.yaml"))
        spec2 = OpenAPISpec(spec=sample_openapi_spec_2, source_path=Path("spec2.yaml"))
        
        merged = merge_openapi_specs([spec1, spec2], title="Merged API")
        
        assert merged.info["title"] == "Merged API"
        assert "/test" in merged.paths
        assert "/users" in merged.paths
        assert "TestResponse" in merged.components["schemas"]
        assert "User" in merged.components["schemas"]
        assert len(merged.servers) == 2
        
        # Check merge metadata
        assert "x-merged-from" in merged.info
        assert len(merged.info["x-merged-from"]) == 2
