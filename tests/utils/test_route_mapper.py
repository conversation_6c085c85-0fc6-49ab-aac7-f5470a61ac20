"""
Tests for route mapping utilities.
"""

import pytest
from src.utils.route_mapper import (
    RouteMapper,
    RouteMappingRule,
    EndpointCategory,
    generate_tool_name,
    categorize_endpoint,
)


class TestRouteMappingRule:
    """Test route mapping rule functionality."""
    
    def test_simple_rule(self):
        """Test a simple mapping rule."""
        rule = RouteMappingRule(
            pattern=r"/services/api/test/?$",
            tool_name_template="{method}_test",
            category=EndpointCategory.SYSTEM
        )
        
        assert rule.matches("/services/api/test")
        assert rule.matches("/services/api/test/")
        assert not rule.matches("/services/api/other")
        
        assert rule.generate_tool_name("/services/api/test", "GET") == "get_test"
        assert rule.generate_tool_name("/services/api/test", "POST") == "create_test"
    
    def test_rule_with_parameters(self):
        """Test a rule with path parameters."""
        rule = RouteMappingRule(
            pattern=r"/services/api/(?P<resource>\w+)/(?P<id>\d+)/?$",
            tool_name_template="{method}_{resource}_by_id",
            category=EndpointCategory.UNKNOWN
        )
        
        assert rule.matches("/services/api/accounts/123")
        assert not rule.matches("/services/api/accounts")
        
        tool_name = rule.generate_tool_name("/services/api/accounts/123", "GET")
        assert tool_name == "get_accounts_by_id"


class TestRouteMapper:
    """Test route mapper functionality."""
    
    def test_default_rules(self):
        """Test that default rules are created."""
        mapper = RouteMapper()
        assert len(mapper.rules) > 0
        
        # Check some default rules exist
        rule_patterns = [rule.pattern for rule in mapper.rules]
        assert any("purchase/invoices" in pattern for pattern in rule_patterns)
        assert any("sales/invoices" in pattern for pattern in rule_patterns)
    
    def test_map_accounts_payable_endpoints(self):
        """Test mapping of accounts payable endpoints."""
        mapper = RouteMapper()
        
        # Purchase invoices
        tool_name, category = mapper.map_endpoint("/services/api/purchase/invoices", "GET")
        assert tool_name == "get_purchase_invoices"
        assert category == EndpointCategory.ACCOUNTS_PAYABLE
        
        tool_name, category = mapper.map_endpoint("/services/api/purchase/invoices/123", "GET")
        assert tool_name == "get_purchase_invoice_by_id"
        assert category == EndpointCategory.ACCOUNTS_PAYABLE
        
        # Purchase credit notes
        tool_name, category = mapper.map_endpoint("/services/api/purchase/credit-notes", "POST")
        assert tool_name == "create_purchase_credit_notes"
        assert category == EndpointCategory.ACCOUNTS_PAYABLE
    
    def test_map_accounts_receivable_endpoints(self):
        """Test mapping of accounts receivable endpoints."""
        mapper = RouteMapper()
        
        # Sales invoices
        tool_name, category = mapper.map_endpoint("/services/api/sales/invoices", "GET")
        assert tool_name == "get_sales_invoices"
        assert category == EndpointCategory.ACCOUNTS_RECEIVABLE
        
        tool_name, category = mapper.map_endpoint("/services/api/sales/invoices/456", "PUT")
        assert tool_name == "update_sales_invoice_by_id"
        assert category == EndpointCategory.ACCOUNTS_RECEIVABLE
    
    def test_map_general_ledger_endpoints(self):
        """Test mapping of general ledger endpoints."""
        mapper = RouteMapper()
        
        # Ledger accounts
        tool_name, category = mapper.map_endpoint("/services/api/ledger/accounts", "GET")
        assert tool_name == "get_ledger_accounts"
        assert category == EndpointCategory.GENERAL_LEDGER
        
        # Journals
        tool_name, category = mapper.map_endpoint("/services/api/ledger/journals", "POST")
        assert tool_name == "create_journals"
        assert category == EndpointCategory.GENERAL_LEDGER
    
    def test_map_contact_endpoints(self):
        """Test mapping of contact endpoints."""
        mapper = RouteMapper()
        
        tool_name, category = mapper.map_endpoint("/services/api/contacts", "GET")
        assert tool_name == "get_contacts"
        assert category == EndpointCategory.CONTACTS
        
        tool_name, category = mapper.map_endpoint("/services/api/contacts/789", "DELETE")
        assert tool_name == "delete_contact_by_id"
        assert category == EndpointCategory.CONTACTS
    
    def test_add_custom_rule(self):
        """Test adding a custom rule."""
        mapper = RouteMapper()
        
        custom_rule = RouteMappingRule(
            pattern=r"/custom/endpoint/?$",
            tool_name_template="{method}_custom",
            category=EndpointCategory.SYSTEM
        )
        
        mapper.add_rule(custom_rule)
        
        tool_name, category = mapper.map_endpoint("/custom/endpoint", "GET")
        assert tool_name == "get_custom"
        assert category == EndpointCategory.SYSTEM
    
    def test_fallback_mapping(self):
        """Test fallback mapping for unknown endpoints."""
        mapper = RouteMapper()
        
        # Unknown endpoint should use generic pattern
        tool_name, category = mapper.map_endpoint("/services/api/unknown/resource", "GET")
        assert "unknown" in tool_name
        assert "resource" in tool_name
        assert category == EndpointCategory.UNKNOWN
    
    def test_categorize_operations(self):
        """Test categorizing a list of operations."""
        mapper = RouteMapper()
        
        operations = [
            {"_path": "/services/api/purchase/invoices", "_method": "GET"},
            {"_path": "/services/api/sales/invoices", "_method": "POST"},
            {"_path": "/services/api/ledger/accounts", "_method": "GET"},
            {"_path": "/services/api/contacts/123", "_method": "PUT"},
        ]
        
        categorized = mapper.categorize_operations(operations)
        
        assert len(categorized[EndpointCategory.ACCOUNTS_PAYABLE]) == 1
        assert len(categorized[EndpointCategory.ACCOUNTS_RECEIVABLE]) == 1
        assert len(categorized[EndpointCategory.GENERAL_LEDGER]) == 1
        assert len(categorized[EndpointCategory.CONTACTS]) == 1


class TestHelperFunctions:
    """Test standalone helper functions."""
    
    def test_generate_tool_name(self):
        """Test the generate_tool_name helper."""
        # Simple cases
        assert generate_tool_name("/accounts", "GET") == "get_accounts"
        assert generate_tool_name("/accounts", "POST") == "create_accounts"
        
        # With services/api prefix
        assert generate_tool_name("/services/api/accounts", "GET") == "get_accounts"
        
        # With path parameters
        assert generate_tool_name("/accounts/{id}", "GET") == "get_accounts_by_id"
        assert generate_tool_name("/accounts/{accountId}/transactions", "GET") == "get_accounts_by_id_transactions"
        
        # Special characters
        assert generate_tool_name("/sales-invoices", "GET") == "get_sales_invoices"
        assert generate_tool_name("/purchase.orders", "GET") == "get_purchase_orders"
    
    def test_categorize_endpoint(self):
        """Test the categorize_endpoint helper."""
        # Accounts Payable
        assert categorize_endpoint("/purchase/invoices") == EndpointCategory.ACCOUNTS_PAYABLE
        assert categorize_endpoint("/vendor/bills") == EndpointCategory.ACCOUNTS_PAYABLE
        assert categorize_endpoint("/payables") == EndpointCategory.ACCOUNTS_PAYABLE
        
        # Accounts Receivable
        assert categorize_endpoint("/sales/invoices") == EndpointCategory.ACCOUNTS_RECEIVABLE
        assert categorize_endpoint("/customer/payments") == EndpointCategory.ACCOUNTS_RECEIVABLE
        assert categorize_endpoint("/receivables") == EndpointCategory.ACCOUNTS_RECEIVABLE
        
        # General Ledger
        assert categorize_endpoint("/ledger/accounts") == EndpointCategory.GENERAL_LEDGER
        assert categorize_endpoint("/journals") == EndpointCategory.GENERAL_LEDGER
        assert categorize_endpoint("/transactions") == EndpointCategory.GENERAL_LEDGER
        
        # Contacts
        assert categorize_endpoint("/contacts") == EndpointCategory.CONTACTS
        assert categorize_endpoint("/contact/123") == EndpointCategory.CONTACTS
        
        # Company
        assert categorize_endpoint("/company/settings") == EndpointCategory.COMPANY
        assert categorize_endpoint("/companies") == EndpointCategory.COMPANY
        
        # Other categories
        assert categorize_endpoint("/inventory/items") == EndpointCategory.INVENTORY
        assert categorize_endpoint("/projects/tasks") == EndpointCategory.PROJECTS
        assert categorize_endpoint("/reports/balances") == EndpointCategory.REPORTING
        
        # Unknown
        assert categorize_endpoint("/unknown/endpoint") == EndpointCategory.UNKNOWN
        assert categorize_endpoint("/misc") == EndpointCategory.UNKNOWN
