"""
Integration test to verify the foundation components work together.
"""

import pytest
import asyncio
from pathlib import Path
import tempfile
import yaml

from src.servers.core import BaseIntacctServer
from src.auth.interfaces import AuthConfig, AuthMethod
from src.auth.token_manager import TokenManager
from src.auth.storage import InMemoryTokenStorage
from src.utils.openapi_loader import OpenAPILoader, merge_openapi_specs
from src.utils.route_mapper import RouteMapper, EndpointCategory


@pytest.fixture
def sample_intacct_spec():
    """Create a sample Intacct-like OpenAPI spec."""
    return {
        "openapi": "3.0.0",
        "info": {
            "title": "Sage Intacct API - Test",
            "version": "1.0.0",
            "description": "Test subset of Intacct API"
        },
        "servers": [
            {"url": "https://api.intacct.com"}
        ],
        "paths": {
            "/services/api/contacts": {
                "get": {
                    "summary": "List contacts",
                    "operationId": "listContacts",
                    "tags": ["Contacts"],
                    "responses": {
                        "200": {
                            "description": "Success",
                            "content": {
                                "application/json": {
                                    "schema": {"type": "object"}
                                }
                            }
                        }
                    }
                },
                "post": {
                    "summary": "Create contact",
                    "operationId": "createContact",
                    "tags": ["Contacts"],
                    "responses": {
                        "201": {"description": "Created"}
                    }
                }
            },
            "/services/api/purchase/invoices": {
                "get": {
                    "summary": "List purchase invoices",
                    "operationId": "listPurchaseInvoices",
                    "tags": ["Accounts Payable"],
                    "responses": {
                        "200": {"description": "Success"}
                    }
                }
            },
            "/services/api/sales/invoices": {
                "get": {
                    "summary": "List sales invoices",
                    "operationId": "listSalesInvoices",
                    "tags": ["Accounts Receivable"],
                    "responses": {
                        "200": {"description": "Success"}
                    }
                }
            },
            "/services/api/ledger/accounts": {
                "get": {
                    "summary": "List ledger accounts",
                    "operationId": "listLedgerAccounts",
                    "tags": ["General Ledger"],
                    "responses": {
                        "200": {"description": "Success"}
                    }
                }
            }
        }
    }


class TestFoundationIntegration:
    """Test that all foundation components work together."""
    
    @pytest.mark.asyncio
    async def test_complete_flow(self, sample_intacct_spec):
        """Test the complete flow from spec loading to server creation."""
        
        # 1. Create auth configuration
        auth_config = AuthConfig(
            client_id="test_client",
            client_secret="test_secret",
            company_id="test_company",
            auth_type=AuthMethod.CLIENT_CREDENTIALS.value
        )
        
        # 2. Create token manager with in-memory storage
        token_storage = InMemoryTokenStorage()
        token_manager = TokenManager(storage=token_storage)
        
        # 3. Create base server
        server = BaseIntacctServer(
            name="intacct-test-server",
            version="0.1.0",
            auth_config=auth_config,
            token_manager=token_manager
        )
        
        # 4. Load OpenAPI spec
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(sample_intacct_spec, f)
            spec_path = f.name
        
        try:
            spec = server.load_openapi_spec(spec_path)
            
            # Verify spec was loaded
            assert spec is not None
            assert spec.info["title"] == "Sage Intacct API - Test"
            assert len(spec.paths) == 4
            
            # 5. Test route mapping
            operations = spec.get_operations()
            assert len(operations) == 5  # 2 contact ops + 3 others
            
            # 6. Test categorization
            categorized = server.route_mapper.categorize_operations(operations)
            
            assert len(categorized[EndpointCategory.CONTACTS]) == 2
            assert len(categorized[EndpointCategory.ACCOUNTS_PAYABLE]) == 1
            assert len(categorized[EndpointCategory.ACCOUNTS_RECEIVABLE]) == 1
            assert len(categorized[EndpointCategory.GENERAL_LEDGER]) == 1
            
            # 7. Test tool name generation
            contact_get_tool, _ = server.route_mapper.map_endpoint(
                "/services/api/contacts", "GET"
            )
            assert contact_get_tool == "get_contacts"
            
            purchase_tool, _ = server.route_mapper.map_endpoint(
                "/services/api/purchase/invoices", "GET"
            )
            assert purchase_tool == "get_purchase_invoices"
            
        finally:
            Path(spec_path).unlink()
    
    def test_openapi_utilities(self, sample_intacct_spec):
        """Test OpenAPI loading and merging utilities."""
        
        # Create two specs to merge
        spec1 = sample_intacct_spec.copy()
        spec1["info"]["title"] = "Intacct API Part 1"
        
        spec2 = {
            "openapi": "3.0.0",
            "info": {
                "title": "Intacct API Part 2",
                "version": "1.0.0"
            },
            "paths": {
                "/services/api/projects": {
                    "get": {
                        "summary": "List projects",
                        "responses": {"200": {"description": "Success"}}
                    }
                }
            }
        }
        
        with tempfile.TemporaryDirectory() as tmpdir:
            # Write specs
            spec1_path = Path(tmpdir) / "spec1.yaml"
            spec2_path = Path(tmpdir) / "spec2.yaml"
            
            with open(spec1_path, 'w') as f:
                yaml.dump(spec1, f)
            with open(spec2_path, 'w') as f:
                yaml.dump(spec2, f)
            
            # Load specs
            loader = OpenAPILoader(tmpdir)
            specs = loader.load_multiple([spec1_path, spec2_path])
            
            assert len(specs) == 2
            
            # Merge specs
            merged = merge_openapi_specs(specs, title="Merged Intacct API")
            
            assert merged.info["title"] == "Merged Intacct API"
            assert len(merged.paths) == 5  # 4 from spec1 + 1 from spec2
            assert "/services/api/projects" in merged.paths
    
    def test_route_mapper_coverage(self):
        """Test route mapper handles various Intacct patterns."""
        mapper = RouteMapper()
        
        test_cases = [
            # (path, method, expected_tool_name, expected_category)
            ("/services/api/purchase/invoices", "GET", "get_purchase_invoices", EndpointCategory.ACCOUNTS_PAYABLE),
            ("/services/api/purchase/invoices/123", "PUT", "update_purchase_invoice_by_id", EndpointCategory.ACCOUNTS_PAYABLE),
            ("/services/api/sales/credit-notes", "POST", "create_sales_credit_notes", EndpointCategory.ACCOUNTS_RECEIVABLE),
            ("/services/api/ledger/journals", "GET", "get_journals", EndpointCategory.GENERAL_LEDGER),
            ("/services/api/contacts", "DELETE", "delete_contacts", EndpointCategory.CONTACTS),
            ("/services/api/companies", "GET", "get_companies", EndpointCategory.COMPANY),
            ("/services/api/unknown/endpoint", "GET", "get_unknown_endpoint", EndpointCategory.UNKNOWN),
        ]
        
        for path, method, expected_name, expected_category in test_cases:
            tool_name, category = mapper.map_endpoint(path, method)
            assert tool_name == expected_name, f"Failed for {method} {path}"
            assert category == expected_category, f"Failed category for {path}"
