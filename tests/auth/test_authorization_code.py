"""Tests for OAuth 2.0 Authorization Code Flow."""

import pytest
import urllib.parse
from src.auth.flows import AuthorizationCode<PERSON>low
from src.auth import AuthConfig, InvalidConfigError


def test_get_auth_url():
    """Test authorization URL generation."""
    config = AuthConfig(
        client_id="test_client_id",
        client_secret="test_client_secret",
        redirect_uri="http://localhost:8080/callback",
        scope="full_access"
    )
    
    flow = AuthorizationCodeFlow()
    auth_request = flow.get_auth_url(config)
    
    # Verify authorization request
    assert auth_request.authorization_url.startswith(
        "https://api.intacct.com/ia/api/v1/oauth2/authorize"
    )
    assert auth_request.state
    assert auth_request.code_verifier
    assert auth_request.code_challenge
    assert auth_request.code_challenge_method == 'S256'
    
    # Parse URL parameters
    parsed = urllib.parse.urlparse(auth_request.authorization_url)
    params = urllib.parse.parse_qs(parsed.query)
    
    assert params['response_type'][0] == 'code'
    assert params['client_id'][0] == config.client_id
    assert params['redirect_uri'][0] == config.redirect_uri
    assert params['scope'][0] == config.scope


def test_missing_config_raises_error():
    """Test that missing configuration raises error."""
    config = AuthConfig(client_id="test", client_secret="secret")
    flow = AuthorizationCodeFlow()
    
    with pytest.raises(InvalidConfigError) as exc_info:
        flow.get_auth_url(config)
    
    assert "redirect_uri is required" in str(exc_info.value)
