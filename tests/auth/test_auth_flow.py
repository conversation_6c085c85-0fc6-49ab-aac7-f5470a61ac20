"""Tests for Authorization Code Flow implementation."""
import pytest
from unittest.mock import As<PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch
from datetime import datetime
import httpx
from src.auth import (
    AuthorizationCodeFlow,
    AuthConfig,
    Token,
    AuthorizationRequest,
    TokenRequest,
    AuthorizationError,
    TokenRefreshError,
    AuthMethod,
)


@pytest.fixture
def auth_config():
    """Create test auth configuration."""
    return AuthConfig(
        client_id="test_client_id",
        client_secret="test_client_secret",
        redirect_uri="http://localhost:8080/callback",
        auth_type="authorization_code",
        scope="api"
    )


@pytest.fixture
def auth_flow():
    """Create Authorization Code Flow instance."""
    return AuthorizationCodeFlow()


@pytest.mark.asyncio
async def test_start_authorization(auth_flow, auth_config):
    """Test starting authorization flow."""
    auth_request = AuthorizationRequest(
        client_id=auth_config.client_id,
        redirect_uri=auth_config.redirect_uri,
        scope=auth_config.scope,
        state="test_state"
    )
    
    url = await auth_flow.start_authorization(auth_config, auth_request)
    
    assert url.startswith("https://api.intacct.com/ia/api/v1/oauth2/authorize")
    assert f"client_id={auth_config.client_id}" in url
    assert "response_type=code" in url
    assert "state=test_state" in url
    assert "code_challenge=" in url  # PKCE


@pytest.mark.asyncio
async def test_exchange_code_success(auth_flow, auth_config):
    """Test successful code exchange."""
    mock_response = Mock()
    mock_response.status_code = 200
    mock_response.json.return_value = {
        "access_token": "new_access_token",
        "token_type": "Bearer",
        "expires_in": 3600,
        "refresh_token": "new_refresh_token",
        "scope": "api"
    }
    
    with patch.object(auth_flow.http_client, 'post', return_value=mock_response) as mock_post:
        token_request = TokenRequest(
            grant_type="authorization_code",
            code="test_code",
            redirect_uri=auth_config.redirect_uri,
            client_id=auth_config.client_id,
            client_secret=auth_config.client_secret,
            code_verifier="test_verifier"
        )
        
        token = await auth_flow.exchange_code(auth_config, token_request)
        
        assert isinstance(token, Token)
        assert token.access_token == "new_access_token"
        assert token.refresh_token == "new_refresh_token"
        assert token.expires_in == 3600


@pytest.mark.asyncio
async def test_exchange_code_error(auth_flow, auth_config):
    """Test code exchange with error response."""
    mock_response = Mock()
    mock_response.status_code = 400
    mock_response.json.return_value = {
        "error": "invalid_grant",
        "error_description": "Invalid authorization code"
    }
    
    with patch.object(auth_flow.http_client, 'post', return_value=mock_response) as mock_post:
        token_request = TokenRequest(
            grant_type="authorization_code",
            code="invalid_code",
            redirect_uri=auth_config.redirect_uri,
            client_id=auth_config.client_id,
            client_secret=auth_config.client_secret
        )
        
        with pytest.raises(AuthorizationError) as exc:
            await auth_flow.exchange_code(auth_config, token_request)
        
        assert "invalid_grant" in str(exc.value)


@pytest.mark.asyncio
async def test_refresh_token_success(auth_flow, auth_config):
    """Test successful token refresh."""
    existing_token = Token(
        access_token="old_access_token",
        token_type="Bearer",
        expires_in=3600,
        refresh_token="old_refresh_token",
        scope="api"
    )
    
    mock_response = Mock()
    mock_response.status_code = 200
    mock_response.json.return_value = {
        "access_token": "refreshed_access_token",
        "token_type": "Bearer",
        "expires_in": 3600,
        "refresh_token": "new_refresh_token",
        "scope": "api"
    }
    
    with patch.object(auth_flow.http_client, 'post', return_value=mock_response) as mock_post:
        new_token = await auth_flow.refresh_token(auth_config, existing_token)
        
        assert new_token.access_token == "refreshed_access_token"
        assert new_token.refresh_token == "new_refresh_token"


@pytest.mark.asyncio
async def test_validate_callback(auth_flow):
    """Test callback validation."""
    # Store a pending request
    auth_flow._pending_requests["test_state"] = {
        "code_verifier": "test_verifier",
        "timestamp": datetime.utcnow(),
        "config": auth_config
    }
    
    # Valid callback
    result = await auth_flow.validate_callback(
        code="test_code",
        state="test_state"
    )
    
    assert result["code"] == "test_code"
    assert result["code_verifier"] == "test_verifier"
    
    # State should be removed after validation
    assert "test_state" not in auth_flow._pending_requests


@pytest.mark.asyncio
async def test_validate_callback_invalid_state(auth_flow):
    """Test callback validation with invalid state."""
    with pytest.raises(AuthorizationError) as exc:
        await auth_flow.validate_callback(
            code="test_code",
            state="invalid_state"
        )
    
    assert "Invalid state parameter" in str(exc.value)
