"""
Tests for the authenticated HTTP client factory.
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, Magic<PERSON>ock, patch

import httpx

from src.auth.http_client import IntacctAuthManager
from src.auth.models import Token
from src.auth.exceptions import IntacctAuthError


@pytest.fixture
def valid_token():
    """Create a valid token."""
    return Token(
        access_token="valid_access_token",
        expires_in=3600,
        refresh_token="valid_refresh_token"
    )


@pytest.fixture
def expired_token():
    """Create an expired token."""
    return Token(
        access_token="expired_access_token",
        expires_in=3600,
        refresh_token="expired_refresh_token",
        issued_at=datetime.utcnow() - timedelta(hours=2)
    )


class TestIntacctAuthManager:
    """Test the IntacctAuthManager class."""
    
    def test_init(self):
        """Test initialization."""
        mock_token_manager = Mock()
        
        manager = IntacctAuthManager(
            token_manager=mock_token_manager,
            base_url="https://api.example.com",
            timeout=60.0
        )
        
        assert manager.token_manager == mock_token_manager
        assert manager.base_url == "https://api.example.com"
        assert manager.timeout == 60.0
        assert manager._active_tokens == {}
        
    def test_get_authenticated_client(self, valid_token):
        """Test getting authenticated client."""
        mock_token_manager = Mock()
        mock_token_manager.get_token.return_value = valid_token
        
        manager = IntacctAuthManager(token_manager=mock_token_manager)
        
        # Should create a client
        client = manager.get_authenticated_client()
        
        assert isinstance(client, httpx.AsyncClient)
        assert client.base_url == "https://api.intacct.com"
        assert "User-Agent" in client.headers
        
    def test_token_validation(self):
        """Test token validation logic."""
        mock_token_manager = Mock()
        manager = IntacctAuthManager(token_manager=mock_token_manager)
        
        # Valid token
        valid = Token(
            access_token="token",
            expires_in=3600,
            issued_at=datetime.utcnow()
        )
        assert manager._is_token_valid(valid)
        
        # Expired token
        expired = Token(
            access_token="token",
            expires_in=3600,
            issued_at=datetime.utcnow() - timedelta(hours=2)
        )
        assert not manager._is_token_valid(expired)
        
        # Token without expiry
        no_expiry = Token(
            access_token="token"
        )
        assert manager._is_token_valid(no_expiry)