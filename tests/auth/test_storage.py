"""Tests for token storage implementations."""
import pytest
import tempfile
from datetime import datetime, UTC
import json
import os
from src.auth import (
    Token,
    TokenType,
    FileTokenStorage,
    InMemoryTokenStorage,
    StorageError
)


@pytest.fixture
def sample_token():
    """Create a test token."""
    return Token(
        access_token="test_access_token",
        token_type=TokenType.BEARER,
        expires_in=3600,
        refresh_token="test_refresh_token",
        scope="api",
        issued_at=datetime.now(UTC)
    )


@pytest.mark.asyncio
async def test_file_storage(sample_token):
    """Test file storage operations."""
    with tempfile.TemporaryDirectory() as temp_dir:
        storage = FileTokenStorage(storage_path=temp_dir)
        
        # Test save and retrieve
        await storage.save_token("test_key", sample_token)
        retrieved = await storage.get_token("test_key")
        assert retrieved.access_token == sample_token.access_token
        assert retrieved.refresh_token == sample_token.refresh_token
        
        # Test list tokens
        tokens = await storage.list_tokens()
        assert "test_key" in tokens
        
        # Test delete
        await storage.delete_token("test_key")
        assert await storage.get_token("test_key") is None


@pytest.mark.asyncio
async def test_memory_storage(sample_token):
    """Test in-memory storage operations."""
    storage = InMemoryTokenStorage()
    
    # Test operations
    await storage.save_token("key1", sample_token)
    assert await storage.get_token("key1") is not None
    assert "key1" in await storage.list_tokens()
    
    await storage.delete_token("key1")
    assert await storage.get_token("key1") is None
