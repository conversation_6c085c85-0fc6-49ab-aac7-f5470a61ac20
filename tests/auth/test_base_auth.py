"""Tests for base authentication module interfaces and models."""

import pytest
from datetime import datetime, timedelta
from src.auth import Token, TokenType, AuthConfig, EntityScope, IntacctAuthError


def test_token_creation():
    """Test basic token creation."""
    token = Token(
        access_token="test_access_token",
        token_type=TokenType.BEARER,
        expires_in=3600,
        refresh_token="test_refresh_token",
        scope="api"
    )
    
    assert token.access_token == "test_access_token"
    assert token.token_type == TokenType.BEARER
    assert token.expires_in == 3600
    assert token.refresh_token == "test_refresh_token"
    assert isinstance(token.issued_at, datetime)


def test_token_expiration():
    """Test token expiration logic."""
    # Create expired token
    past_time = datetime.utcnow() - timedelta(hours=2)
    expired_token = Token(
        access_token="expired_token",
        expires_in=3600,  # 1 hour
        issued_at=past_time
    )
    assert expired_token.is_expired is True
    
    # Create valid token
    valid_token = Token(
        access_token="valid_token",
        expires_in=3600,
        issued_at=datetime.utcnow()
    )
    assert valid_token.is_expired is False


def test_auth_config():
    """Test AuthConfig creation."""
    config = AuthConfig(client_id="test_id", client_secret="test_secret")
    assert config.client_id == "test_id"
    assert config.client_secret == "test_secret"
