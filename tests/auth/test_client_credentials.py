"""Tests for OAuth 2.0 Client Credentials Flow."""

import pytest
import httpx
from unittest.mock import AsyncMock, patch
from src.auth.flows import ClientCredentialsFlow
from src.auth import AuthConfig, InvalidConfigError, InvalidCredentialsError


@pytest.mark.asyncio
async def test_client_credentials_authenticate():
    """Test client credentials authentication."""
    config = AuthConfig(
        client_id="test_client_id",
        client_secret="test_client_secret",
        scope="api"
    )
    
    flow = ClientCredentialsFlow()
    
    # Mock the HTTP response
    mock_response = AsyncMock()
    mock_response.status_code = 200
    mock_response.json.return_value = {
        'access_token': 'test_access_token',
        'token_type': 'Bearer',
        'expires_in': 3600,
        'scope': 'api'
    }
    mock_response.raise_for_status = AsyncMock()
    
    with patch.object(flow.http_client, 'post', return_value=mock_response):
        token_response = await flow.authenticate(config)
        
        assert token_response.access_token == 'test_access_token'
        assert token_response.token_type == 'Bearer'
        assert token_response.expires_in == 3600
        assert token_response.scope == 'api'
    
    await flow.close()


def test_not_implemented_methods():
    """Test that user-interaction methods raise NotImplementedError."""
    flow = ClientCredentialsFlow()
    config = AuthConfig(client_id="test", client_secret="secret")
    
    with pytest.raises(NotImplementedError):
        flow.get_auth_url(config)
