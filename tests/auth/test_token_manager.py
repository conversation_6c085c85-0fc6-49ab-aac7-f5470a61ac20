"""Tests for Token Manager implementation."""
import pytest
from unittest.mock import As<PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch
from datetime import datetime, timedelta
from src.auth import (
    TokenManager,
    AuthConfig,
    Token,
    InMemoryTokenStorage,
    AuthMethod,
    TokenExpiredError,
    TokenRefreshError,
)


@pytest.fixture
def auth_config():
    """Create test auth configuration."""
    return AuthConfig(
        client_id="test_client_id",
        client_secret="test_client_secret",
        redirect_uri="http://localhost:8080/callback",
        auth_type="authorization_code",
        scope="api"
    )


@pytest.fixture
def token_manager():
    """Create token manager instance."""
    storage = InMemoryTokenStorage()
    return TokenManager(storage=storage)


@pytest.fixture
def valid_token():
    """Create a valid token."""
    return Token(
        access_token="valid_access_token",
        token_type="Bearer",
        expires_in=3600,
        refresh_token="valid_refresh_token",
        scope="api",
        issued_at=datetime.utcnow()
    )


@pytest.fixture
def expired_token():
    """Create an expired token."""
    return Token(
        access_token="expired_access_token",
        token_type="Bearer",
        expires_in=3600,
        refresh_token="refresh_token",
        scope="api",
        issued_at=datetime.utcnow() - timedelta(hours=2)
    )


@pytest.mark.asyncio
async def test_store_token(token_manager, auth_config, valid_token):
    """Test storing a token."""
    await token_manager.store_token(auth_config, valid_token)
    
    # Verify token is stored
    stored_token = await token_manager.get_token(auth_config)
    assert stored_token is not None
    assert stored_token.access_token == valid_token.access_token


@pytest.mark.asyncio
async def test_get_valid_token_cached(token_manager, auth_config, valid_token):
    """Test getting a valid cached token."""
    # Store a valid token
    await token_manager.store_token(auth_config, valid_token)
    
    # Should return the cached token
    token = await token_manager.get_valid_token(auth_config)
    assert token.access_token == "valid_access_token"


@pytest.mark.asyncio
async def test_get_valid_token_expired_refresh(token_manager, auth_config, expired_token):
    """Test automatic refresh of expired token."""
    # Register config and store expired token
    token_manager.register_config("test", auth_config)
    await token_manager.store_token(auth_config, expired_token)
    
    # Mock the flow's refresh_token method
    mock_flow = Mock()
    refreshed_token = Token(
        access_token="refreshed_access_token",
        token_type="Bearer",
        expires_in=3600,
        refresh_token="new_refresh_token",
        scope="api",
        issued_at=datetime.utcnow()
    )
    mock_flow.refresh_token = AsyncMock(return_value=refreshed_token)
    
    # Replace the flow
    token_manager._flows[auth_config.auth_method] = mock_flow
    
    # Get valid token should trigger refresh
    token = await token_manager.get_valid_token(auth_config)
    assert token.access_token == "refreshed_access_token"
    mock_flow.refresh_token.assert_called_once()


@pytest.mark.asyncio
async def test_get_valid_token_no_token(token_manager, auth_config):
    """Test behavior when no token is stored."""
    with pytest.raises(TokenExpiredError) as exc:
        await token_manager.get_valid_token(auth_config)
    
    assert "No token found" in str(exc.value)


@pytest.mark.asyncio
async def test_remove_token(token_manager, auth_config, valid_token):
    """Test removing a token."""
    # Store and then remove
    await token_manager.store_token(auth_config, valid_token)
    await token_manager.remove_token(auth_config)
    
    # Token should be gone
    token = await token_manager.get_token(auth_config)
    assert token is None


@pytest.mark.asyncio
async def test_is_token_valid(token_manager, valid_token, expired_token):
    """Test token validity check."""
    # Valid token
    assert token_manager.is_token_valid(valid_token) is True
    
    # Expired token
    assert token_manager.is_token_valid(expired_token) is False
    
    # No token
    assert token_manager.is_token_valid(None) is False


@pytest.mark.asyncio
async def test_multi_entity_support(token_manager, auth_config):
    """Test multi-entity token storage."""
    # Store tokens for different entities
    token1 = Token(
        access_token="entity1_token",
        token_type="Bearer",
        expires_in=3600,
        scope="api"
    )
    token2 = Token(
        access_token="entity2_token",
        token_type="Bearer",
        expires_in=3600,
        scope="api"
    )
    
    await token_manager.store_token(auth_config, token1, entity_id="entity1")
    await token_manager.store_token(auth_config, token2, entity_id="entity2")
    
    # Retrieve tokens
    retrieved1 = await token_manager.get_token(auth_config, entity_id="entity1")
    retrieved2 = await token_manager.get_token(auth_config, entity_id="entity2")
    
    assert retrieved1.access_token == "entity1_token"
    assert retrieved2.access_token == "entity2_token"
