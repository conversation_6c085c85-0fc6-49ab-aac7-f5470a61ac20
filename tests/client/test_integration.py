"""
Integration tests for the complete authentication and client flow.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from pathlib import Path
import httpx

from src.client import IntacctAuthManager
from src.auth import AuthConfig, TokenType
from src.auth.token_manager import Token


class TestAuthenticationIntegration:
    """Integration tests for authentication flow."""
    
    @pytest.fixture
    def mock_token(self):
        """Create a mock token."""
        return Token(
            access_token="integration_test_token",
            token_type=TokenType.BEARER,
            expires_in=3600,
            refresh_token="refresh_token",
            scope="intacct:*"
        )
        
    @pytest.fixture
    def auth_config(self):
        """Create test auth config."""
        return AuthConfig(
            auth_type="client_credentials",
            client_id="test_client_id",
            client_secret="test_client_secret",
            authorization_url="https://api.intacct.com/oauth2/authorize",
            token_url="https://api.intacct.com/oauth2/token",
            scope="intacct:*",
            web_services_user="test_user"
        )    
    @pytest.mark.asyncio
    async def test_full_authentication_flow(self, auth_config, mock_token, tmp_path):
        """Test complete authentication flow from start to API call."""
        # Create auth manager
        token_storage = tmp_path / "tokens"
        manager = IntacctAuthManager(auth_config, token_storage_path=token_storage)
        
        # Mock the authentication response
        mock_auth_response = {
            "access_token": mock_token.access_token,
            "token_type": mock_token.token_type,
            "expires_in": 3600,
            "refresh_token": mock_token.refresh_token,
            "scope": mock_token.scope
        }
        
        # Mock HTTP client for token request
        with patch('httpx.AsyncClient') as mock_client_class:
            mock_client = AsyncMock()
            mock_client_class.return_value = mock_client
            mock_client.__aenter__.return_value = mock_client
            
            # Mock token endpoint response
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = mock_auth_response
            mock_response.raise_for_status = MagicMock()
            
            mock_client.post.return_value = mock_response
            
            # Initialize (should authenticate)
            await manager.initialize()
            
            # Verify token was obtained
            token_info = await manager.get_token_info()
            assert token_info is not None
            assert not token_info["is_expired"]            
    @pytest.mark.asyncio
    async def test_api_call_with_auth_retry(self, auth_config, mock_token, tmp_path):
        """Test making API call with automatic retry on 401."""
        # Create and initialize auth manager
        token_storage = tmp_path / "tokens"
        manager = IntacctAuthManager(auth_config, token_storage_path=token_storage)
        
        # Pre-populate with a token
        await manager.token_manager.store_token(mock_token)
        
        # Get authenticated client
        client = await manager.get_authenticated_client()
        
        # Mock API responses
        mock_401_response = httpx.Response(
            status_code=401,
            request=httpx.Request("GET", "https://api.intacct.com/test"),
            headers={"content-type": "application/json"},
            json={"error": "unauthorized"}
        )
        
        mock_200_response = httpx.Response(
            status_code=200,
            request=httpx.Request("GET", "https://api.intacct.com/test"),
            headers={"content-type": "application/json"},
            json={"data": "success"}
        )
        
        # Mock the client's request method
        with patch.object(client, '_client') as mock_http_client:
            # First request returns 401, second returns 200
            mock_http_client.request.side_effect = [mock_401_response, mock_200_response]
            
            # Mock token refresh
            new_token = Token(
                access_token="new_access_token",
                token_type="Bearer",
                expires_at=datetime.now(timezone.utc) + timedelta(hours=2),
                refresh_token="new_refresh_token",
                scope="intacct:*"
            )            
            with patch.object(manager.token_manager, 'refresh_token', return_value=new_token):
                # Make request - should retry after 401
                response = await client.get("/test")
                
                # Verify success after retry
                assert response.status_code == 200
                assert response.json() == {"data": "success"}
                
                # Verify retry occurred
                assert mock_http_client.request.call_count == 2
                manager.token_manager.refresh_token.assert_called_once()
                
    @pytest.mark.asyncio
    async def test_multi_entity_support(self, auth_config, mock_token, tmp_path):
        """Test multi-entity support with different clients."""
        # Create auth manager
        token_storage = tmp_path / "tokens"
        manager = IntacctAuthManager(auth_config, token_storage_path=token_storage)
        
        # Pre-populate with a token
        await manager.token_manager.store_token(mock_token)
        
        # Get clients for different entities
        client1 = await manager.get_authenticated_client(entity_id="entity1")
        client2 = await manager.get_authenticated_client(entity_id="entity2")
        
        # Mock requests to verify entity headers
        request1 = httpx.Request("GET", "https://api.intacct.com/test")
        request2 = httpx.Request("GET", "https://api.intacct.com/test")
        
        await client1._auth_interceptor(request1)
        await client2._auth_interceptor(request2)
        
        # Verify different entity headers
        assert request1.headers["X-Intacct-Entity-ID"] == "entity1"
        assert request2.headers["X-Intacct-Entity-ID"] == "entity2"
        
        # Both should have same auth token
        assert request1.headers["Authorization"] == request2.headers["Authorization"]
        
        # Clean up
        await manager.close()    
    @pytest.mark.asyncio
    async def test_token_expiration_handling(self, auth_config, tmp_path):
        """Test handling of expired tokens during API calls."""
        # Create auth manager
        token_storage = tmp_path / "tokens"
        manager = IntacctAuthManager(auth_config, token_storage_path=token_storage)
        
        # Create an expired token
        expired_token = Token(
            access_token="expired_token",
            token_type=TokenType.BEARER,
            expires_in=-300,  # Expired 5 minutes ago
            refresh_token="valid_refresh_token",
            scope="intacct:*"
        )
        
        # Store the expired token
        await manager.token_manager.store_token(expired_token)
        
        # Create a new valid token for refresh
        new_token = Token(
            access_token="new_valid_token",
            token_type=TokenType.BEARER,
            expires_in=3600,
            refresh_token="new_refresh_token",
            scope="intacct:*"
        )
        
        # Mock the refresh response
        with patch('httpx.AsyncClient') as mock_client_class:
            mock_client = AsyncMock()
            mock_client_class.return_value = mock_client
            mock_client.__aenter__.return_value = mock_client
            
            # Mock refresh token response
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "access_token": new_token.access_token,
                "token_type": new_token.token_type,
                "expires_in": 3600,
                "refresh_token": new_token.refresh_token,
                "scope": new_token.scope
            }
            mock_response.raise_for_status = MagicMock()
            
            mock_client.post.return_value = mock_response
            
            # Get client - should trigger refresh due to expired token
            client = await manager.get_authenticated_client()
            
            # Make a request
            request = httpx.Request("GET", "https://api.intacct.com/test")
            await client._auth_interceptor(request)
            
            # Verify new token is used
            assert request.headers["Authorization"] == f"Bearer {new_token.access_token}"
            
        await manager.close()