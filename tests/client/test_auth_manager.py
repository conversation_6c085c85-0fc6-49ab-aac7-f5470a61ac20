"""
Tests for the Intacct authentication manager.
"""

import pytest
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch, mock_open
from datetime import datetime, timedelta, timezone
import yaml

from src.client.auth_manager import IntacctAuthManager
from src.client.http_client import AuthenticatedClient
from src.auth import AuthConfig, TokenType
from src.auth.exceptions import AuthorizationError as AuthenticationError
from src.auth.token_manager import Token


class TestIntacctAuthManager:
    """Test cases for IntacctAuthManager."""
    
    @pytest.fixture
    def auth_config_auth_code(self):
        """Create auth config for authorization code flow."""
        return AuthConfig(
            auth_type="authorization_code",
            client_id="test_client_id",
            client_secret="test_client_secret",
            redirect_uri="http://localhost:8080/callback",
            authorization_url="https://api.intacct.com/oauth2/authorize",
            token_url="https://api.intacct.com/oauth2/token",
            scope="intacct:*"
        )
        
    @pytest.fixture
    def auth_config_client_creds(self):
        """Create auth config for client credentials flow."""
        return AuthConfig(
            auth_type="client_credentials",
            client_id="test_client_id",
            client_secret="test_client_secret",
            authorization_url="https://api.intacct.com/oauth2/authorize",
            token_url="https://api.intacct.com/oauth2/token",
            scope="intacct:*",
            web_services_user="test_user"
        )
        
    @pytest.fixture
    def mock_token(self):
        """Create a mock token."""
        return Token(
            access_token="test_access_token",
            token_type=TokenType.BEARER,
            expires_in=3600,
            refresh_token="test_refresh_token",
            scope="intacct:*"
        )        
    def test_create_auth_flow_authorization_code(self, auth_config_auth_code):
        """Test creation of authorization code flow."""
        manager = IntacctAuthManager(auth_config_auth_code)
        
        from src.auth import AuthorizationCodeFlow
        assert isinstance(manager.auth_flow, AuthorizationCodeFlow)
        
    def test_create_auth_flow_client_credentials(self, auth_config_client_creds):
        """Test creation of client credentials flow."""
        manager = IntacctAuthManager(auth_config_client_creds)
        
        from src.auth import ClientCredentialsFlow
        assert isinstance(manager.auth_flow, ClientCredentialsFlow)
        
    def test_create_auth_flow_invalid_type(self):
        """Test error on invalid auth type."""
        config = AuthConfig(
            auth_type="invalid_type",
            client_id="test",
            client_secret="test",
            authorization_url="https://test",
            token_url="https://test"
        )
        
        with pytest.raises(ValueError) as exc_info:
            IntacctAuthManager(config)
        assert "Unsupported auth type" in str(exc_info.value)
        
    @pytest.mark.asyncio
    async def test_initialize_with_existing_token(self, auth_config_client_creds, mock_token):
        """Test initialization with existing valid token."""
        manager = IntacctAuthManager(auth_config_client_creds)
        
        # Mock token manager to return existing token
        with patch.object(manager.token_manager, 'get_token', return_value=mock_token):
            await manager.initialize()
            
        # Should not trigger authentication
        manager.auth_flow.authenticate = AsyncMock()
        manager.auth_flow.authenticate.assert_not_called()        
    @pytest.mark.asyncio
    async def test_initialize_auth_code_no_token(self, auth_config_auth_code):
        """Test initialization for auth code flow without token."""
        manager = IntacctAuthManager(auth_config_auth_code)
        
        # Mock token manager to return None
        with patch.object(manager.token_manager, 'get_token', return_value=None):
            with patch.object(manager.auth_flow, 'get_authorization_url', return_value="https://auth.url"):
                with pytest.raises(AuthenticationError) as exc_info:
                    await manager.initialize()
                    
                assert "Authorization required" in str(exc_info.value)
                
    @pytest.mark.asyncio
    async def test_initialize_client_creds_no_token(self, auth_config_client_creds):
        """Test initialization for client credentials flow without token."""
        manager = IntacctAuthManager(auth_config_client_creds)
        
        # Mock token manager and auth flow
        with patch.object(manager.token_manager, 'get_token', return_value=None):
            manager.auth_flow.authenticate = AsyncMock()
            
            await manager.initialize()
            
            # Should trigger authentication
            manager.auth_flow.authenticate.assert_called_once()
            
    @pytest.mark.asyncio
    async def test_get_authenticated_client(self, auth_config_client_creds):
        """Test getting an authenticated client."""
        manager = IntacctAuthManager(auth_config_client_creds)
        
        client = await manager.get_authenticated_client(
            entity_id="test_entity",
            timeout=60.0,
            max_retries=5
        )
        
        assert isinstance(client, AuthenticatedClient)
        assert client.entity_id == "test_entity"
        assert client.timeout == 60.0
        assert client.max_retries == 5        
    @pytest.mark.asyncio
    async def test_get_authenticated_client_caching(self, auth_config_client_creds):
        """Test client caching."""
        manager = IntacctAuthManager(auth_config_client_creds)
        
        # Get same client twice
        client1 = await manager.get_authenticated_client(entity_id="test_entity")
        client2 = await manager.get_authenticated_client(entity_id="test_entity")
        
        # Should be the same instance
        assert client1 is client2
        
        # Different entity should get different client
        client3 = await manager.get_authenticated_client(entity_id="other_entity")
        assert client3 is not client1
        
    @pytest.mark.asyncio
    async def test_complete_authorization(self, auth_config_auth_code):
        """Test completing authorization code flow."""
        manager = IntacctAuthManager(auth_config_auth_code)
        
        # Mock the auth flow
        manager.auth_flow.handle_callback = AsyncMock()
        
        await manager.complete_authorization("https://callback.url?code=test")
        
        manager.auth_flow.handle_callback.assert_called_once_with("https://callback.url?code=test")
        
    @pytest.mark.asyncio
    async def test_complete_authorization_wrong_flow(self, auth_config_client_creds):
        """Test error when completing auth with wrong flow type."""
        manager = IntacctAuthManager(auth_config_client_creds)
        
        with pytest.raises(ValueError) as exc_info:
            await manager.complete_authorization("https://callback.url")
            
        assert "only for authorization code flow" in str(exc_info.value)        
    @pytest.mark.asyncio
    async def test_token_operations(self, auth_config_client_creds, mock_token):
        """Test token refresh and revoke operations."""
        manager = IntacctAuthManager(auth_config_client_creds)
        
        # Mock token manager methods
        manager.token_manager.refresh_token = AsyncMock(return_value=mock_token)
        manager.token_manager.revoke_token = AsyncMock()
        manager.token_manager.get_token = AsyncMock(return_value=mock_token)
        
        # Test refresh
        token = await manager.refresh_token()
        assert token == mock_token
        manager.token_manager.refresh_token.assert_called_once()
        
        # Test revoke
        await manager.revoke_token()
        manager.token_manager.revoke_token.assert_called_once()
        
        # Test get token info
        info = await manager.get_token_info()
        assert info is not None
        assert info["access_token"].endswith("...")
        assert "expires_at" in info
        assert info["is_expired"] is False
        
    @pytest.mark.asyncio
    async def test_list_entities(self, auth_config_client_creds):
        """Test listing available entities."""
        manager = IntacctAuthManager(auth_config_client_creds)
        
        # Mock response
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "entities": [
                {"id": "entity1", "name": "Entity 1"},
                {"id": "entity2", "name": "Entity 2"}
            ]
        }
        
        # Mock client
        mock_client = AsyncMock()
        mock_client.get.return_value = mock_response
        mock_client.__aenter__.return_value = mock_client
        mock_client.__aexit__.return_value = None
        
        with patch.object(manager, 'get_authenticated_client', return_value=mock_client):
            entities = await manager.list_entities()
            
        assert entities == ["entity1", "entity2"]
        mock_client.get.assert_called_once_with("/entities")        
    @pytest.mark.asyncio
    async def test_close(self, auth_config_client_creds):
        """Test closing all cached clients."""
        manager = IntacctAuthManager(auth_config_client_creds)
        
        # Create some clients
        client1 = await manager.get_authenticated_client(entity_id="entity1")
        client2 = await manager.get_authenticated_client(entity_id="entity2")
        
        # Mock close methods
        client1.close = AsyncMock()
        client2.close = AsyncMock()
        
        # Close manager
        await manager.close()
        
        # Verify clients were closed
        client1.close.assert_called_once()
        client2.close.assert_called_once()
        assert len(manager._client_cache) == 0
        
    def test_from_config_file(self, tmp_path):
        """Test creating manager from config file."""
        # Create test config file
        config_data = {
            "auth_type": "client_credentials",
            "client_id": "test_client",
            "client_secret": "test_secret",
            "authorization_url": "https://api.intacct.com/oauth2/authorize",
            "token_url": "https://api.intacct.com/oauth2/token",
            "scope": "intacct:*"
        }
        
        config_path = tmp_path / "auth_config.yaml"
        with open(config_path, 'w') as f:
            yaml.dump(config_data, f)
            
        # Create manager from file
        manager = IntacctAuthManager.from_config_file(config_path)
        
        assert manager.auth_config.auth_type == "client_credentials"
        assert manager.auth_config.client_id == "test_client"