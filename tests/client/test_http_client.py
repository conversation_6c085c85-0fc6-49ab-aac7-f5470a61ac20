"""
Tests for the authenticated HTTP client.
"""

import pytest
from datetime import datetime, timedelta, timezone
from unittest.mock import Async<PERSON>ock, MagicMock, patch
import httpx

from src.client.http_client import AuthenticatedClient
from src.auth.token_manager import Token, TokenManager
from src.auth.exceptions import TokenExpiredError, AuthorizationError as AuthenticationError
from src.auth import TokenType


class TestAuthenticatedClient:
    """Test cases for AuthenticatedClient."""
    
    @pytest.fixture
    def mock_token(self):
        """Create a mock token."""
        return Token(
            access_token="test_access_token",
            token_type=TokenType.BEARER,
            expires_in=3600,  # 1 hour
            refresh_token="test_refresh_token",
            scope="intacct:*"
        )
        
    @pytest.fixture
    def mock_expired_token(self):
        """Create an expired mock token."""
        return Token(
            access_token="expired_token",
            token_type=TokenType.BEARER,
            expires_in=-3600,  # Already expired (negative)
            refresh_token="test_refresh_token",
            scope="intacct:*"
        )
        
    @pytest.fixture
    def mock_token_manager(self, mock_token):
        """Create a mock token manager."""
        manager = AsyncMock(spec=TokenManager)
        manager.get_token.return_value = mock_token
        manager.refresh_token.return_value = mock_token
        return manager
        
    @pytest.fixture
    def auth_client(self, mock_token_manager):
        """Create an authenticated client instance."""
        return AuthenticatedClient(
            token_manager=mock_token_manager,
            entity_id="test_entity",
            base_url="https://api.intacct.com",
            timeout=30.0,
            max_retries=3
        )        
    @pytest.mark.asyncio
    async def test_auth_interceptor_adds_headers(self, auth_client, mock_token):
        """Test that auth interceptor adds required headers."""
        # Create a mock request
        request = httpx.Request("GET", "https://api.intacct.com/test")
        
        # Apply auth interceptor
        await auth_client._auth_interceptor(request)
        
        # Verify headers
        assert request.headers["Authorization"] == f"Bearer {mock_token.access_token}"
        assert request.headers["X-Intacct-Entity-ID"] == "test_entity"
        assert request.headers["Accept"] == "application/json"
        assert request.headers["Content-Type"] == "application/json"
        assert "User-Agent" in request.headers
        
    @pytest.mark.asyncio
    async def test_get_valid_token_refresh_on_expired(self, auth_client, mock_token_manager, mock_expired_token, mock_token):
        """Test token refresh when expired."""
        # First call returns expired token, then raises TokenExpiredError
        mock_token_manager.get_token.side_effect = TokenExpiredError("Token expired")
        
        # Get valid token should trigger refresh
        token = await auth_client._get_valid_token()
        
        # Verify refresh was called
        mock_token_manager.refresh_token.assert_called_once()
        assert token == mock_token
        
    @pytest.mark.asyncio
    async def test_request_with_retry_on_401(self, auth_client, mock_token_manager):
        """Test retry logic on 401 responses."""
        # Mock the HTTP client
        mock_response_401 = httpx.Response(
            status_code=401,
            request=httpx.Request("GET", "https://api.intacct.com/test")
        )
        mock_response_200 = httpx.Response(
            status_code=200,
            request=httpx.Request("GET", "https://api.intacct.com/test"),
            json={"data": "success"}
        )        
        # Setup mock client to return 401 first, then 200 after refresh
        with patch.object(auth_client, '_client') as mock_client:
            mock_client.request.side_effect = [mock_response_401, mock_response_200]
            
            # Make request
            response = await auth_client.request("GET", "/test")
            
            # Verify retry occurred
            assert mock_client.request.call_count == 2
            mock_token_manager.refresh_token.assert_called_once()
            assert response.status_code == 200
            
    @pytest.mark.asyncio
    async def test_request_fails_after_max_retries(self, auth_client, mock_token_manager):
        """Test that request fails after max retries."""
        # Mock all responses as 401
        mock_response_401 = httpx.Response(
            status_code=401,
            request=httpx.Request("GET", "https://api.intacct.com/test")
        )
        
        with patch.object(auth_client, '_client') as mock_client:
            mock_client.request.return_value = mock_response_401
            
            # Request should fail after max retries
            with pytest.raises(AuthenticationError) as exc_info:
                await auth_client.request("GET", "/test")
                
            assert "failed after 3 attempts" in str(exc_info.value)
            assert mock_client.request.call_count == 3
            
    @pytest.mark.asyncio
    async def test_convenience_methods(self, auth_client):
        """Test convenience HTTP methods."""
        # Mock the request method
        auth_client.request = AsyncMock()
        
        # Test each method
        await auth_client.get("/test")
        auth_client.request.assert_called_with("GET", "/test")
        
        await auth_client.post("/test", json={"data": "test"})
        auth_client.request.assert_called_with("POST", "/test", json={"data": "test"})        
        await auth_client.put("/test", json={"data": "test"})
        auth_client.request.assert_called_with("PUT", "/test", json={"data": "test"})
        
        await auth_client.patch("/test", json={"data": "test"})
        auth_client.request.assert_called_with("PATCH", "/test", json={"data": "test"})
        
        await auth_client.delete("/test")
        auth_client.request.assert_called_with("DELETE", "/test")
        
    @pytest.mark.asyncio
    async def test_context_manager(self, auth_client):
        """Test async context manager functionality."""
        async with auth_client as client:
            assert client is auth_client
            assert client._client is not None
            
        # Client should be closed after context
        assert auth_client._client is None or auth_client._client.is_closed
        
    @pytest.mark.asyncio
    async def test_entity_id_header_optional(self, mock_token_manager):
        """Test that entity ID header is optional."""
        # Create client without entity ID
        client = AuthenticatedClient(
            token_manager=mock_token_manager,
            base_url="https://api.intacct.com"
        )
        
        request = httpx.Request("GET", "https://api.intacct.com/test")
        await client._auth_interceptor(request)
        
        # Entity ID header should not be present
        assert "X-Intacct-Entity-ID" not in request.headers