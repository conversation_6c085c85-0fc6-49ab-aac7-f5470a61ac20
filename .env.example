# Sage Intacct OAuth Configuration
INTACCT_CLIENT_ID=your-client-id-here
INTACCT_CLIENT_SECRET=your-client-secret-here
INTACCT_REDIRECT_URI=http://localhost:8080/callback
INTACCT_AUTHORIZATION_URL=https://api.intacct.com/ia/oauth2/authorize
INTACCT_TOKEN_URL=https://api.intacct.com/ia/oauth2/token

# Token Encryption (auto-generated if not provided)
# Generate with: python -c "import secrets; print(secrets.token_urlsafe(32))"
INTACCT_TOKEN_ENCRYPTION_KEY=

# Server Configuration
MCP_SERVER_HOST=0.0.0.0
MCP_SERVER_PORT=8000
MCP_SERVER_LOG_LEVEL=INFO
MCP_SERVER_TRANSPORT=streamable-http

# Environment Settings
DEBUG=false
ENVIRONMENT=development

# Token Storage
TOKEN_STORAGE_BACKEND=file
TOKEN_STORAGE_PATH=./tokens

# Database/Redis (for future token storage backends)
# REDIS_URL=redis://localhost:6379/0
# DATABASE_URL=sqlite:///./intacct_mcp.db

# Rate Limiting
RATE_LIMIT_CALLS=100
RATE_LIMIT_PERIOD=60

# Cache Settings
CACHE_ENABLED=true
CACHE_TTL=300
CACHE_MAX_SIZE_MB=100

# API Configuration
INTACCT_API_BASE_URL=https://api.intacct.com/ia/xml/xmlgw.phtml
INTACCT_API_VERSION=3.0

# Performance Settings
ENABLE_LAZY_LOADING=true
MAX_CONCURRENT_REQUESTS=10
REQUEST_TIMEOUT_SECONDS=30

# Feature Flags
ENABLE_CROSS_MODULE_SEARCH=true
ENABLE_BATCH_OPERATIONS=true
ENABLE_WORKFLOW_AUTOMATION=false
ENABLE_CUSTOM_REPORTS=true
ENABLE_AUDIT_LOGGING=true
ENABLE_METRICS=true

# Monitoring
HEALTH_CHECK_ENABLED=true
METRICS_ENABLED=true
TRACING_ENABLED=false

# Security
VERIFY_SSL=true
ALLOWED_IPS=

# Logging
LOG_FORMAT=json
LOG_FILE_ENABLED=true
LOG_FILE_PATH=./logs/intacct-mcp.log
LOG_FILE_MAX_SIZE_MB=100
LOG_FILE_BACKUP_COUNT=5