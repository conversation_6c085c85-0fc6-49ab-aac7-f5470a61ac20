# Authentication Module Documentation

## Overview

The authentication module (`src/auth/`) provides the foundation for OAuth 2.0 authentication with Sage Intacct.

## Module Structure

```
src/auth/
├── __init__.py          # Module exports
├── interfaces.py        # Authentication protocols and interfaces
├── exceptions.py        # Custom exception classes
└── models.py           # Data models (Token, etc.)
```

## Key Components

### Interfaces (interfaces.py)
- **AuthFlow**: Abstract base class for authentication flows
- **TokenStorage**: Protocol for token storage implementations
- **AuthConfig**: Authentication configuration dataclass
- **TokenRequest/Response**: OAuth 2.0 token request/response models

### Models (models.py)
- **Token**: Core token model with expiration logic
- **TokenType**: Enum for token types (Bearer, Basic)
- **AuthMethod**: Enum for authentication methods
- **EntityScope**: Represents Intacct entity access scope

### Exceptions (exceptions.py)
- **IntacctAuthError**: Base exception class
- **InvalidCredentialsError**: Invalid credentials provided
- **TokenExpiredError**: Access token has expired
- **TokenRefreshError**: Token refresh failed
- **AuthorizationError**: Authorization process failed
- **InvalidConfigError**: Invalid configuration
- **StorageError**: Token storage operation failed
- **RateLimitError**: API rate limit exceeded

## Next Steps

1. **Task 2.2**: Implement OAuth 2.0 Authorization Code Flow
2. **Task 2.3**: Implement Client Credentials Flow
3. **Task 2.4**: Implement Token Management
4. **Task 2.5**: Create Authenticated HTTP Client Factory

These implementations will build upon the interfaces and models defined in this base module.


## OAuth 2.0 Authorization Code Flow

The Authorization Code Flow implementation (`src/auth/flows/authorization_code.py`) provides secure authentication with PKCE support.

### Features

- **PKCE Support**: Implements Proof Key for Code Exchange for enhanced security
- **State Management**: Securely manages state parameters to prevent CSRF attacks
- **Token Exchange**: Handles authorization code to token exchange
- **Token Refresh**: Supports refreshing expired access tokens
- **Error Handling**: Comprehensive error handling with detailed error messages

### Usage

```python
from src.auth import AuthConfig, AuthorizationCodeFlow

# Configure authentication
config = AuthConfig(
    client_id="your_client_id",
    client_secret="your_client_secret",
    redirect_uri="http://localhost:8080/callback",
    scope="admin:*"
)

# Initialize flow
flow = AuthorizationCodeFlow()

# 1. Generate authorization URL
auth_request = flow.get_auth_url(config)
# Direct user to: auth_request.authorization_url

# 2. Handle callback (extract code and state from callback URL)
# 3. Exchange code for tokens
token_response = await flow.exchange_code(code, state)

# 4. Use access token for API calls
access_token = token_response.access_token

# 5. Refresh token when needed
new_token_response = await flow.refresh_token(token_response.refresh_token)
```

### Security Considerations

- Always use HTTPS for redirect URIs in production
- Store tokens securely (never in plain text)
- Implement proper session management
- Clean up expired authorization requests regularly


## OAuth 2.0 Client Credentials Flow

The Client Credentials Flow implementation (`src/auth/flows/client_credentials.py`) provides server-to-server authentication without user interaction.

### Features

- **Direct Authentication**: No user interaction required
- **Web Services Support**: Supports Intacct Web Services user credentials
- **Multi-Entity Support**: Can authenticate to specific entities
- **Simple Integration**: Ideal for backend services and automated processes

### Usage

```python
from src.auth import AuthConfig, ClientCredentialsFlow

# Configure server-to-server authentication
config = AuthConfig(
    client_id="your_client_id",
    client_secret="your_client_secret",
    scope="api",
    # Optional Web Services credentials
    web_services_user="ws_user",
    web_services_password="ws_password",
    company_id="DEMO01"
)

# Initialize flow
flow = ClientCredentialsFlow()

# Authenticate directly
token_response = await flow.authenticate(config)

# Use access token for API calls
access_token = token_response.access_token
```

### When to Use

- **Backend Services**: Server-to-server communication
- **Batch Jobs**: Automated processes without user interaction
- **System Integration**: Integration with other systems
- **Scheduled Tasks**: Cron jobs and scheduled operations

### Note

Client Credentials Flow typically doesn't support refresh tokens. When the access token expires, simply request a new token using the same credentials.
