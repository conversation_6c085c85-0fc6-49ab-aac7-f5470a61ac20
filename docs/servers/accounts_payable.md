# Accounts Payable MCP Server

## Overview

The Accounts Payable (AP) MCP server provides access to Sage Intacct's AP functionality through the Model Context Protocol. It automatically generates MCP tools and resources from the Intacct OpenAPI specification.

## Features

- **Bills Management**: Create, read, update, and delete bills
- **Payment Processing**: Handle payment creation and management
- **Vendor Management**: Manage vendor records and relationships
- **Adjustments & Advances**: Process AP adjustments and advance payments
- **Terms & Groups**: Configure payment terms and vendor account groups

## Architecture

The AP server uses FastMCP 2.0's `from_openapi()` method to automatically generate MCP components:

```python
# Automatic conversion from OpenAPI spec
mcp_server = FastMCP.from_openapi(
    openapi_spec=spec,
    client=authenticated_client,
    route_maps=custom_mappings,
    mcp_names=friendly_names
)
```

### Route Mapping

Custom route maps ensure appropriate MCP component types:

- **Bills**: GET operations → Resources (read-only data)
- **Payments**: All operations → Tools (actions)
- **Vendors**: Standard REST mapping
- **Summaries**: All operations → Tools

### Component Naming

Operation IDs are mapped to user-friendly names:
- `list_accounts_payable_bill` → `list_bills`
- `create_accounts_payable_payment` → `create_payment`
- `get_accounts_payable_vendor` → `get_vendor`

## Usage

### Running the Server

```python
from src.servers.ap import AccountsPayableServer
from src.auth.interfaces import AuthConfig

# Configure authentication
auth_config = AuthConfig(
    sender_id="your_sender_id",
    sender_password="your_password",
    # ... other credentials
)

# Create and run server
server = AccountsPayableServer(auth_config=auth_config)
server.run()
```

### Available Tools

The server provides tools for:
- Creating bills and payments
- Updating vendor information
- Processing adjustments
- Managing payment terms

### Available Resources

Resources provide read-only access to:
- Bill listings and details
- Vendor information
- Payment history
- Account summaries

## Custom Tools

In addition to auto-generated components, the server includes:

- **ap_health_check**: Check server status and authentication
- **ap_quick_summary**: Get overview of bills, payments, and vendors

## Error Handling

The server includes comprehensive error handling:
- Authentication failures are caught and reported
- API timeouts are configured to 30 seconds
- Missing OpenAPI specs trigger clear error messages

## Testing

Run tests with:
```bash
pytest tests/servers/test_accounts_payable.py
```

## Next Steps

With the AP server complete, we can:
1. Implement the Accounts Receivable server
2. Create the General Ledger server
3. Build the composite server that combines all modules
