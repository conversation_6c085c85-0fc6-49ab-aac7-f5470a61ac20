# Accounts Receivable MCP Server

## Overview

The Accounts Receivable (AR) MCP server provides access to Sage Intacct's AR functionality through the Model Context Protocol. It automatically generates MCP tools and resources from the Intacct OpenAPI specification.

## Features

- **Invoice Management**: Create, read, update, and delete invoices
- **Customer Receipts**: Handle payment receipt processing
- **Customer Management**: Manage customer records and relationships
- **Adjustments & Advances**: Process AR adjustments and advance payments
- **Reporting Tools**: AR dashboard and aging reports

## Architecture

The AR server uses FastMCP 2.0's `from_openapi()` method to automatically generate MCP components:

```python
# Automatic conversion from OpenAPI spec
mcp_server = FastMCP.from_openapi(
    openapi_spec=spec,
    client=authenticated_client,
    route_maps=custom_mappings,
    mcp_names=friendly_names
)
```

### Route Mapping

Custom route maps ensure appropriate MCP component types:

- **Invoices**: GET operations → Resources (read-only data)
- **Payments**: All operations → Tools (actions)
- **Customers**: Standard REST mapping
- **Adjustments**: All operations → Tools
- **Invoice Summaries**: All operations → Tools

### Component Naming

Operation IDs are mapped to user-friendly names:
- `list_accounts_receivable_invoice` → `list_invoices`
- `create_accounts_receivable_payment` → `create_receipt`
- `get_accounts_receivable_customer` → `get_customer`

## Usage

### Running the Server

```python
from src.servers.ar import AccountsReceivableServer
from src.auth.interfaces import AuthConfig

# Configure authentication
auth_config = AuthConfig(
    sender_id="your_sender_id",
    sender_password="your_password",
    # ... other credentials
)

# Create and run server
server = AccountsReceivableServer(auth_config=auth_config)
server.run()
```

### Available Tools

The server provides tools for:
- Creating invoices and receipts
- Processing adjustments
- Managing customer information
- Generating reports

### Available Resources

Resources provide read-only access to:
- Invoice listings and details
- Customer information
- Receipt history
- Account summaries

## Custom Tools

In addition to auto-generated components, the server includes:

- **ar_health_check**: Check server status and authentication
- **ar_dashboard**: Get AR metrics including outstanding balances
- **ar_aging_report**: Generate detailed aging reports with date filtering

### AR Dashboard Example

```python
dashboard = await ar_dashboard()
# Returns:
{
    "invoices": {"total": 150, "unpaid": 45, "overdue": 12, "total_outstanding": 125000},
    "receipts": {"total": 320, "recent": 25, "total_amount": 450000},
    "customers": {"total": 85, "active": 72, "new_this_month": 5},
    "aging": {"current": 80000, "30_days": 25000, "60_days": 15000, "90_plus": 5000}
}
```

### Aging Report Example

```python
report = await ar_aging_report(
    as_of_date="2024-12-31",
    customer_id="CUST001"
)
# Returns detailed aging breakdown by period
```

## Error Handling

The server includes comprehensive error handling:
- Authentication failures are caught and reported
- API timeouts are configured to 30 seconds
- Missing OpenAPI specs trigger clear error messages

## Testing

Run tests with:
```bash
pytest tests/servers/test_accounts_receivable.py
```

## Key Differences from AP Server

1. **Terminology**: Payments are called "receipts" in AR context
2. **Custom Tools**: Includes AR-specific tools like aging reports
3. **Dashboard**: Provides AR metrics including aging buckets
4. **Tags**: Uses AR-specific tags for better organization

## Next Steps

With both AP and AR servers complete, we can:
1. Implement the General Ledger server
2. Build the composite server that combines all modules
