# Server Implementation Pattern Comparison

## Common Pattern Across AP and AR Servers

### Structure (Both Servers)
```python
class [Module]Server(BaseIntacctServer):
    def __init__(self):
        # 1. Set server name and version
        # 2. Define OpenAPI spec path
        # 3. Configure custom route maps
        # 4. Set friendly component names
    
    async def initialize(self):
        # 1. Initialize authentication
        # 2. Load OpenAPI spec
        # 3. Create MCP server with from_openapi()
        # 4. Add custom tools
    
    def _customize_component(self):
        # Add module-specific tags and descriptions
    
    def _add_custom_tools(self):
        # Add module-specific custom tools
```

### Key Differences

| Feature | AP Server | AR Server |
|---------|-----------|-----------|
| Server Name | sage-intacct-ap | sage-intacct-ar |
| Main Objects | Bills, Payments, Vendors | Invoices, Receipts, Customers |
| Custom Tools | ap_health_check, ap_quick_summary | ar_health_check, ar_dashboard, ar_aging_report |
| Payment Term | "payment" | "receipt" (AR terminology) |
| Icon Prefix | 🔧 (operations) | 💰 (revenue) |

### Route Map Pattern
Both servers use similar route mapping:
1. Main documents (bills/invoices) → Resources for GET
2. Payments/Receipts → Tools for all operations
3. Summaries → Always tools
4. Exclude internal/admin endpoints

### Next: GL Server
The General Ledger server will follow the same pattern with:
- Journal entries
- Accounts
- GL reports
- Trial balance tools
