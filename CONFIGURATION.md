# Configuration Guide for Sage Intacct MCP Server

This guide explains all configuration options available for the Sage Intacct MCP Server.

## Configuration Files

The server uses three main configuration sources:

1. **Environment Variables** (`.env` file) - For sensitive data and environment-specific settings
2. **Authentication Configuration** (`config/auth_config.yaml`) - OAuth and authentication settings
3. **Server Configuration** (`config/server_config.yaml`) - Server behavior and feature settings

Configuration precedence: Environment Variables > YAML Files > Defaults

## Environment Variables (.env)

### OAuth Configuration
- `INTACCT_CLIENT_ID` - OAuth client ID from Sage Intacct
- `INTACCT_CLIENT_SECRET` - OAuth client secret (keep secure!)
- `INTACCT_REDIRECT_URI` - OAuth callback URL
- `INTACCT_AUTHORIZATION_URL` - Intacct OAuth authorization endpoint
- `INTACCT_TOKEN_URL` - Intacct OAuth token endpoint
- `INTACCT_TOKEN_ENCRYPTION_KEY` - Key for encrypting stored tokens (auto-generated if not set)

### Server Configuration
- `MCP_SERVER_HOST` - Server bind address (default: 0.0.0.0)
- `MCP_SERVER_PORT` - Server port (default: 8000)
- `MCP_SERVER_LOG_LEVEL` - Logging level: DEBUG, INFO, WARNING, ERROR (default: INFO)

### Development Settings
- `DEBUG` - Enable debug mode (default: false)
- `ENVIRONMENT` - Environment name: development, staging, production (default: development)

### Token Storage
- `TOKEN_STORAGE_PATH` - Directory for token storage (default: ./tokens)
- `TOKEN_STORAGE_BACKEND` - Storage backend: file, memory, redis (default: file)

### Rate Limiting
- `RATE_LIMIT_CALLS` - API calls per period (default: 100)
- `RATE_LIMIT_PERIOD` - Period in seconds (default: 60)

### Cache Settings
- `CACHE_TTL` - Cache time-to-live in seconds (default: 300)
- `CACHE_ENABLED` - Enable/disable caching (default: true)

## Authentication Configuration (auth_config.yaml)

### OAuth Settings (`oauth`)
- `client_id` - OAuth client ID (can be overridden by env var)
- `client_secret` - OAuth client secret (can be overridden by env var)
- `authorization_url` - OAuth authorization endpoint
- `token_url` - OAuth token endpoint
- `redirect_uri` - OAuth callback URL (must match Intacct configuration)
- `scope` - OAuth scope, typically "full" for complete API access
- `use_pkce` - Enable PKCE for enhanced security (recommended)
- `use_state` - Enable state parameter for CSRF protection

### Authentication Flow (`auth_flow`)
- `preferred_flow` - Default auth flow: "authorization_code" or "client_credentials"
- `web_services_user` - Credentials for client credentials flow:
  - `sender_id` - Web Services sender ID
  - `sender_password` - Web Services password
- `auto_refresh` - Automatically refresh expiring tokens
- `refresh_buffer_seconds` - Seconds before expiry to refresh token

### Token Storage (`token_storage`)
- `backend` - Storage backend: "file", "memory", "redis"
- `file` - File storage settings:
  - `directory` - Token storage directory
  - `encryption_enabled` - Encrypt tokens at rest
- `cache_tokens` - Cache tokens in memory
- `cache_ttl_seconds` - Token cache duration
### Multi-Entity Support (`multi_entity`)
- `enabled` - Enable multi-entity support
- `selection_strategy` - How to select entity: "prompt", "first", "specific"
- `default_entity_id` - Default entity ID for "specific" strategy
- `per_entity_tokens` - Store separate tokens per entity

### Session Management (`session`)
- `timeout_seconds` - Session timeout (0 = no timeout)
- `keepalive_interval_seconds` - Interval for keepalive pings
- `max_concurrent_sessions` - Maximum concurrent sessions allowed

### Security Settings (`security`)
- `verify_ssl` - Validate SSL certificates
- `custom_headers` - Additional headers for all requests
- `allowed_ips` - IP allowlist (empty = allow all)
- `rate_limit` - Per-client rate limiting:
  - `enabled` - Enable rate limiting
  - `requests_per_minute` - Request limit per minute
  - `burst_size` - Burst capacity

### Logging (`logging`)
- `log_auth_events` - Log authentication events
- `log_token_refresh` - Log token refresh events
- `mask_sensitive_data` - Mask sensitive data in logs
- `masked_fields` - List of fields to mask

## Server Configuration (server_config.yaml)

### Server Settings (`server`)
- `name` - Server display name
- `version` - Server version
- `description` - Server description
- `host` - Bind address
- `port` - Listen port
- `transport` - MCP transport: "stdio", "streamable-http", "sse"
- `debug` - Enable debug mode
- `request_timeout` - Request timeout in seconds
- `max_request_size` - Maximum request size in bytes
### Module Configuration (`modules`)

Each module (accounts_payable, accounts_receivable, general_ledger) has:
- `enabled` - Enable/disable the module
- `priority` - Load priority (lower numbers load first)
- `custom_tools` - List of custom tools to enable
- `priority_endpoints` - Endpoints to load immediately (not lazily)
- `resource_templates` - Resource URI templates to register

### Performance Configuration (`performance`)

#### Lazy Loading
- `lazy_loading` - Load modules on demand

#### Cache (`cache`)
- `enabled` - Enable caching
- `ttl` - Cache time-to-live in seconds
- `max_size_mb` - Maximum cache size
- `strategy` - Eviction strategy: "lru" or "fifo"

#### Connection Pool (`connection_pool`)
- `max_connections` - Max connections per host
- `max_keepalive_connections` - Max keepalive connections
- `keepalive_timeout` - Keepalive timeout in seconds

#### Batching (`batching`)
- `enabled` - Enable request batching
- `max_batch_size` - Maximum batch size
- `batch_timeout_ms` - Batch collection timeout

#### Rate Limiting (`rate_limiting`)
- `requests_per_second` - Request rate limit
- `burst_size` - Burst capacity

### API Configuration (`api`)
- `base_url` - Intacct API base URL
- `version` - API version
- `retry` - Retry configuration:
  - `enabled` - Enable retries
  - `max_attempts` - Maximum retry attempts
  - `backoff_strategy` - "exponential", "linear", or "fixed"
  - `initial_delay_ms` - Initial retry delay
  - `max_delay_ms` - Maximum retry delay- `response` - Response handling:
  - `max_size_mb` - Maximum response size
  - `streaming_parse` - Parse responses incrementally

### Feature Flags (`features`)
- `cross_module_search` - Enable search across modules
- `batch_operations` - Enable batch operations
- `workflow_automation` - Enable workflow automation
- `custom_reports` - Enable custom report builder
- `audit_logging` - Enable audit logging
- `metrics` - Enable metrics collection

### Logging Configuration (`logging`)
- `level` - Log level: DEBUG, INFO, WARNING, ERROR
- `format` - Log format: "json" or "text"
- `handlers` - Log destinations:
  - `console` - Console logging settings
  - `file` - File logging settings
- `extra_fields` - Additional structured logging fields

### Monitoring Configuration (`monitoring`)
- `health_check` - Health check endpoint settings
- `metrics` - Metrics endpoint settings
- `tracing` - Distributed tracing settings

### Error Handling (`error_handling`)
- `include_stack_trace` - Include stack traces in errors
- `friendly_messages` - Use user-friendly error messages
- `map_intacct_errors` - Map Intacct errors to friendly messages
- `fallback` - Fallback behavior settings

## Configuration Best Practices

1. **Security**
   - Never commit actual credentials to version control
   - Use environment variables for sensitive data
   - Enable token encryption for file storage
   - Use PKCE for OAuth flows

2. **Performance**
   - Enable lazy loading for large deployments
   - Configure appropriate cache sizes
   - Use connection pooling
   - Enable request batching for bulk operations

3. **Monitoring**
   - Enable health checks for production
   - Configure appropriate log levels
   - Enable metrics for performance monitoring
   - Use structured logging for better searchability

4. **Development vs Production**
   - Use different configs for each environment
   - Disable debug mode in production
   - Configure appropriate rate limits
   - Enable SSL verification in production