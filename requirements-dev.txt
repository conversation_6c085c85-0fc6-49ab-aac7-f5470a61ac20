# Development dependencies for Sage Intacct MCP Server
# Include all core requirements
-r requirements.txt

# Testing framework
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0
pytest-timeout>=2.1.0

# HTTP mocking for tests
httpx-mock>=0.25.0
respx>=0.20.0

# Code quality and linting
black>=23.0.0
flake8>=6.0.0
flake8-docstrings>=1.7.0
isort>=5.12.0
mypy>=1.5.0

# Type stubs
types-pyyaml>=6.0.0
types-python-dateutil>=2.8.0
types-aiofiles>=23.0.0

# Documentation
sphinx>=7.0.0
sphinx-rtd-theme>=1.3.0

# Development utilities
ipython>=8.15.0
watchdog>=3.0.0
pre-commit>=3.4.0

# Performance profiling
py-spy>=0.3.14
memory-profiler>=0.61.0

# Security scanning
bandit>=1.7.5
safety>=2.3.5
