openapi: 3.0.3
info:
  title: General Ledger
  description: >
    The General Ledger is where a company creates and maintains accounts, journals, and financial reports. You use the General Ledger to post journal entries, design and run reports, create budgets, and more. Other Sage Intacct subscriptions automatically post transactions to the General Ledger in real-time.

  version: '1'
servers:
  - url: https://api.intacct.com/ia/api/v1
tags:
  - name: Account allocation basis
    description: >
      The account allocation basis defines how the allocation splits your source pool amount into each allocation focused dimension, it is the blueprint used when calculating dynamic allocations.

    x-displayName: Account allocation basis
  - name: Account allocation group members
    description: >
      Account allocation group members are the member accounts contained within an account allocation group. An account allocation group member contains the reference to the account allocation that is run as part of the account allocation group. Account allocation group member objects can only be created and updated through the owning account allocation group object.

    x-displayName: Account allocation group members
  - name: Account allocation groups
    description: >-
      When you have several allocations that run on the same interval (for example, at month end), creating an allocation group allows you to process these allocations efficiently. You can also order the members of an allocation group in the sequence they need to be processed in, for example, if an allocation is dependent on a different allocation being processed first.
    x-displayName: Account allocation groups
  - name: Account allocation reversals
    description: >
      Account allocation reverse creates offset entries to undo previous dynamic account allocation calculations.

    x-displayName: Account allocation reversals
  - name: Account allocation runs
    description: >
      Account allocations are where transactions are distributed across dimensions. Allocations are processed (run) to reflect accurate and up-to-date account balances.

    x-displayName: Account allocation runs
  - name: Account allocation sources
    description: >
      The account allocation source pool determines the source accounts from which Intacct dynamically finds amounts to use in the account allocation. You can use dimension filters to narrow the scope of your allocation by limiting the source pool, for example, to a specific department or location group.

    x-displayName: Account allocation sources
  - name: Account allocation targets
    description: >
      The account allocation target specifies where the amounts from the source pool are distributed when calculating the account allocation.

    x-displayName: Account allocation targets
  - name: Account allocation definitions
    description: >-
      Account allocations let you automatically distribute amounts across multiple dimensions such as departments, locations, projects, or classes.


      Before you generate an account allocation, you must create an account allocation definition. The account allocation definition enables you to record the rationale used for your allocation, the source pool, basis, and target entry in 1 place. After the definition is in place, you can generate the allocation.


      For example, you can create an account allocation definition that distributes expenses across revenue-earning departments, then run an account allocation using that definition for the dates you want.
    x-displayName: Account allocation definitions
  - name: Account categories
    description: >
      Account categories are high-level classifications that appear in a balance sheet or income statement. Sage Intacct provides ready-made account groups based on account categories for companies using the QuickStart template to simplify setup.

    x-displayName: Account categories
  - name: Account group category members
    description: >
      Account group category members are accounts within a company's Chart of Accounts that have been organized into logical categories. For example, the Assets account group may contain account group category members, such as Cash, Accounts Receivable, and Inventory in the Current Assets category.

    x-displayName: Account group category members
  - name: Computation account groups
    description: >
      Computation account groups consist of other account groups or individual accounts that you use as components in a mathematical equation; results of the equation display in financial reports. For more information, see [Account group types](https://www.intacct.com/ia/docs/en_US/help_action/Reporting/Setup/Account_groups/account-group-types.htm) in the Sage Intacct help center.

    x-displayName: Computation account groups
  - name: Account group members
    description: >-
      Account group members are the member accounts contained within an account group.
    x-displayName: Account group members
  - name: Account group purposes
    description: >-
      Account group purposes let you filter account groups according to why you might use them, which is particularly useful in financial reporting. You create account group purposes and then assign them to account groups when you create or update the groups.
    x-displayName: Account group purposes
  - name: Account groups
    description: "All financial reports include account groups, these indicate which accounting data to include in your  reports. For example, a report might include the \"Accounts Payable\" account group for a specific customer or in a specific location. \n\nAccount groups organize accounts into re-usable structures, creating the groupings and amounts that you want to see on reports. You can create as many groups as you need, from large hierarchical structures like \"Net Income\" to flat account groups such as \"Cash and Cash Equivalents,\" and then use them across many different reports, graphs, and dashboard performance cards."
    x-displayName: Account groups
  - name: Account ranges
    description: >-
      Account ranges are used to define specific ranges of general ledger accounts. Account groups can include account ranges as group members, along with individual named accounts.
    x-displayName: Account ranges
  - name: Accounts
    description: >-
      Companies post balance sheet, income statement, and statistical transactions to General Ledger accounts. Accounts make it easy to keep track of the transaction type, such as Accounts Payable, Accounts Receivable, Inventory, and the various sub-accounts for those transaction types. The comprehensive list of a company's General Ledger accounts is called its Chart of Accounts.
    x-displayName: Accounts
  - name: Budget details
    description: "A budget detail is a unique combination of account, department, location, and reporting period. (Department and location are optional.) If you want to track travel expenses for 3 locations, you would need 3 budget detail objects _per reporting period_:\n\n1. Travel Expenses account, Location A\n2. Travel Expenses account, Location B\n3. Travel Expenses account, Location C\n\nA `budget-detail` object corresponds to what is seen on the Budget amount details page for a specific reporting period in Sage Intacct. \n\nNote: `budget-detail` objects can only be created and updated through the owning `budget` object.\n"
    x-displayName: Budget details
  - name: Budgets
    description: >
      A budget is a plan to help estimate revenue and expenses for operations. The `budget` object is essentially a header that contains an array of `budget-detail` objects.

      Before you can create or import a budget, [several items in Sage Intacct must be set up](https://www.intacct.com/ia/docs/en_US/help_action/General_Ledger/Budgets_and_spending/Budgets/before-creating-your-budgets.htm):


      * Budgetable reporting periods: Reporting periods must exist and must be marked as Budgetable. Most often, budgetable periods are monthly.

      * Accounts: Each account for which you want to create a budget must exist in the Chart of Accounts.

      * Dimensions: Although using dimensions in budgets isn't required, dimensions such as Location, Department, or Employee can be included in a budget if they've been created in Intacct.

    x-displayName: Budgets
  - name: Journal entry lines
    description: >-
      Journal entry lines represent individual debit or credit line items within a [journal-entry](/openapi/gl/general-ledger.journal-entry/tag/Journal-entries). You can create or update line items by modifying the journal entry.


      When adding a line item to the journal entry, you can post it immediately or save it as a draft, which can be edited and posted later.
    x-displayName: Journal entry lines
  - name: Journal entry tax entries
    description: >-
      For VAT enabled transactions, journal entries will have tax entries. Providing multiple tax entries is allowed if the tax solution supports it (AU, GB, FR). For ZA, only one tax entry is allowed.
    x-displayName: Journal entry tax entries
  - name: Journal entries
    description: "Journal entries record accounting entries in the general ledger by specifying affected accounts, amounts and descriptions. Use them to create, update, or reverse entries while ensuring the general ledger remains balanced with offsetting debits and credits.\n\nIf GL Outlier Detection is enabled, Sage Intacct identifies journal entries and allocation splits that deviate from expected patterns. Outlier details are included when retrieving a journal entry object. "
    x-displayName: Journal entries
  - name: Journals
    description: >-
      Journals serve to categorize transactions that are directly entered in the General Ledger and the transactions that are posted from subledgers such as Accounts Payable. For more information see [What journals do you need?](https://www.intacct.com/ia/docs/en_US/help_action/General_Ledger/Setup/Journals/add-edit-journals.htm#Whatjournalsdoyouneed)
    x-displayName: Journals
  - name: Reporting periods
    description: >-
      Reporting periods are used when creating reports and budgets, and when opening and closing books. They limit the information in a report to specific time ranges.
    x-displayName: Reporting periods
  - name: Statistical accounts
    description: >-
      Statistical accounts are used to track non-financial data, such as employee headcount, number of members, or even the number of rooms in a hotel.
    x-displayName: Statistical accounts
  - name: Statistical journal entry lines
    description: >-
      Statistical journal entry lines represent transactions within a statistical journal entry and are created or updated by modifying the statistical journal-entry.


      When adding a line item, you can post it immediately or save it as a draft, which can be edited and posted later.
    x-displayName: Statistical journal entry lines
  - name: Statistical journal entries
    description: >
      Statistical journal entries record and track non-financial data alongside financial transactions. They help measure key performance indicators and provide valuable insights for business reporting and analysis.


      Use statistical journal entries to add, edit, or reverse statistical transactions within a journal before posting them to the general ledger for reporting and analysis.

    x-displayName: Statistical journal entries
  - name: Statistical journals
    description: >
      Statistical journals hold all non-monetary journal entries, which do not have a debit or credit as a transaction. Instead, statistical journal entries simply increase or decrease a value.

    x-displayName: Statistical journals
  - name: Transaction allocation template lines
    description: >-
      Transaction allocation template lines define how allocations are distributed. Allocation template lines can only be added, updated, and deleted through the owning txn-allocation-template object.
    x-displayName: Transaction allocation template lines
  - name: Transaction allocation templates
    description: "Transaction allocation templates (definitions) provide a way to save standard allocation combinations that are used routinely. You can define an allocation transaction to distribute amounts across multiple dimensions—such as departments, locations, projects, or classes—automatically.\n\nYou can allocate a percentage, a set (absolute) value, or a fixed amount with over/under logic. Each of these methods takes the transaction line amount and allocates it across the dimensions specified in the transaction allocation template. \n\nTransaction allocations are intended for use on single line items, allowing you to distribute amounts during entry of a transaction, usually a one-time action. Transaction allocations are only available for distribution across eight of the standard dimensions and have other restrictions as well.\n\nIf you want to automatically pull updated source balances and distribute them across dimensions according to defined basis calculations, you should use the Dynamic Allocations subscription. See Account Allocations for more info."
    x-displayName: Transaction allocation templates
paths:
  /objects/general-ledger/account-allocation-basis:
    get:
      summary: List account allocation basis objects
      description: >-
        Returns a collection with a key, ID, and link for each account allocation basis.
      tags:
        - Account allocation basis
      operationId: list-general-ledger-account-allocation-basis
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of account allocation basis objects
                properties:
                  ia::result:
                    type: array
                    items:
                      $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata-pages'
              examples:
                List account allocation basis objects:
                  value:
                    ia::result:
                      - key: '16'
                        id: '16'
                        href: /objects/general-ledger/account-allocation-basis/16
                      - key: '20'
                        id: '20'
                        href: /objects/general-ledger/account-allocation-basis/20
                      - key: '21'
                        id: '21'
                        href: /objects/general-ledger/account-allocation-basis/21
                    ia::meta:
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/account-allocation-basis/{key}:
    parameters:
      - name: key
        description: System-assigned key for the account allocation basis.
        in: path
        required: true
        schema:
          type: string
        example: '178'
    get:
      summary: Get an account allocation basis
      description: Returns detailed information for a specified account allocation basis.
      tags:
        - Account allocation basis
      operationId: get-general-ledger-account-allocation-basis-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the account allocation basis
                properties:
                  ia::result:
                    $ref: '#/components/schemas/general-ledger-account-allocation-basis'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Get an account allocation basis:
                  value:
                    ia::result:
                      id: '21'
                      key: '21'
                      glAccountAllocation:
                        id: '29'
                        key: '29'
                        href: /objects/general-ledger/account-allocation/29
                      glAccountGroup:
                        id: Basis
                        key: '623'
                        href: /objects/general-ledger/account-group/623
                      accumulation: activity
                      timePeriod:
                        id: Current Month
                        key: '395'
                        href: /objects/general-ledger/reporting-period/395
                      allocationMethod: dynamicRelativeAccountFinancial
                      reportingBook: accrual
                      useAmountsFrom: mainReportingBookAndAlternateBooks
                      skipNegative: false
                      dimensions:
                        location:
                          key: '1'
                          id: '1'
                          name: United States of America
                          href: /objects/company-config/location/1
                        department:
                          key: '3'
                          id: '3'
                          name: Engineering
                          href: /objects/company-config/department/3
                        project:
                          key: null
                          name: null
                          id: null
                        customer:
                          key: null
                          name: null
                          id: null
                        vendor:
                          key: null
                          name: null
                          id: null
                        employee:
                          key: null
                          name: null
                          id: null
                        item:
                          key: null
                          name: null
                          id: null
                        class:
                          key: null
                          name: null
                          id: null
                        contract:
                          key: null
                          name: null
                          id: null
                        warehouse:
                          key: null
                          name: null
                          id: null
                      href: /objects/general-ledger/account-allocation-basis/21
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    patch:
      summary: Update an account allocation basis
      description: >-
        Updates an existing account allocation basis object by setting field values. Any fields not provided remain unchanged.
      tags:
        - Account allocation basis
      operationId: update-general-ledger-account-allocation-basis-key
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/general-ledger-account-allocation-basis'
            examples:
              Update an account allocation basis:
                value:
                  skipNegative: true
                  allocationMethod: dynamicRelativeAccountFinancial
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated account allocation basis
                properties:
                  ia::result:
                    $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Reference to updated account allocation basis:
                  value:
                    ia::result:
                      key: '21'
                      id: '21'
                      href: /objects/general-ledger/account-allocation-basis/21
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/account-allocation-group-member:
    get:
      summary: List account allocation group members
      description: >-
        Returns a collection with a key, ID, and link for each group member. This operation is mostly for use in testing; use the query service to find allocation group members that meet specific criteria and to specify the properties that you want in the response.
      tags:
        - Account allocation group members
      operationId: list-general-ledger-account-allocation-group-member
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of account allocation group members
                properties:
                  ia::result:
                    type: array
                    items:
                      $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata-pages'
              examples:
                List account allocation group members:
                  value:
                    ia::result:
                      - key: '1'
                        id: '1'
                        href: '/objects/general-ledger/account-allocation-group-member/1'
                      - key: '2'
                        id: '2'
                        href: '/objects/general-ledger/account-allocation-group-member/2'
                    ia::meta:
                      totalCount: 2
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/account-allocation-group-member/{key}:
    parameters:
      - name: key
        description: System-assigned key for the account allocation group member.
        in: path
        required: true
        schema:
          type: string
        example: '99'
    get:
      summary: Get an account allocation group member
      description: >-
        Returns detailed information for a specified account allocation group member.
      tags:
        - Account allocation group members
      operationId: get-general-ledger-account-allocation-group-member-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the account allocation group member
                properties:
                  ia::result:
                    $ref: '#/components/schemas/general-ledger-account-allocation-group-member'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Get an account allocation group member:
                  value:
                    ia::result:
                      id: '1'
                      key: '1'
                      glAccountAllocationGroup:
                        id: Fringe Group
                        key: '1'
                        href: /objects/general-ledger/account-allocation-group/1
                      glAccountAllocation:
                        name: Fringe01
                        id: '1'
                        key: '1'
                        href: /objects/general-ledger/account-allocation/1
                      status: active
                      audit:
                        createdDateTime: '2025-01-28T16:20:22Z'
                        modifiedDateTime: '2025-01-28T16:20:22Z'
                        createdByUser:
                          key: '159'
                          href: /objects/company-config/user/159
                        createdBy: '159'
                        modifiedByUser:
                          key: '159'
                          href: /objects/company-config/user/159
                        modifiedBy: '159'
                      href: '/objects/general-ledger/account-allocation-group-member/1'
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/account-allocation-group:
    get:
      summary: List account allocation groups
      description: >-
        Returns a collection with a key, ID, and link for each account allocation group. This operation is mostly for use in testing; use the query service to find allocation groups that meet specific criteria and to specify the properties that you want in the response.
      tags:
        - Account allocation groups
      operationId: list-general-ledger-account-allocation-group
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of account allocation groups
                properties:
                  ia::result:
                    type: array
                    items:
                      $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata-pages'
              examples:
                List account allocation groups:
                  value:
                    ia::result:
                      - key: '1'
                        id: '1'
                        href: /objects/general-ledger/account-allocation-group/1
                      - key: '2'
                        id: '2'
                        href: /objects/general-ledger/account-allocation-group/2
                    ia::meta:
                      totalCount: 2
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    post:
      summary: Create an account allocation group
      description: Creates a new account allocation group.
      tags:
        - Account allocation groups
      operationId: create-general-ledger-account-allocation-group
      requestBody:
        description: Create a new account allocation group.
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/general-ledger-account-allocation-group'
                - $ref: '#/components/schemas/general-ledger-account-allocation-groupRequiredProperties'
            examples:
              Create an account allocation group:
                value:
                  name: Month end
                  description: All month end allocations for the company.
                  errorProcessingMethod: skipAndContinue
                  lines:
                    - glAccountAllocation:
                        id: '1'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New account allocation group
                properties:
                  ia::result:
                    $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Reference to new account allocation group:
                  value:
                    ia::result:
                      key: '28'
                      id: '28'
                      href: /objects/general-ledger/account-allocation-group/28
                    ia::meta:
                      totalCount: 1
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/account-allocation-group/{key}:
    parameters:
      - name: key
        description: System-assigned key for the account allocation group.
        in: path
        required: true
        schema:
          type: string
        example: '178'
    get:
      summary: Get an account allocation group
      description: Returns detailed information for a specified account allocation group.
      tags:
        - Account allocation groups
      operationId: get-general-ledger-account-allocation-group-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the account allocation group
                properties:
                  ia::result:
                    $ref: '#/components/schemas/general-ledger-account-allocation-group'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Get an account allocation group:
                  value:
                    ia::result:
                      key: '5'
                      id: '5'
                      name: Month end
                      description: All month end allocations for the company.
                      status: active
                      errorProcessingMethod: skipAndContinue
                      audit:
                        createdDateTime: '2024-10-08T13:29:18Z'
                        modifiedDateTime: '2024-10-08T13:29:18Z'
                        createdByUser:
                          key: '34'
                          id: Admin
                          href: /objects/company-config/user/34
                        modifiedByUser:
                          key: '1'
                          id: Aman
                          href: /objects/company-config/user/1
                      lines:
                        - key: '6'
                          id: '6'
                          glAccountAllocationGroup:
                            id: Month end
                            key: '5'
                            href: /objects/general-ledger/account-allocation-group/5
                          glAccountAllocation:
                            id: '8'
                            key: '8'
                            href: /objects/general-ledger/account-allocation/8
                          status: active
                          audit:
                            createdDateTime: '2024-10-08T13:29:18Z'
                            modifiedDateTime: '2024-10-08T13:29:18Z'
                            createdByUser:
                              key: '34'
                              id: Admin
                              href: /objects/company-config/user/34
                            modifiedByUser:
                              key: '1'
                              id: Aman
                              href: /objects/company-config/user/1
                          href: '/objects/general-ledger/account-allocation-group-member/6'
                      href: /objects/general-ledger/account-allocation-group/5
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    patch:
      summary: Update an account allocation group
      description: >-
        Updates an existing account allocation group by setting field values. Any fields not provided remain unchanged.
      tags:
        - Account allocation groups
      operationId: update-general-ledger-account-allocation-group-key
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/general-ledger-account-allocation-group'
                - type: object
                  properties:
                    id:
                      readOnly: true
            examples:
              Update an account allocation group:
                value:
                  name: Interest allocation
                  description: Compute interest allocation.
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated account allocation group
                properties:
                  ia::result:
                    $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Reference to updated account allocation group:
                  value:
                    ia::result:
                      key: '21'
                      id: '21'
                      href: /objects/general-ledger/account-allocation-group/21
                    ia::meta:
                      totalCount: 1
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    delete:
      summary: Delete an account allocation group
      description: Deletes an account allocation group.
      tags:
        - Account allocation groups
      operationId: delete-general-ledger-account-allocation-group-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/account-allocation-reverse:
    get:
      summary: List account allocation reversals
      description: >-
        Returns a collection with a key, ID, and link for each account allocation reversal.
      tags:
        - Account allocation reversals
      operationId: list-general-ledger-account-allocation-reverse
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of account allocation reversals
                properties:
                  ia::result:
                    type: array
                    items:
                      $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata-pages'
              examples:
                List account allocation reversals:
                  value:
                    ia::result:
                      - key: '16'
                        id: '16'
                        href: /objects/general-ledger/account-allocation-reverse/16
                      - key: '20'
                        id: '20'
                        href: /objects/general-ledger/account-allocation-reverse/20
                      - key: '21'
                        id: '21'
                        href: /objects/general-ledger/account-allocation-reverse/21
                    ia::meta:
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/account-allocation-reverse/{key}:
    parameters:
      - name: key
        description: System-assigned key for the account allocation reversal.
        in: path
        required: true
        schema:
          type: string
        example: '178'
    get:
      summary: Get an account allocation reversal
      description: >-
        Returns detailed information for a specified account allocation reversal.
      tags:
        - Account allocation reversals
      operationId: get-general-ledger-account-allocation-reverse-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the account allocation reversal
                properties:
                  ia::result:
                    $ref: '#/components/schemas/general-ledger-account-allocation-reverse'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Get an account allocation reversal:
                  value:
                    ia::result:
                      id: '21'
                      key: '21'
                      glAccountAllocation:
                        id: '29'
                        key: '29'
                        href: /objects/general-ledger/account-allocation/29
                      glAccount:
                        id: null
                        key: null
                        name: null
                      useSourceAccount: true
                      audit:
                        createdDateTime: '2024-06-25T12:16:48Z'
                        modifiedDateTime: '2024-06-25T12:58:13Z'
                        createdBy: '1'
                        modifiedBy: '1'
                      dimensions:
                        location:
                          key: '72'
                          id: AZ
                          name: Arizona
                          href: /objects/company-config/location/72
                        department:
                          key: '6'
                          id: '6'
                          name: Marketing
                          href: /objects/company-config/department/6
                        project:
                          key: null
                          name: null
                          id: null
                        customer:
                          key: null
                          name: null
                          id: null
                        vendor:
                          key: null
                          name: null
                          id: null
                        employee:
                          key: null
                          name: null
                          id: null
                        item:
                          key: null
                          name: null
                          id: null
                        class:
                          key: null
                          name: null
                          id: null
                        contract:
                          key: null
                          name: null
                          id: null
                        warehouse:
                          key: null
                          name: null
                          id: null
                      href: /objects/general-ledger/account-allocation-reverse/21
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    patch:
      summary: Update an account allocation reversal
      description: >-
        Updates an existing account allocation reversal by setting field values. Any fields not provided remain unchanged.
      tags:
        - Account allocation reversals
      operationId: update-general-ledger-account-allocation-reverse-key
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/general-ledger-account-allocation-reverse'
            examples:
              Update an account allocation reversal:
                value:
                  glAccount:
                    id: '1000'
                  useSourceAccount: false
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated account allocation reversal
                properties:
                  ia::result:
                    $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Reference to updated account allocation reversal:
                  value:
                    ia::result:
                      key: '21'
                      id: '21'
                      href: /objects/general-ledger/account-allocation-reverse/21
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/account-allocation-run:
    get:
      summary: List account allocation runs
      description: >-
        Returns a collection with a key, ID, and link for each account allocation run.
      tags:
        - Account allocation runs
      operationId: list-general-ledger-account-allocation-run
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of account-allocation-run objects
                properties:
                  ia::result:
                    type: array
                    items:
                      $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata-pages'
              examples:
                List account allocation runs:
                  value:
                    ia::result:
                      - key: '105'
                        id: '105'
                        href: /objects/general-ledger/account-allocation-run/105
                      - key: '106'
                        id: '106'
                        href: /objects/general-ledger/account-allocation-run/106
                      - key: '108'
                        id: '108'
                        href: /objects/general-ledger/account-allocation-run/108
                    ia::meta:
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    post:
      summary: Create an account allocation run
      description: Creates a new account allocation run.
      tags:
        - Account allocation runs
      operationId: create-general-ledger-account-allocation-run
      requestBody:
        description: Create an account allocation run
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/general-ledger-account-allocation-run'
                - $ref: '#/components/schemas/general-ledger-account-allocation-runRequiredProperties'
            examples:
              Create an account allocation run:
                value:
                  asOfDate: '2024-08-24'
                  glPostingDate: '2024-10-25'
                  email: <EMAIL>
                  accountAllocationGroup:
                    id: '5'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New account allocation run
                properties:
                  ia::result:
                    $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Create a new account allocation run:
                  value:
                    ia::result:
                      id: '97'
                      key: '97'
                      href: /objects/general-ledger/account-allocation-run/97
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/account-allocation-run/{key}:
    parameters:
      - name: key
        description: System-assigned unique key for the account allocation run.
        in: path
        required: true
        schema:
          type: string
        example: '178'
    get:
      summary: Get an account allocation run
      description: Returns detailed information for the specified account allocation run.
      tags:
        - Account allocation runs
      operationId: get-general-ledger-account-allocation-run-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the account allocation run
                properties:
                  ia::result:
                    $ref: '#/components/schemas/general-ledger-account-allocation-run'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Get an account allocation run:
                  value:
                    ia::result:
                      id: '97'
                      key: '97'
                      asOfDate: '2024-08-24'
                      glPostingDate: '2024-10-25'
                      email: <EMAIL>
                      accountAllocation:
                        id: null
                        key: null
                      allocationType: null
                      accountAllocationGroup:
                        name: Employee allocation group
                        id: '5'
                        key: '5'
                        href: /objects/general-ledger/account-allocation-group/5
                      parent:
                        id: null
                        key: null
                      state: partialSuccess
                      allocationRunType: regular
                      message: null
                      audit:
                        createdDateTime: '2024-09-20T05:17:09Z'
                        modifiedDateTime: '2024-09-20T05:17:58Z'
                        createdBy: '1'
                        modifiedBy: '1'
                      entity:
                        key: null
                        id: null
                        name: null
                      dimensions:
                        project:
                          key: null
                          id: null
                          name: null
                        employee:
                          key: null
                          id: null
                          name: null
                      href: /objects/general-ledger/account-allocation-run/97
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    delete:
      summary: Delete an account allocation run
      description: Deletes an account allocation run.
      tags:
        - Account allocation runs
      operationId: delete-general-ledger-account-allocation-run-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/account-allocation-source:
    get:
      summary: List account allocation sources
      description: >-
        Returns a collection with a key, ID, and link for each account allocation source.
      tags:
        - Account allocation sources
      operationId: list-general-ledger-account-allocation-source
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of account allocation sources
                properties:
                  ia::result:
                    type: array
                    items:
                      $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata-pages'
              examples:
                List account allocation sources:
                  value:
                    ia::result:
                      - key: '23'
                        id: '23'
                        href: /objects/general-ledger/account-allocation-source/23
                      - key: '27'
                        id: '27'
                        href: /objects/general-ledger/account-allocation-source/27
                      - key: '28'
                        id: '28'
                        href: /objects/general-ledger/account-allocation-source/28
                    ia::meta:
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/account-allocation-source/{key}:
    parameters:
      - name: key
        description: System-assigned unique key for the account allocation source.
        in: path
        required: true
        schema:
          type: string
        example: '178'
    get:
      summary: Get an account allocation source
      description: Returns detailed information for a specified account allocation source.
      tags:
        - Account allocation sources
      operationId: get-general-ledger-account-allocation-source-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the account allocation source
                properties:
                  ia::result:
                    $ref: '#/components/schemas/general-ledger-account-allocation-source'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Get an account allocation source:
                  value:
                    ia::result:
                      id: '28'
                      key: '28'
                      glAccountAllocation:
                        id: '29'
                        key: '29'
                        href: /objects/general-ledger/account-allocation/29
                      glAccountGroup:
                        id: source
                        key: '622'
                        href: /objects/general-ledger/account-group/622
                      percentToAllocate: '100'
                      timePeriod:
                        id: Current Month
                        key: '395'
                        href: /objects/general-ledger/reporting-period/395
                      reportingBook: accrual
                      useAmountsFrom: mainReportingBookAndAlternateBooks
                      currency: USD
                      audit:
                        createdDateTime: '2024-06-25T12:16:47Z'
                        modifiedDateTime: '2024-06-25T12:34:07Z'
                        createdBy: '1'
                        modifiedBy: '1'
                      dimensions:
                        location:
                          key: '1'
                          id: '1'
                          name: United States of America
                          href: /objects/company-config/location/1
                        department:
                          key: '3'
                          id: '3'
                          name: Engineering
                          href: /objects/company-config/department/3
                        project:
                          key: null
                          name: null
                          id: null
                        customer:
                          key: null
                          name: null
                          id: null
                        vendor:
                          key: null
                          name: null
                          id: null
                        employee:
                          key: null
                          name: null
                          id: null
                        item:
                          key: null
                          name: null
                          id: null
                        class:
                          key: null
                          name: null
                          id: null
                        contract:
                          key: null
                          name: null
                          id: null
                        warehouse:
                          key: null
                          name: null
                          id: null
                      href: /objects/general-ledger/account-allocation-source/28
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    patch:
      summary: Update an account allocation source
      description: >-
        Updates an existing account allocation source by setting field values. Any fields not provided remain unchanged.
      tags:
        - Account allocation sources
      operationId: update-general-ledger-account-allocation-source-key
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/general-ledger-account-allocation-source'
            examples:
              Update an account allocation source:
                value:
                  percentToAllocate: '70'
                  timePeriod:
                    key: '400'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated account allocation source
                properties:
                  ia::result:
                    $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Reference to updated account allocation source:
                  value:
                    ia::result:
                      key: '28'
                      id: '28'
                      href: /objects/general-ledger/account-allocation-source/28
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/account-allocation-target:
    get:
      summary: List account allocation targets
      description: >-
        Returns a collection with a key, ID, and link for each account allocation target.
      tags:
        - Account allocation targets
      operationId: list-general-ledger-account-allocation-target
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of account allocation targets
                properties:
                  ia::result:
                    type: array
                    items:
                      $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata-pages'
              examples:
                List account allocation targets:
                  value:
                    ia::result:
                      - key: '16'
                        id: '16'
                        href: /objects/general-ledger/account-allocation-target/16
                      - key: '20'
                        id: '20'
                        href: /objects/general-ledger/account-allocation-target/20
                      - key: '21'
                        id: '21'
                        href: /objects/general-ledger/account-allocation-target/21
                    ia::meta:
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/account-allocation-target/{key}:
    parameters:
      - name: key
        description: System-assigned key for the account allocation target.
        in: path
        required: true
        schema:
          type: string
        example: '178'
    get:
      summary: Get an account allocation target
      description: Returns detailed information for a specified account allocation target.
      tags:
        - Account allocation targets
      operationId: get-general-ledger-account-allocation-target-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the account allocation target
                properties:
                  ia::result:
                    $ref: '#/components/schemas/general-ledger-account-allocation-target'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Get an account allocation target:
                  value:
                    ia::result:
                      id: '21'
                      key: '21'
                      glAccountAllocation:
                        id: '29'
                        key: '29'
                        href: /objects/general-ledger/account-allocation/29
                      journal:
                        key: '39'
                        title: Accommodation Expenses
                        id: Others
                        href: /objects/general-ledger/journal/39
                      exchangeRate:
                        typeId: '1'
                      glAccount:
                        id: '1105'
                        key: '378'
                        name: Target
                        href: /objects/general-ledger/account/378
                      isBillable: false
                      audit:
                        createdDateTime: '2024-06-25T12:16:47Z'
                        modifiedDateTime: '2024-06-25T12:58:12Z'
                        createdBy: '1'
                        modifiedBy: '1'
                      dimensions:
                        location:
                          key: '72'
                          id: AZ
                          name: Arizona
                          href: /objects/company-config/location/72
                        department:
                          key: null
                          id: null
                          name: null
                        project:
                          key: null
                          name: null
                          id: null
                        customer:
                          key: '1'
                          name: Power Aerospace Materials
                          id: '1'
                          href: /objects/accounts-receivable/customer/1
                        vendor:
                          key: null
                          name: null
                          id: null
                        employee:
                          key: null
                          name: null
                          id: null
                        item:
                          key: null
                          name: null
                          id: null
                        class:
                          key: null
                          name: null
                          id: null
                        contract:
                          key: null
                          name: null
                          id: null
                        warehouse:
                          key: null
                          name: null
                          id: null
                      href: /objects/general-ledger/account-allocation-target/21
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    patch:
      summary: Update an account allocation target
      description: >-
        Updates an existing account allocation target by setting field values. Any fields not provided remain unchanged.
      tags:
        - Account allocation targets
      operationId: update-general-ledger-account-allocation-target-key
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/general-ledger-account-allocation-target'
            examples:
              Update an account allocation target:
                value:
                  isBillable: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated account allocation target
                properties:
                  ia::result:
                    $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Reference to updated account allocation target:
                  value:
                    ia::result:
                      key: '21'
                      id: '21'
                      href: /objects/general-ledger/account-allocation-target/21
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/account-allocation:
    get:
      summary: List account allocation definitions
      description: >-
        Returns a collection with a key, ID, and link for each account allocation definition. This operation is mostly for use in testing; use the query service to find allocation definitions that meet specific criteria and to specify the properties that you want in the response.
      tags:
        - Account allocation definitions
      operationId: list-general-ledger-account-allocation
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of account allocation definitions
                properties:
                  ia::result:
                    type: array
                    items:
                      $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata-pages'
              examples:
                List account allocation definitions:
                  value:
                    ia::result:
                      - key: '23'
                        id: '23'
                        href: /objects/general-ledger/account-allocation/23
                      - key: '27'
                        id: '27'
                        href: /objects/general-ledger/account-allocation/27
                      - key: '21'
                        id: '21'
                        href: /objects/general-ledger/account-allocation/21
                    ia::meta:
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    post:
      summary: Create an account allocation definition
      description: Create a new account allocation definition.
      tags:
        - Account allocation definitions
      operationId: create-general-ledger-account-allocation
      requestBody:
        description: Create an account allocation definition.
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/general-ledger-account-allocation'
                - $ref: '#/components/schemas/general-ledger-account-allocationRequiredProperties'
            examples:
              Creates an account allocation definition:
                value:
                  name: Monthly Expenses RE
                  description: Monthly allocation of expenses
                  methodology: >-
                    Monthly Expense allocation across revenue earning departments
                  status: active
                  activityDelta: false
                  autoReversePriorPostedJournalEntry: false
                  dimensionTreatment:
                    location: preserveValues
                    department: allocationFocus
                    project: notConsidered
                    customer: notConsidered
                    vendor: notConsidered
                    employee: notConsidered
                    item: notConsidered
                    class: notConsidered
                    contract: notConsidered
                    warehouse: notConsidered
                  latestVersion: null
                  allowAllocation: withinOneEntity
                  glAccountAllocationSource:
                    - glAccountGroup:
                        id: source
                      percentToAllocate: '100'
                      timePeriod:
                        id: Current Month
                      reportingBook: accrual
                      useAmountsFrom: mainReportingBookAndAlternateBooks
                      currency: USD
                      dimensions:
                        location:
                          id: '1'
                        department:
                          id: '3'
                        employee:
                          id: '1'
                  glAccountAllocationBasis:
                    - glAccountGroup:
                        id: Basis
                      accumulation: activity
                      timePeriod:
                        id: Current Month
                      allocationMethod: dynamicRelativeAccountFinancial
                      reportingBook: accrual
                      useAmountsFrom: mainReportingBookAndAlternateBooks
                      skipNegative: false
                      dimensions:
                        location:
                          id: '1'
                        employee:
                          id: '1'
                  glAccountAllocationTarget:
                    - journal:
                        id: Others
                      glAccount:
                        id: '1105'
                      isBillable: false
                      dimensions:
                        location:
                          id: AZ
                        customer:
                          id: '1'
                  glAccountAllocationReverse:
                    - useSourceAccount: true
                      dimensions:
                        location:
                          id: AZ
                        department:
                          id: '6'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New account allocation definition
                properties:
                  ia::result:
                    $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Reference to new account allocation definition:
                  value:
                    ia::result:
                      id: '29'
                      key: '29'
                      href: /objects/general-ledger/account-allocation/29
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/account-allocation/{key}:
    parameters:
      - name: key
        description: System-assigned key for the account allocation definition.
        in: path
        required: true
        schema:
          type: string
        example: '178'
    get:
      summary: Get an account allocation definition
      description: >-
        Returns detailed information for a specified account allocation definition.
      tags:
        - Account allocation definitions
      operationId: get-general-ledger-account-allocation-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the account allocation definition
                properties:
                  ia::result:
                    $ref: '#/components/schemas/general-ledger-account-allocation'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Get an account allocation definition:
                  value:
                    ia::result:
                      id: '29'
                      key: '29'
                      name: Monthly Expenses RE
                      description: Monthly allocation of expenses
                      methodology: >-
                        Monthly Expense allocation across revenue earning departments
                      status: active
                      activityDelta: false
                      autoReversePriorPostedJournalEntry: false
                      dimensionTreatment:
                        location: preserveValues
                        department: allocationFocus
                        project: notConsidered
                        customer: notConsidered
                        vendor: notConsidered
                        employee: notConsidered
                        item: notConsidered
                        class: notConsidered
                        contract: notConsidered
                        warehouse: notConsidered
                      latestVersion: null
                      audit:
                        createdDateTime: '2024-06-25T12:16:47Z'
                        modifiedDateTime: '2024-06-25T12:16:47Z'
                        createdBy: Admin
                        modifiedBy: Admin
                      journal:
                        id: Travel
                        key: '41'
                        href: /objects/general-ledger/journal/41
                      allowAllocation: withinOneEntity
                      attachment:
                        id: null
                        key: null
                      entity:
                        key: null
                        id: null
                        name: null
                      glAccountAllocationSource:
                        - id: '28'
                          key: '28'
                          glAccountAllocation:
                            id: '29'
                            key: '29'
                            href: /objects/general-ledger/account-allocation/29
                          glAccountGroup:
                            id: source
                            key: '622'
                            href: /objects/general-ledger/account-group/622
                          percentToAllocate: '100'
                          timePeriod:
                            id: Current Month
                            key: '395'
                            href: /objects/general-ledger/reporting-period/395
                          reportingBook: accrual
                          useAmountsFrom: mainReportingBookAndAlternateBooks
                          currency: USD
                          audit:
                            createdDateTime: '2024-06-25T12:16:47Z'
                            modifiedDateTime: '2024-06-25T12:16:47Z'
                            createdBy: '1'
                            modifiedBy: '1'
                          dimensions:
                            location:
                              key: '1'
                              id: '1'
                              name: United States of America
                              href: /objects/company-config/location/1
                            department:
                              key: '3'
                              id: '3'
                              name: Engineering
                              href: /objects/company-config/department/3
                            project:
                              key: null
                              name: null
                              id: null
                            customer:
                              key: null
                              name: null
                              id: null
                            vendor:
                              key: null
                              name: null
                              id: null
                            employee:
                              key: '1'
                              name: John Smith
                              id: '1'
                              href: /objects/company-config/employee/1
                            item:
                              key: null
                              name: null
                              id: null
                            class:
                              key: null
                              name: null
                              id: null
                            contract:
                              key: null
                              name: null
                              id: null
                            warehouse:
                              key: null
                              name: null
                              id: null
                            task:
                              id: null
                          href: /objects/general-ledger/account-allocation-source/28
                      glAccountAllocationBasis:
                        - id: '21'
                          key: '21'
                          glAccountAllocation:
                            id: '29'
                            key: '29'
                            href: /objects/general-ledger/account-allocation/29
                          glAccountGroup:
                            id: Basis
                            key: '623'
                            href: /objects/general-ledger/account-group/623
                          accumulation: activity
                          timePeriod:
                            id: Current Month
                            key: '395'
                            href: /objects/general-ledger/reporting-period/395
                          allocationMethod: dynamicRelativeAccountFinancial
                          reportingBook: accrual
                          useAmountsFrom: mainReportingBookAndAlternateBooks
                          skipNegative: false
                          dimensions:
                            location:
                              key: '1'
                              id: '1'
                              name: United States of America
                              href: /objects/company-config/location/1
                            department:
                              key: null
                              id: null
                              name: null
                            project:
                              key: null
                              name: null
                              id: null
                            customer:
                              key: null
                              name: null
                              id: null
                            vendor:
                              key: null
                              name: null
                              id: null
                            employee:
                              key: '1'
                              name: John Smith
                              id: '1'
                              href: /objects/company-config/employee/1
                            item:
                              key: null
                              name: null
                              id: null
                            class:
                              key: null
                              name: null
                              id: null
                            contract:
                              key: null
                              name: null
                              id: null
                            warehouse:
                              key: null
                              name: null
                              id: null
                            task:
                              id: null
                          href: /objects/general-ledger/account-allocation-basis/21
                      glAccountAllocationTarget:
                        - id: '21'
                          key: '21'
                          glAccountAllocation:
                            id: '29'
                            key: '29'
                            href: /objects/general-ledger/account-allocation/29
                          journal:
                            key: '39'
                            title: Accommodation Expenses
                            id: Others
                            href: /objects/general-ledger/journal/39
                          glAccount:
                            id: '1105'
                            key: '378'
                            name: Target
                            href: /objects/general-ledger/account/378
                          isBillable: false
                          audit:
                            createdDateTime: '2024-06-25T12:16:47Z'
                            modifiedDateTime: '2024-06-25T12:16:47Z'
                            createdBy: '1'
                            modifiedBy: '1'
                          dimensions:
                            location:
                              key: '72'
                              id: AZ
                              name: Arizona
                              href: /objects/company-config/location/72
                            department:
                              key: null
                              id: null
                              name: null
                            project:
                              key: null
                              name: null
                              id: null
                            customer:
                              key: '1'
                              name: Power Aerospace Materials
                              id: '1'
                              href: /objects/accounts-receivable/customer/1
                            vendor:
                              key: null
                              name: null
                              id: null
                            employee:
                              key: null
                              name: null
                              id: null
                            item:
                              key: null
                              name: null
                              id: null
                            class:
                              key: null
                              name: null
                              id: null
                            contract:
                              key: null
                              name: null
                              id: null
                            warehouse:
                              key: null
                              name: null
                              id: null
                          href: /objects/general-ledger/account-allocation-target/21
                      glAccountAllocationReverse:
                        - id: '21'
                          key: '21'
                          glAccountAllocation:
                            id: '29'
                            key: '29'
                            href: /objects/general-ledger/account-allocation/29
                          glAccount:
                            id: null
                            key: null
                            name: null
                          useSourceAccount: true
                          audit:
                            createdDateTime: '2024-06-25T12:16:48Z'
                            modifiedDateTime: '2024-06-25T12:16:48Z'
                            createdBy: '1'
                            modifiedBy: '1'
                          dimensions:
                            location:
                              key: '72'
                              id: AZ
                              name: Arizona
                              href: /objects/company-config/location/72
                            department:
                              key: '6'
                              id: '6'
                              name: Marketing
                              href: /objects/company-config/department/6
                            project:
                              key: null
                              name: null
                              id: null
                            customer:
                              key: null
                              name: null
                              id: null
                            vendor:
                              key: null
                              name: null
                              id: null
                            employee:
                              key: null
                              name: null
                              id: null
                            item:
                              key: null
                              name: null
                              id: null
                            class:
                              key: null
                              name: null
                              id: null
                            contract:
                              key: null
                              name: null
                              id: null
                            warehouse:
                              key: null
                              name: null
                              id: null
                          href: '/objects/general-ledger/account-allocation-reverse/21'
                      href: /objects/general-ledger/account-allocation/29
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    patch:
      summary: Update an account allocation definition
      description: >-
        Updates an existing account allocation definition by setting field values. Any fields not provided remain unchanged.
      tags:
        - Account allocation definitions
      operationId: update-general-ledger-account-allocation-key
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/general-ledger-account-allocation'
                - type: object
                  properties:
                    id:
                      readOnly: true
            examples:
              Updates an account allocation definition:
                value:
                  description: Yearly allocation of expenses
                  methodology: Yearly Expense allocation across revenue earning departments
                  activityDelta: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated account allocation definition
                properties:
                  ia::result:
                    $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Reference to updated account allocation definition:
                  value:
                    ia::result:
                      id: '29'
                      key: '29'
                      href: /objects/general-ledger/account-allocation/29
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    delete:
      summary: Delete an account allocation definition
      description: Deletes an account allocation definition.
      tags:
        - Account allocation definitions
      operationId: delete-general-ledger-account-allocation-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/account-category:
    get:
      summary: List account categories
      description: Returns a collection with a key, ID, and link for each account category.
      tags:
        - Account categories
      operationId: list-general-ledger-account-category
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of account categories
                properties:
                  ia::result:
                    type: array
                    items:
                      $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata-pages'
              examples:
                List account categories:
                  value:
                    ia::result:
                      - key: '129'
                        id: Patents
                        href: /objects/general-ledger/account-category/129
                      - key: '130'
                        id: Trademark
                        href: /objects/general-ledger/account-category/130
                      - key: '131'
                        id: Copyrights
                        href: /objects/general-ledger/account-category/131
                    ia::meta:
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: 4
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/account-category/{key}:
    parameters:
      - name: key
        description: System-assigned unique key for the account category.
        in: path
        required: true
        schema:
          type: string
        example: '178'
    get:
      summary: Get an account category
      description: Returns detailed information for a specified account category.
      tags:
        - Account categories
      operationId: get-general-ledger-account-category-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the account category
                properties:
                  ia::result:
                    $ref: '#/components/schemas/general-ledger-account-category'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Get an account category:
                  value:
                    ia::result:
                      key: '129'
                      id: Patents
                      accountType: Intangible Assets
                      normalBalance: debit
                      href: /objects/general-ledger/account-category/129
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/account-group-category-member:
    get:
      summary: List account group category members
      description: >-
        Returns a collection with a key, ID, and link for each account group category member.
      tags:
        - Account group category members
      operationId: list-general-ledger-account-group-category-member
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of account group category members
                properties:
                  ia::result:
                    type: array
                    items:
                      $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata-pages'
              examples:
                List account group category members:
                  value:
                    ia::result:
                      - key: '265'
                        id: '265'
                        href: '/objects/general-ledger/account-group-category-member/265'
                      - key: '270'
                        id: '270'
                        href: '/objects/general-ledger/account-group-category-member/270'
                      - key: '271'
                        id: '271'
                        href: '/objects/general-ledger/account-group-category-member/271'
                    ia::meta:
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: 101
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/account-group-category-member/{key}:
    parameters:
      - name: key
        description: System-assigned unique key for the account group category member.
        in: path
        required: true
        schema:
          type: string
        example: '178'
    get:
      summary: Get an account group category member
      description: >-
        Returns detailed information for a specified account group category member.
      tags:
        - Account group category members
      operationId: get-general-ledger-account-group-category-member-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the account group category member
                properties:
                  ia::result:
                    $ref: '#/components/schemas/general-ledger-account-group-category-member'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Get an account group category member:
                  value:
                    ia::result:
                      id: '270'
                      key: '270'
                      glAccountGroup:
                        key: '716'
                        id: Assets
                        href: /objects/general-ledger/account-group/716
                      sortOrder: 0
                      accountCategory:
                        key: '962'
                        id: Fixed Assets
                        href: /objects/general-ledger/account-category/962
                      categoryName: Fixed Assets
                      audit:
                        createdDateTime: '2024-07-04T06:41:30Z'
                        modifiedDateTime: '2024-07-04T06:41:30Z'
                        createdBy: '1'
                        modifiedBy: '1'
                      href: '/objects/general-ledger/account-group-category-member/270'
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/account-group-computation:
    get:
      summary: List computation account groups
      description: >-
        Returns a collection with a key, ID, and link for each computation account group.
      tags:
        - Computation account groups
      operationId: list-general-ledger-account-group-computation
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of computation account groups
                properties:
                  ia::result:
                    type: array
                    items:
                      $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata-pages'
              examples:
                List computation account groups:
                  value:
                    ia::result:
                      - key: '57'
                        id: '57'
                        href: /objects/general-ledger/account-group-computation/57
                      - key: '58'
                        id: '58'
                        href: /objects/general-ledger/account-group-computation/58
                      - key: '59'
                        id: '59'
                        href: /objects/general-ledger/account-group-computation/59
                    ia::meta:
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/account-group-computation/{key}:
    parameters:
      - name: key
        description: System-assigned unique key for the computation account group.
        in: path
        required: true
        example: '201'
        schema:
          type: string
    get:
      summary: Get a computation account group
      description: Returns detailed information for a specified computation account group.
      tags:
        - Computation account groups
      operationId: get-general-ledger-account-group-computation-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the computation account group
                properties:
                  ia::result:
                    $ref: '#/components/schemas/general-ledger-account-group-computation'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Get a computation account group:
                  value:
                    ia::result:
                      id: '59'
                      key: '59'
                      glAccountGroup:
                        id: AG-Computation-01
                        key: '713'
                        href: /objects/general-ledger/account-group/713
                      formulaLeft:
                        glAccount:
                          key: '3'
                          id: '1000'
                          title: Cash in Bank 1
                          href: /objects/general-ledger/account/3
                        glAccountGroup:
                          key: null
                          id: null
                        constant: '100'
                        asOf: forPeriod
                      operator: add
                      formulaRight:
                        glAccount:
                          key: null
                          id: null
                          title: null
                        glAccountGroup:
                          key: '709'
                          id: AG-Accounts-01
                          href: /objects/general-ledger/account-group/709
                        constant: '100'
                        asOf: forPeriod
                      numberOfDecimalPlaces: 2
                      displayAs: number
                      unit: AK
                      unitPlacement: left
                      audit:
                        createdDateTime: '2024-07-04T06:36:03Z'
                        modifiedDateTime: '2024-07-04T06:36:03Z'
                        createdBy: '1'
                        modifiedBy: '1'
                      href: /objects/general-ledger/account-group-computation/59
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/account-group-member:
    get:
      summary: List account group members
      description: >-
        Returns a collection with a key, ID, and link for each account group member.
      tags:
        - Account group members
      operationId: list-general-ledger-account-group-member
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of the account group members.
                properties:
                  ia::result:
                    type: array
                    items:
                      $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata-pages'
              examples:
                List account group members:
                  value:
                    ia::result:
                      - key: '281'
                        id: '281'
                        href: /objects/general-ledger/account-group-member/281
                      - key: '284'
                        id: '284'
                        href: /objects/general-ledger/account-group-member/284
                      - key: '286'
                        id: '286'
                        href: /objects/general-ledger/account-group-member/286
                    ia::meta:
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/account-group-member/{key}:
    parameters:
      - name: key
        description: System-assigned unique key for the account group member.
        in: path
        example: '201'
        required: true
        schema:
          type: string
    get:
      summary: Get an account group member
      description: Returns detailed information for a specified account group member.
      tags:
        - Account group members
      operationId: get-general-ledger-account-group-member-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the account group member.
                properties:
                  ia::result:
                    $ref: '#/components/schemas/general-ledger-account-group-member'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Get an account group member:
                  value:
                    ia::result:
                      id: '287'
                      key: '287'
                      glAccountGroup:
                        key: '710'
                        id: AG-GroupOfAccountGroups-01
                        href: /objects/general-ledger/account-group/710
                      accountGroup:
                        key: '528'
                        id: 1000 cr
                        href: /objects/general-ledger/account-group/528
                      sortOrder: 0
                      audit:
                        createdDateTime: '2024-07-04T06:29:09Z'
                        modifiedDateTime: '2024-07-04T06:29:09Z'
                        createdBy: '1'
                        modifiedBy: '1'
                      href: /objects/general-ledger/account-group-member/287
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/account-group-purpose:
    get:
      summary: List account group purposes
      description: >-
        Returns a collection with a key, ID, and link for each purpose. This operation is mostly for use in testing; use the query service to find account group purposes that meet specific criteria and to specify the properties that you want in the response.
      tags:
        - Account group purposes
      operationId: list-general-ledger-account-group-purpose
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of account-group-purposes
                properties:
                  ia::result:
                    type: array
                    items:
                      $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata-pages'
              examples:
                List account group purposes:
                  value:
                    ia::result:
                      - key: '1'
                        id: Budgeted Expenses
                        href: /objects/general-ledger/account-group-purposes/19
                      - key: '2'
                        id: Capital Expenses
                        href: /objects/general-ledger/account-group-purposes/21
                    ia::meta:
                      totalCount: 2
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    post:
      summary: Create an account group purpose
      description: Creates a new account group purpose.
      tags:
        - Account group purposes
      operationId: create-general-ledger-account-group-purpose
      requestBody:
        description: Account group purpose to create
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/general-ledger-account-group-purpose'
                - $ref: '#/components/schemas/general-ledger-account-group-purposeRequiredProperties'
            examples:
              Create an account group purpose:
                value:
                  id: Budget1
                  status: active
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: Reference to new account group purpose
                properties:
                  ia::result:
                    $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Reference to new account group purpose:
                  value:
                    ia::result:
                      key: '35'
                      id: Budget1
                      href: /objects/general-ledger/account-group-purpose/35
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/account-group-purpose/{key}:
    parameters:
      - name: key
        description: System-assigned key for the account group purpose.
        in: path
        required: true
        schema:
          type: string
        example: '3906'
    get:
      summary: Get an account group purpose
      description: Returns detailed information for a specified account group purpose.
      tags:
        - Account group purposes
      operationId: get-general-ledger-account-group-purpose-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the account group purpose
                properties:
                  ia::result:
                    $ref: '#/components/schemas/general-ledger-account-group-purpose'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Get an account group purpose:
                  value:
                    ia::result:
                      id: P&L - New
                      status: active
                      key: '32'
                      audit:
                        createdDateTime: '2021-02-22T16:30:48Z'
                        modifiedDateTime: '2021-02-22T16:30:48Z'
                        createdBy: '1'
                        modifiedBy: '1'
                      href: /objects/general-ledger/account-group-purpose/32
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    patch:
      summary: Update an account group purpose
      description: >-
        Updates an existing account group purpose by setting field values. Any fields not provided remain unchanged.
      tags:
        - Account group purposes
      operationId: update-general-ledger-account-group-purpose-key
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/general-ledger-account-group-purpose'
            examples:
              Update an account group purpose:
                value:
                  id: Budgeted Expenses
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Reference to updated account group purpose
                properties:
                  ia::result:
                    $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Reference to updated account group purpose:
                  value:
                    ia::result:
                      key: '35'
                      id: Budgeted Expenses
                      href: /objects/general-ledger/account-group-purpose/35
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    delete:
      summary: Delete an account group purpose
      description: Deletes an account group purpose.
      tags:
        - Account group purposes
      operationId: delete-general-ledger-account-group-purpose-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/account-group:
    get:
      summary: List account groups
      description: >-
        Returns a collection with a key, ID, and link for each account group. This operation is mostly for use in testing; use the query service to find account groups that meet specific criteria and to specify the properties that you want in the response.
      tags:
        - Account groups
      operationId: list-general-ledger-account-group
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of account group objects
                properties:
                  ia::result:
                    type: array
                    items:
                      $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata-pages'
              examples:
                List account groups:
                  value:
                    ia::result:
                      - key: '647'
                        id: Liquidity Ratios
                        href: /objects/general-ledger/account-group/647
                      - key: '621'
                        id: Cash and ST Investments
                        href: /objects/general-ledger/account-group/621
                      - key: '629'
                        id: Days in Month
                        href: /objects/general-ledger/account-group/629
                    ia::meta:
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    post:
      summary: Create an account group
      description: Creates a new account group.
      tags:
        - Account groups
      operationId: create-general-ledger-account-group
      requestBody:
        description: Account group to create
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/general-ledger-account-group'
                - $ref: '#/components/schemas/general-ledger-account-groupRequiredProperties'
            examples:
              Create an account group:
                value:
                  id: AGG Group
                  normalBalance: debit
                  calculationMethod: period
                  includeChildAmount: false
                  title: ReportFilter
                  displayTotalLineAs: Total ReportFilter
                  reportFilters:
                    debitOrCredit: both
                    department: noFilter
                    location: noFilter
                    vendor: noFilter
                    customer: noFilter
                    project: noFilter
                    employee: noFilter
                    item: noFilter
                    class: noFilter
                    task: noFilter
                    warehouse: noFilter
                    asset: noFilter
                    affiliateEntity: noFilter
                  groupType: accounts
                  isKPI: false
                  manager: AG
                  accountGroupPurpose:
                    id: '21'
                  accountRanges:
                    - sortOrder: 0
                      rangeFrom: '1000'
                      rangeTo: '2000'
                    - sortOrder: 1
                      rangeFrom: '3000'
                      rangeTo: '3000'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New account group
                properties:
                  ia::result:
                    $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Reference to new account group:
                  value:
                    ia::result:
                      key: '719'
                      id: AGG Group
                      href: /objects/general-ledger/account-group/719
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/account-group/{key}:
    parameters:
      - name: key
        description: System-assigned key for the account group.
        in: path
        required: true
        schema:
          type: string
        example: '99'
    get:
      summary: Get an account group
      description: Returns detailed information for a specified account group.
      tags:
        - Account groups
      operationId: get-general-ledger-account-group-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the account group
                properties:
                  ia::result:
                    $ref: '#/components/schemas/general-ledger-account-group'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Get an account group:
                  value:
                    ia::result:
                      id: AG-Computation-01
                      normalBalance: debit
                      calculationMethod: period
                      includeChildAmount: false
                      title: AG-Computation-01
                      displayTotalLineAs: Total AG-Computation-01
                      dimensions:
                        location:
                          key: null
                          name: null
                          id: null
                        department:
                          key: '9'
                          name: Partner Sales
                          id: PRS
                          href: /objects/company-config/department/9
                        departmentGroup:
                          key: null
                          name: null
                          id: null
                        locationGroup:
                          key: '7'
                          name: USA Locations
                          id: USA-GRP
                        project:
                          key: null
                          id: null
                          name: null
                        customer:
                          key: null
                          id: null
                          name: null
                        vendor:
                          key: null
                          id: null
                          name: null
                        employee:
                          key: null
                          id: null
                          name: null
                        item:
                          key: null
                          id: null
                          name: null
                        class:
                          key: null
                          id: null
                          name: null
                        task:
                          key: null
                          id: null
                          name: null
                        warehouse:
                          key: null
                          id: null
                          name: null
                        asset:
                          key: null
                          id: null
                          name: null
                        affiliateEntity:
                          key: null
                          id: null
                          name: null
                        entity:
                          key: null
                          id: null
                          name: null
                      reportFilters:
                        debitOrCredit: both
                        department: specificHierarchy
                        location: specific
                        vendor: noFilter
                        customer: noFilter
                        project: noFilter
                        employee: noFilter
                        item: noFilter
                        class: noFilter
                        contract: null
                        task: noFilter
                        warehouse: noFilter
                        costType: null
                        asset: noFilter
                        affiliateEntity: noFilter
                      groupType: computation
                      isKPI: false
                      audit:
                        createdDateTime: '2024-10-08T13:29:18Z'
                        modifiedDateTime: '2024-10-08T13:29:18Z'
                        createdBy: '34'
                        modifiedBy: '1'
                        createdByUser:
                          key: '34'
                          id: Admin
                          href: /objects/company-config/user/34
                        modifiedByUser:
                          key: '1'
                          id: Aman
                          href: /objects/company-config/user/1
                      manager: John Doe
                      accountGroupPurpose:
                        key: '10'
                        id: P&L
                        href: /objects/general-ledger/account-group-purpose/10
                      accountRanges: []
                      accountGroupMembers: []
                      accountGroupComputation:
                        - id: '59'
                          key: '59'
                          glAccountGroup:
                            key: '713'
                            id: AG-Computation-01
                            href: /objects/general-ledger/account-group/713
                          formulaLeft:
                            glAccount:
                              key: null
                              id: null
                              name: null
                            glAccountGroup:
                              key: '1902'
                              id: Acc grp with Dept filter
                              href: /objects/general-ledger/account-group/1902
                            constant: '10'
                            asOf: forPeriod
                          operator: add
                          formulaRight:
                            glAccount:
                              key: null
                              id: null
                              name: null
                            glAccountGroup:
                              key: '1044'
                              id: 990IX Advertising and Promotion
                              href: /objects/general-ledger/account-group/1044
                            constant: '10'
                            asOf: forPeriod
                          numberOfDecimalPlaces: 2
                          displayAs: number
                          unit: AK
                          unitPlacement: left
                          audit:
                            createdDateTime: '2024-06-17T05:00:51Z'
                            modifiedDateTime: '2024-06-17T05:00:51Z'
                            createdBy: '1'
                            modifiedBy: '1'
                          href: /objects/general-ledger/account-group-computation/59
                      accountGroupCategoryMembers: []
                      statisticalAccountGroupCategoryMembers: []
                      statisticalAccountRanges: []
                      href: /objects/general-ledger/account-group/713
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    patch:
      summary: Update an account group
      description: >-
        Updates an existing account group by setting field values. Any fields not provided remain unchanged.
      tags:
        - Account groups
      operationId: update-general-ledger-account-group-key
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/general-ledger-account-group'
                - type: object
                  properties:
                    id:
                      readOnly: true
            examples:
              Update an account group:
                value:
                  accountRanges:
                    - sortOrder: 0
                      rangeFrom: '1001'
                      rangeTo: '2000'
                    - sortOrder: 1
                      rangeFrom: '2001'
                      rangeTo: '5000'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated account group
                properties:
                  ia::result:
                    $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Reference to updated account group:
                  value:
                    ia::result:
                      key: '719'
                      id: AGG Group
                      href: /objects/general-ledger/account-group/719
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    delete:
      summary: Delete an account group
      description: Deletes an account group.
      tags:
        - Account groups
      operationId: delete-general-ledger-account-group-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/account-range:
    get:
      summary: List account ranges
      description: Returns a collection with a key, ID, and link for each account range.
      tags:
        - Account ranges
      operationId: list-general-ledger-account-range
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of account ranges
                properties:
                  ia::result:
                    type: array
                    items:
                      $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata-pages'
              examples:
                List account ranges:
                  value:
                    ia::result:
                      - key: '57'
                        id: '57'
                        href: /objects/general-ledger/account-range/130
                      - key: '58'
                        id: '58'
                        href: /objects/general-ledger/account-range/132
                      - key: '59'
                        id: '59'
                        href: /objects/general-ledger/account-range/133
                    ia::meta:
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/account-range/{key}:
    parameters:
      - name: key
        description: System-assigned key for the account range.
        in: path
        required: true
        schema:
          type: string
        example: '178'
    get:
      summary: Get an account range
      description: Returns detailed information for a specified account range.
      tags:
        - Account ranges
      operationId: get-general-ledger-account-range-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the account range
                properties:
                  ia::result:
                    $ref: '#/components/schemas/general-ledger-account-range'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Get an account range:
                  value:
                    ia::result:
                      id: '130'
                      key: '130'
                      glAccountGroup:
                        id: Accounts Payable group
                        key: '33'
                        href: /objects/general-ledger/account-group/33
                      sortOrder: 0
                      rangeFrom: '1000'
                      rangeTo: '1501'
                      audit:
                        createdDateTime: '2022-03-11T09:21:52Z'
                        modifiedDateTime: '2022-03-11T09:21:52Z'
                        createdBy: '1'
                        modifiedBy: '1'
                      href: /objects/general-ledger/account-range/130
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/account:
    get:
      summary: List accounts
      description: >-
        Returns a collection with a key, ID, and link for each account. This operation is mostly for use in testing; use the query service to find accounts that meet certain criteria and to specify the properties that you want in the response.
      tags:
        - Accounts
      operationId: list-general-ledger-account
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of account objects
                properties:
                  ia::result:
                    type: array
                    items:
                      $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata-pages'
              examples:
                List accounts:
                  value:
                    ia::result:
                      - key: '406'
                        id: '9999'
                        href: /objects/general-ledger/account/406
                      - key: '407'
                        id: '1501.06'
                        href: /objects/general-ledger/account/407
                      - key: '16'
                        id: '1007'
                        href: /objects/general-ledger/account/16
                    ia::meta:
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    post:
      summary: Create an account
      description: Creates a new General Ledger account.
      tags:
        - Accounts
      operationId: create-general-ledger-account
      requestBody:
        description: Account to create
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/general-ledger-account'
                - $ref: '#/components/schemas/general-ledger-accountRequiredProperties'
            examples:
              Create an account:
                value:
                  id: '1501'
                  name: Vehicle Spare parts - Transmission
                  accountType: balanceSheet
                  closingType: nonClosingAccount
                  normalBalance: debit
                  alternativeGLAccount: none
                  status: active
                  requireDimensions:
                    department: false
                    location: false
                  isTaxable: false
                  disallowDirectPosting: true
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New account
                properties:
                  ia::result:
                    $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Reference to new account:
                  value:
                    ia::result:
                      key: '356'
                      id: '1501'
                      href: /objects/general-ledger/account/356
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/account/{key}:
    parameters:
      - name: key
        description: System-assigned key for the account.
        in: path
        schema:
          type: string
        required: true
        example: '411'
    get:
      summary: Get an account
      description: Returns detailed information for a specified account.
      operationId: get-general-ledger-account-key
      tags:
        - Accounts
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the account
                properties:
                  ia::result:
                    $ref: '#/components/schemas/general-ledger-account'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Get an account:
                  value:
                    ia::result:
                      key: '356'
                      id: '1501'
                      name: Vehicle Spare parts - Transmission
                      accountType: balanceSheet
                      normalBalance: debit
                      closingType: nonClosingAccount
                      closeToGLAccount:
                        id: null
                        key: null
                      status: active
                      requireDimensions:
                        department: false
                        location: false
                        project: false
                        customer: false
                        vendor: false
                        employee: false
                        item: false
                        class: false
                        contract: false
                        warehouse: false
                      isTaxable: false
                      category: null
                      taxCode: null
                      mrcCode: null
                      alternativeGLAccount: none
                      audit:
                        createdDateTime: '2022-10-15T00:05:46Z'
                        modifiedDateTime: '2022-10-15T00:05:46Z'
                        createdBy: '68'
                        modifiedBy: '68'
                      disallowDirectPosting: true
                      href: /objects/general-ledger/account/356
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    patch:
      summary: Update an account
      description: >-
        Updates an existing account by setting field values. Any fields not provided remain unchanged.


        An account `id` (account number) can only be changed if the General Ledger is configured to allow changes to account numbers and by users who have permission to change financial account numbers. Changing account numbers affects important aspects of Sage Intacct such as reports, data imports, historical information, and automation.
      tags:
        - Accounts
      operationId: update-general-ledger-account-key
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/general-ledger-account'
            examples:
              Update account to enable direct posting:
                value:
                  disallowDirectPosting: false
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Account updated
                properties:
                  ia::result:
                    $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Reference to updated account:
                  value:
                    ia::result:
                      key: '356'
                      id: '1501'
                      href: /objects/general-ledger/account/356
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    delete:
      summary: Delete an account
      description: >-
        Deletes an account. You cannot delete an account if the account is used in a book transaction. Deleted accounts cannot be recovered.
      tags:
        - Accounts
      operationId: delete-general-ledger-account-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/budget-detail:
    get:
      summary: List budget details
      description: >-
        Returns a collection with a key, ID, and link for each budget detail object. This operation is mostly for use in testing; use the query service to find budget details that meet certain criteria and to specify the properties that you want in the response.
      tags:
        - Budget details
      operationId: list-general-ledger-budget-detail
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List budget detail objects
                properties:
                  ia::result:
                    type: array
                    items:
                      $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata-pages'
              examples:
                List budget details:
                  value:
                    ia::result:
                      - key: '284'
                        id: '284'
                        href: /objects/general-ledger/budget-detail/284
                      - key: '285'
                        id: '285'
                        href: /objects/general-ledger/budget-detail/285
                      - key: '286'
                        id: '286'
                        href: /objects/general-ledger/budget-detail/286
                    ia::meta:
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/budget-detail/{key}:
    parameters:
      - name: key
        description: System-assigned key for the budget detail.
        in: path
        schema:
          type: string
        required: true
        example: '153'
    get:
      summary: Get budget details
      description: Returns detailed information for a specified budget detail.
      tags:
        - Budget details
      operationId: get-general-ledger-budget-detail-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of a budget detail
                properties:
                  ia::result:
                    $ref: '#/components/schemas/general-ledger-budget-detail'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                A budget detail:
                  value:
                    ia::result:
                      id: '13'
                      key: '13'
                      budget:
                        key: '2'
                        id: Proj-budget
                        href: /objects/general-ledger/budget/2
                      currency:
                        txnCurrency: null
                      reportingPeriod:
                        key: '127'
                        id: Month ended January 2023
                        name: Month ended January 2023
                        href: /objects/general-ledger/reporting-period/127
                      glAccount:
                        key: '279'
                        id: '9501'
                        name: Labor Costing - Billable
                        href: /objects/general-ledger/account/279
                      dimensions:
                        department:
                          key: null
                          id: null
                          name: null
                        location:
                          key: null
                          id: null
                          name: null
                        class:
                          key: null
                          id: null
                          name: null
                        item:
                          key: null
                          id: null
                          name: null
                        customer:
                          key: null
                          id: null
                          name: null
                        vendor:
                          key: null
                          id: null
                          name: null
                        project:
                          key: null
                          id: null
                          name: null
                        employee:
                          key: null
                          id: null
                          name: null
                        warehouse:
                          key: null
                          id: null
                          name: null
                        task:
                          key: null
                          id: null
                          name: null
                      amount: '1000.00'
                      budgetGrowth:
                        basedOn: budget
                        growBy: null
                        perPeriod: percentage
                      notes: Notes
                      audit:
                        createdDateTime: '2024-10-08T13:29:18Z'
                        modifiedDateTime: '2024-10-08T13:29:18Z'
                        createdByUser:
                          key: '34'
                          id: Admin
                          href: /objects/company-config/user/34
                        modifiedByUser:
                          key: '1'
                          id: Aman
                          href: /objects/company-config/user/1
                      entity:
                        key: null
                        id: null
                        name: null
                      href: /objects/general-ledger/budget-detail/13
                    ia::meta:
                      totalCount: 1
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    delete:
      summary: Delete budget detail
      description: Deletes a budget detail
      tags:
        - Budget details
      operationId: delete-general-ledger-budget-detail-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/budget:
    get:
      summary: List budgets
      description: >-
        Returns a collection with a key, ID, and link for each budget. This operation is mostly for use in testing; use the query service to find budgets that meet specific criteria and to specify the properties that you want in the response.
      tags:
        - Budgets
      operationId: list-general-ledger-budget
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List budget objects
                properties:
                  ia::result:
                    type: array
                    items:
                      $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata-pages'
              examples:
                List budgets:
                  value:
                    ia::result:
                      - key: '1'
                        id: Std_Budget
                        href: /objects/general-ledger/budget/1
                      - key: '2'
                        id: KPI Budgets
                        href: /objects/general-ledger/budget/2
                    ia::meta:
                      totalCount: 2
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    post:
      summary: Create a budget
      description: >-
        Creates a new budget. The API does not compute amounts for calculated budgets, so an `amount` must be provided when creating a budget-detail.
      tags:
        - Budgets
      operationId: create-general-ledger-budget
      requestBody:
        description: Budget to create
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/general-ledger-budget'
                - $ref: '#/components/schemas/general-ledger-budgetRequiredProperties'
            examples:
              Create a budget:
                value:
                  id: Budget1
                  description: First budget
                  isDefault: true
                  status: active
                  consolidateAmounts: false
                  currency: null
                  postProjectEstimate: false
                  lines:
                    - glAccount:
                        id: '1000'
                      dimensions:
                        location:
                          id: '1'
                      reportingPeriod:
                        id: Month ended January 2022
                      amount: '100'
                      budgetGrowth:
                        basedOn: budget
                        growBy: null
                        perPeriod: percentage
                    - glAccount:
                        id: '1000'
                      dimensions:
                        location:
                          id: '1'
                      reportingPeriod:
                        id: Month ended February 2022
                      amount: '150'
                      budgetGrowth:
                        basedOn: budget
                        growBy: null
                        perPeriod: percentage
                    - glAccount:
                        id: '1000'
                      dimensions:
                        location:
                          id: '1'
                      reportingPeriod:
                        id: Month ended March 2022
                      amount: '100'
                      budgetGrowth:
                        basedOn: budget
                        growBy: null
                        perPeriod: percentage
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New budget
                properties:
                  ia::result:
                    $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Reference to new budget:
                  value:
                    ia::result:
                      key: '41'
                      id: Budget1
                      href: /objects/general-ledger/budget/41
                    ia::meta:
                      totalCount: 1
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/budget/{key}:
    parameters:
      - name: key
        description: System-assigned key for the budget.
        in: path
        schema:
          type: string
        required: true
        example: '291'
    get:
      summary: Get a budget
      description: Returns detailed information for a specified budget.
      tags:
        - Budgets
      operationId: get-general-ledger-budget-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the budget
                properties:
                  ia::result:
                    $ref: '#/components/schemas/general-ledger-budget'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Get a budget:
                  value:
                    ia::result:
                      key: '5'
                      id: Employee Expense Budget
                      description: Employee Expense Budget
                      isDefault: false
                      userName: Admin
                      status: active
                      audit:
                        createdDateTime: '2022-02-16T05:13:36Z'
                        modifiedDateTime: '2022-02-16T05:13:36Z'
                        createdBy: Admin
                        modifiedBy: Admin
                      consolidateAmounts: false
                      currency: USD
                      postProjectEstimate: false
                      postProjectContract: false
                      entity:
                        key: null
                        id: null
                        name: null
                      lines:
                        - id: '291'
                          key: '291'
                          budget:
                            key: '5'
                            id: Employee Expense Budget
                            href: /objects/general-ledger/budget/291
                          currency:
                            txnCurrency: USD
                          reportingPeriod:
                            key: '79'
                            id: Month Ended January 2022
                            name: Month Ended January 2022
                            href: /objects/reportingperiod/79
                          glAccount:
                            key: '9'
                            id: '4000'
                            name: Revenue
                            href: /objects/general-ledger/account/9
                          dimensions:
                            department:
                              key: '9'
                              id: '11'
                              name: Sales
                              href: /objects/company-config/department/9
                            location:
                              key: '1'
                              id: '1'
                              name: United States of America
                              href: /objects/company-config/location/1
                            employee:
                              key: '1'
                              id: '1'
                              name: Joe Smith
                              href: /objects/company-config/employee/10
                            item:
                              key: '101'
                              id: '101'
                              name: Widgets
                              href: /objects/inventory-control/item/101
                            class:
                              key: '2'
                              id: '2'
                              name: Mid Market
                              href: /objects/company-config/class/2
                            warehouse:
                              key: '1'
                              id: '1'
                              name: Main
                              href: /objects/inventory-control/warehouse/1
                            customer:
                              key: '1'
                              id: '1'
                              name: Big Buyer
                              href: /objects/accounts-receivable/customer/1
                            vendor:
                              key: '1'
                              id: '1'
                              name: Widget Wholesales
                              href: /objects/accounts-payable/vendor/1
                          amount: '100'
                          budgetGrowth:
                            basedOn: budget
                            growBy: null
                            perPeriod: percentage
                          notes: Projection for 2021
                          href: /objects/general-ledger/budget-detail/291
                      href: /objects/general-ledger/budget/5
                    ia::meta:
                      totalCount: 1
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    patch:
      summary: Update a budget
      description: "Updates an existing budget and its budget details by setting field values. Any fields not provided remain unchanged. \n\n* To update an existing budget-detail line within a budget, provide the budget-detail `key` value. \n* To add a new budget-detail line to a budget, do not include a `key` value.\n* To delete a budget-detail line, send a `DELETE /objects/general-ledger/budget-detail/{key}` request.\n"
      tags:
        - Budgets
      operationId: update-general-ledger-budget-key
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/general-ledger-budget'
                - type: object
                  properties:
                    id:
                      readOnly: true
            examples:
              Set a budget to inactive:
                value:
                  status: inactive
              Update the amount in a budget detail line:
                value:
                  lines:
                    - key: '4120'
                      amount: '1229'
              Set a budget as default, update a line and add a line:
                value:
                  isDefault: true
                  lines:
                    - key: '135'
                      budgetGrowth:
                        basedOn: budget
                        growBy: 15
                        perPeriod: percentage
                    - glAccount:
                        id: '1000'
                      dimensions:
                        location:
                          id: '1'
                      reportingPeriod:
                        id: Month ended June 2022
                      amount: '100'
                      budgetGrowth:
                        basedOn: budget
                        growBy: 18
                        perPeriod: percentage
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Budget updated
                properties:
                  ia::result:
                    $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Reference to updated budget:
                  value:
                    ia::result:
                      key: '21'
                      id: Std_Budget
                      href: /objects/general-ledger/budget/21
                    ia::meta:
                      totalCount: 1
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    delete:
      summary: Delete budget
      description: >-
        Deletes a budget. You cannot delete the default budget or any budget that is being used by a financial report.


        Carefully consider the implications before you delete a budget. After you delete a budget, you will no longer be able to use its data to create future budgets and it can no longer be used in financial reports.
      tags:
        - Budgets
      operationId: delete-general-ledger-budget-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/journal-entry-line:
    get:
      summary: List journal entry lines
      description: >-
        Returns a collection of journal entry lines with a key, ID, and link for each entry line. This operation is mostly for use in testing; use the query service to find journal entry lines that meet certain criteria and to specify the properties that are returned.
      tags:
        - Journal entry lines
      operationId: list-general-ledger-journal-entry-line
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of journal entry lines
                properties:
                  ia::result:
                    type: array
                    items:
                      $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata-pages'
              examples:
                List journal entry lines:
                  value:
                    ia::result:
                      - key: '18'
                        id: '18'
                        href: /objects/general-ledger/journal-entry-line/18
                      - key: '21'
                        id: '21'
                        href: /objects/general-ledger/journal-entry-line/21
                      - key: '41'
                        id: '41'
                        href: /objects/general-ledger/journal-entry-line/41
                    ia::meta:
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/journal-entry-line/{key}:
    parameters:
      - name: key
        description: System-assigned unique key for the journal entry line.
        in: path
        required: true
        schema:
          type: string
        example: '132'
    get:
      summary: Get a journal entry line
      description: Returns detailed information for a specified journal entry line.
      tags:
        - Journal entry lines
      operationId: get-general-ledger-journal-entry-line-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the journal entry line
                properties:
                  ia::result:
                    $ref: '#/components/schemas/general-ledger-journal-entry-line'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Get a journal entry line:
                  value:
                    ia::result:
                      id: '18'
                      key: '18'
                      journalEntry:
                        id: '132'
                        key: '132'
                        href: /objects/general-ledger/journal-entry/132
                      lineNumber: 18
                      txnType: credit
                      entryDate: '2024-01-14'
                      baseAmount: '49.31'
                      txnAmount: '80.00'
                      glAccount:
                        key: '194'
                        id: '********'
                        name: Sales
                        href: /objects/general-ledger/account/194
                      dimensions:
                        department:
                          key: '15'
                          id: SEE
                          name: Security Engineering
                          href: /objects/company-config/department/15
                        location:
                          key: '26'
                          id: CRD
                          name: Croydon
                          href: /objects/company-config/location/26
                        customer:
                          key: '9'
                          id: JHC
                          name: Jones Hogan Company
                          href: /objects/accounts-receivable/customer/9
                        vendor:
                          key: '13'
                          id: CAL
                          name: CALOIL Cor Corporation
                          href: /objects/accounts-payable/vendor/13
                        employee:
                          key: '29'
                          id: '234'
                          name: John Smith
                          href: /objects/company-config/employee/29
                        item:
                          key: '8'
                          id: MAS
                          name: Mobile Accessories
                          href: /objects/inventory-control/item/8
                        contract:
                          key: null
                          id: null
                          name: null
                        project:
                          key: '15'
                          id: STS
                          name: Staff Support -  IT - Jones Hogan Company
                          href: /objects/projects/project/15
                        class:
                          key: '8'
                          id: CON
                          name: Construction
                          href: /objects/company-config/class/8
                      documentId: CalOil_Credit_01-24
                      description: CalOil Credit entries January 2024
                      numberOfUnits: 2
                      currency:
                        baseCurrency: GBP
                        txnCurrency: USD
                        exchangeRateDate: '2024-01-14'
                        exchangeRateTypeId: Company Daily Rate
                        exchangeRate: 0.6164
                      reconciliationGroup:
                        cleared: matched
                        clearingDate: '2024-01-14'
                        reconciliationDate: '2024-01-14'
                      accountingPeriod: 1
                      allocation:
                        id: '21'
                        key: FA_ALLOC
                        href: /objects/general-ledger/txn-allocation-template/21
                      interEntityTxnType: p
                      parent:
                        id: '40'
                        key: '40'
                        href: /objects/general-ledger/journal-entry-line/40
                      audit:
                        createdDateTime: '2024-01-14T18:13:29Z'
                        modifiedDateTime: '2024-01-14T18:13:29Z'
                        createdByUser:
                          key: '1'
                          href: /objects/company-config/user/1
                        createdBy: '1'
                        modifiedByUser:
                          key: '1'
                          href: /objects/company-config/user/1
                        modifiedBy: '1'
                      state: posted
                      isBillable: false
                      isBilled: false
                      taxEntries: []
                      href: /objects/general-ledger/journal-entry-line/18
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    delete:
      summary: Delete a Journal entry line
      description: Deletes a journal entry line.
      tags:
        - Journal entry lines
      operationId: delete-general-ledger-journal-entry-line-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/journal-entry-tax-entry:
    get:
      summary: List journal entry tax entries
      description: >-
        Returns a collection of journal entry tax entries with a key, ID, and link for each account.
      tags:
        - Journal entry tax entries
      operationId: list-general-ledger-journal-entry-tax-entry
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of general-ledger-journal-entry-tax-entry
                properties:
                  ia::result:
                    type: array
                    items:
                      $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata-pages'
              examples:
                List journal entry tax entries:
                  value:
                    ia::result:
                      - key: '8'
                        id: '8'
                        href: /objects/general-ledger/journal-entry-tax-entry/8
                      - key: '9'
                        id: '9'
                        href: /objects/general-ledger/journal-entry-tax-entry/9
                      - key: '10'
                        id: '10'
                        href: /objects/general-ledger/journal-entry-tax-entry/10
                    ia::meta:
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/journal-entry-tax-entry/{key}:
    parameters:
      - name: key
        description: System-assigned key for the journal entry tax entry.
        in: path
        required: true
        example: '300'
        schema:
          type: string
    get:
      summary: Get a journal entry tax entry
      description: Returns detailed information for a specified journal entry tax entry.
      tags:
        - Journal entry tax entries
      operationId: get-general-ledger-journal-entry-tax-entry-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the journal-entry-tax-entry
                properties:
                  ia::result:
                    $ref: '#/components/schemas/general-ledger-journal-entry-tax-entry'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Get a journal entry tax entry:
                  value:
                    ia::result:
                      key: '300'
                      id: '300'
                      baseTaxAmount: '100'
                      txnTaxAmount: '100'
                      taxRate: 5.5
                      taxDetail:
                        id: UK Export Reduced Rate
                        key: '24'
                        href: /objects/tax/tax-detail/24
                      journalEntryLine:
                        id: '148'
                        key: '148'
                        href: /objects/general-ledger/journal-entry-line/148
                      href: /objects/general-ledger/journal-entry-tax-entry/300
                    ia::meta:
                      totalCount: 1
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/journal-entry:
    get:
      summary: List journal entries
      description: >-
        Returns a collection of journal entries with a key, ID, and link for each entry. This operation is mostly for use in testing; use the query service to find journal entries that meet certain criteria and to specify the properties that you want in the response.
      tags:
        - Journal entries
      operationId: list-general-ledger-journal-entry
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of journal entries
                properties:
                  ia::result:
                    type: array
                    items:
                      $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata-pages'
              examples:
                List journal entries:
                  value:
                    ia::result:
                      - key: '56'
                        id: '56'
                        href: /objects/general-ledger/journal-entry/56
                      - key: '132'
                        id: '132'
                        href: /objects/general-ledger/journal-entry/132
                      - key: '256'
                        id: '256'
                        href: /objects/general-ledger/journal-entry/256
                    ia::meta:
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    post:
      summary: Create a journal entry
      description: >-
        Creates a new journal entry. When creating a journal entry, you can either post it immediately or save it as a draft for review. Draft entries can be updated before posting, but all required information must be provided. To post a journal entry, debits and credits must balance, and all required details must be entered.


        Note: The only valid values when creating a new entry are `posted` (default, system-defined state), and `draft`. Only populate the state field when creating a `draft` entry.
      tags:
        - Journal entries
      operationId: create-general-ledger-journal-entry
      requestBody:
        description: Create a new journal entry
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/general-ledger-journal-entry'
                - $ref: '#/components/schemas/general-ledger-journal-entryRequiredProperties'
            examples:
              Create a journal entry:
                value:
                  glJournal:
                    id: GJ
                  postingDate: '2024-11-01'
                  description: October revenue posting
                  lines:
                    - txnType: credit
                      txnAmount: '200'
                      glAccount:
                        id: '1000'
                      dimensions:
                        department:
                          key: '11'
                        location:
                          key: '1'
                      documentId: October_revenue_credit_transactions
                    - txnType: debit
                      txnAmount: '200'
                      glAccount:
                        id: '1000'
                      dimensions:
                        department:
                          key: '11'
                        location:
                          key: '1'
                      documentId: October_revenue_debit_transactions
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New journal entry
                properties:
                  ia::result:
                    $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Reference to new journal entry:
                  value:
                    ia::result:
                      key: '30565'
                      id: '30565'
                      href: /objects/general-ledger/journal-entry/30565
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/journal-entry/{key}:
    parameters:
      - name: key
        description: System-assigned unique key for the journal entry.
        in: path
        required: true
        schema:
          type: string
        example: '132'
    get:
      summary: Get journal entry
      description: Returns detailed information for a specified journal entry.
      tags:
        - Journal entries
      operationId: get-general-ledger-journal-entry-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the journal entry
                properties:
                  ia::result:
                    $ref: '#/components/schemas/general-ledger-journal-entry'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Get a journal entry:
                  value:
                    ia::result:
                      id: '30565'
                      key: '30565'
                      txnNumber: 493
                      description: October revenue posting
                      glJournal:
                        id: GJ
                        key: '4'
                        isAdjustment: false
                        href: /objects/general-ledger/journal/4
                      postingDate: '2024-11-01'
                      moduleName: 2.GL
                      referenceNumber: Rev_Oct
                      reversedBy:
                        id: '663'
                        key: '663'
                        href: /objects/general-ledger/journal-entry/663
                      reversedFromDate: null
                      accountAllocationRun:
                        id: '87'
                        key: '87'
                        href: /objects/general-ledger/account-allocation-run/87
                      baseLocation:
                        key: '32'
                        id: BC
                        href: /objects/company-config/location/32
                      audit:
                        createdDateTime: '2025-03-17T10:07:00Z'
                        modifiedDateTime: '2025-03-17T10:07:00Z'
                        createdByUser:
                          key: '159'
                          href: /objects/company-config/user/159
                        createdBy: '159'
                        modifiedByUser:
                          key: '159'
                          href: /objects/company-config/user/159
                        modifiedBy: '159'
                      state: posted
                      sequenceNumber: '1'
                      tax:
                        taxImplication: none
                        taxSolution:
                          key: '5'
                          id: GST
                          href: /objects/company-config/attachment/5
                      attachment:
                        key: '6'
                        id: Doc-6331
                        href: /objects/company-config/attachment/6
                      entity:
                        key: null
                        id: null
                        name: null
                      lines:
                        - key: '465744'
                          id: '465744'
                          journalEntry:
                            key: '30565'
                            id: '30565'
                            href: /objects/general-ledger/journal-entry/30565
                          lineNumber: 1
                          txnType: credit
                          entryDate: '2024-11-01'
                          baseAmount: '200.00'
                          txnAmount: '200.00'
                          glAccount:
                            key: '9'
                            id: '********'
                            name: Bank of America A/c.
                            href: /objects/general-ledger/account/9
                          dimensions:
                            department:
                              key: '11'
                              id: FIN-AP
                              name: Finance - Accounts Payable
                              href: /objects/company-config/department/11
                            location:
                              key: '1'
                              id: '1'
                              name: United States of America
                              href: /objects/company-config/location/1
                            customer:
                              key: null
                              id: null
                              name: null
                            vendor:
                              key: null
                              id: null
                              name: null
                            employee:
                              key: null
                              id: null
                              name: null
                            item:
                              key: null
                              id: null
                              name: null
                            contract:
                              key: null
                              id: null
                              name: null
                            project:
                              key: null
                              id: null
                              name: null
                            class:
                              key: null
                              id: null
                              name: null
                          documentId: October_revenue_credit_transactions
                          description: October revenue credit transactions
                          numberOfUnits: 2
                          currency:
                            baseCurrency: USD
                            txnCurrency: USD
                            exchangeRateDate: '2024-11-01'
                            exchangeRateTypeId: Company Daily Rate
                            exchangeRate: 0.78
                          reconciliationGroup:
                            cleared: matched
                            clearingDate: '2024-11-01'
                            reconciliationDate: '2024-11-01'
                          accountingPeriod: 10
                          allocation:
                            id: FA_ALLOC
                            key: '21'
                            href: /objects/general-ledger/txn-allocation-template/21
                          interEntityTxnType: p
                          parent:
                            id: '40'
                            key: '40'
                            href: /objects/general-ledger/journal-entry-line/40
                          audit:
                            createdDateTime: '2025-03-17T10:07:00Z'
                            modifiedDateTime: '2025-03-17T10:07:00Z'
                            createdByUser:
                              key: '159'
                              href: /objects/company-config/user/159
                            createdBy: '159'
                            modifiedByUser:
                              key: '159'
                              href: /objects/company-config/user/159
                            modifiedBy: '159'
                          state: posted
                          isBillable: false
                          isBilled: false
                          taxEntries: []
                          href: /objects/general-ledger/journal-entry-line/465744
                        - key: '465745'
                          id: '465745'
                          journalEntry:
                            key: '30565'
                            id: '30565'
                            href: /objects/general-ledger/journal-entry/30565
                          lineNumber: 2
                          txnType: debit
                          entryDate: '2024-11-01'
                          baseAmount: '200.00'
                          txnAmount: '200.00'
                          glAccount:
                            key: '9'
                            id: '********'
                            name: Bank of America A/c.
                            href: /objects/general-ledger/account/9
                          dimensions:
                            department:
                              key: '11'
                              id: EXPP
                              name: Expense Payable -----> Inactive
                              href: /objects/company-config/department/11
                            location:
                              key: '1'
                              id: '1'
                              name: United States of America
                              href: /objects/company-config/location/1
                            customer:
                              key: null
                              id: null
                              name: null
                            vendor:
                              key: null
                              id: null
                              name: null
                            employee:
                              key: null
                              id: null
                              name: null
                            item:
                              key: null
                              id: null
                              name: null
                            contract:
                              key: null
                              id: null
                              name: null
                            project:
                              key: null
                              id: null
                              name: null
                            class:
                              key: null
                              id: null
                              name: null
                          documentId: October_revenue_debit_transactions
                          description: October revenue debit transactions
                          numberOfUnits: 2
                          currency:
                            baseCurrency: USD
                            txnCurrency: USD
                            exchangeRateDate: '2024-11-01'
                            exchangeRateTypeId: Company Daily Rate
                            exchangeRate: 0.78
                          reconciliationGroup:
                            cleared: matched
                            clearingDate: '2024-11-01'
                            reconciliationDate: '2024-11-01'
                          accountingPeriod: 10
                          allocation:
                            id: FA_ALLOC
                            key: '21'
                            href: /objects/general-ledger/txn-allocation-template/21
                          interEntityTxnType: p
                          parent:
                            id: '40'
                            key: '40'
                            href: /objects/general-ledger/journal-entry-line/40
                          audit:
                            createdDateTime: '2025-03-17T10:07:00Z'
                            modifiedDateTime: '2025-03-17T10:07:00Z'
                            createdByUser:
                              key: '159'
                              href: /objects/company-config/user/159
                            createdBy: '159'
                            modifiedByUser:
                              key: '159'
                              href: /objects/company-config/user/159
                            modifiedBy: '159'
                          state: posted
                          isBillable: false
                          isBilled: false
                          taxEntries: []
                          href: /objects/general-ledger/journal-entry-line/465745
                      href: /objects/general-ledger/journal-entry/30565
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    patch:
      summary: Update a journal entry
      description: >-
        Updates an existing journal entry by setting field values. Any fields not provided remain unchanged.
      tags:
        - Journal entries
      operationId: update-general-ledger-journal-entry-key
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/general-ledger-journal-entry'
                - type: object
                  properties:
                    id:
                      readOnly: true
                    state:
                      readOnly: true
            examples:
              Update a  journal entry:
                value:
                  key: '30565'
                  postingDate: '2024-12-11'
                  lines:
                    - key: '465744'
                      txnType: debit
                      txnAmount: '100'
                      glAccount:
                        id: '********'
                      dimensions:
                        department:
                          id: '11'
                        location:
                          id: '1'
                      documentId: Debit-USD-195
                      currency:
                        txnCurrency: USD
                    - key: '465745'
                      txnType: credit
                      txnAmount: '100'
                      glAccount:
                        id: '********'
                      dimensions:
                        department:
                          id: '11'
                        location:
                          id: '1'
                      documentId: Credit-USD-196
                      currency:
                        txnCurrency: USD
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated journal entry
                properties:
                  ia::result:
                    $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Reference to updated journal entry:
                  value:
                    ia::result:
                      key: '30565'
                      id: '30565'
                      href: /objects/general-ledger/journal-entry/30565
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    delete:
      summary: Delete a journal entry
      description: Deletes a journal entry.
      tags:
        - Journal entries
      operationId: delete-general-ledger-journal-entry-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /workflows/general-ledger/journal-entry/submit:
    post:
      summary: Submit a journal entry
      description: "Submits a `draft` or `declined` journal entry for approval. When a journal entry is submitted, it's sent for approval. Draft journal entries are not sent for approval. \n\nPre-requisites: \n  1. Enable journal entry approval in the General Ledger configuration.\n  2. Grant journal entry approval permissions to users. Only users who already have the Journal Entry Approver permission will be selectable as approvers or substitute approvers.\n  3. Define journal entry approvals for each General Ledger journal that requires approval. Each journal can have either 1 or a chain of approvers.  \n"
      tags:
        - Journal entries
      operationId: submit-general-ledger-journal-entry
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/general-ledger-journal-entry-actions-submit-request'
            examples:
              Submit a journal entry:
                value:
                  key: '1'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  ia::result:
                    $ref: '#/components/schemas/general-ledger-journal-entry-actions-submit-response'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Submit a journal entry:
                  value:
                    ia::result:
                      key: '1'
                      id: '1'
                      state: submitted
                      href: /objects/general-ledger/journal-entry/1
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /workflows/general-ledger/journal-entry/approve:
    post:
      summary: Approve a journal entry
      description: "Approves a `submitted` or `partially approved` journal entry. When approved, the journal entry is posted to the General Ledger. A journal entry that requires approval from multiple approvers remains `partially approved` until it has been approved by all approvers. If General Ledger Outlier Detection is enabled, the entry is evaluated for historical outliers. \n\nPre-requisites: \n1. Enable journal entry approval in the General Ledger configuration.\n2. Grant journal entry approval permissions to users. Only users who already have the Journal Entry Approver permission will be selectable as approvers or substitute approvers.\n3. Define journal entry approvals for each General Ledger journal that requires approval. Each journal can have either 1 or a chain of approvers. \n"
      tags:
        - Journal entries
      operationId: approve-general-ledger-journal-entry
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/general-ledger-journal-entry-actions-approve-request'
            examples:
              Approve a journal entry:
                value:
                  key: '30565'
                  notes: Approved by the Accounting Manager
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  ia::result:
                    $ref: '#/components/schemas/general-ledger-journal-entry-actions-approve-response'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Approve a journal entry:
                  value:
                    ia::result:
                      key: '30565'
                      id: '30565'
                      state: approved
                      href: /objects/general-ledger/journal-entry/30565
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /workflows/general-ledger/journal-entry/decline:
    post:
      summary: Decline a journal entry
      description: "Declines a `submitted` or `partially approved` journal entry. When declined, the entry is returned to the submitter to make any changes and resubmit. The approver can also decline the entry, edit, and then approve it. In this case, the journal entry remains `partially approved`, until it has been approved by all approvers.\n\nPre-requisites: \n1. Enable journal entry approval in the General Ledger configuration.\n2. Grant journal entry approval permissions to users. Only users who already have the Journal Entry Approver permission will be selectable as approvers or substitute approvers.\n3. Define journal entry approvals for each General Ledger journal that requires approval. Each journal can have either 1 or a chain of approvers. \n"
      tags:
        - Journal entries
      operationId: decline-general-ledger-journal-entry
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/general-ledger-journal-entry-actions-decline-request'
            examples:
              Decline a journal entry:
                value:
                  key: '30565'
                  notes: Declined by the Accounting Manager
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  ia::result:
                    $ref: '#/components/schemas/general-ledger-journal-entry-actions-decline-response'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Decline a journal entry:
                  value:
                    ia::result:
                      key: '30565'
                      id: '30565'
                      state: declined
                      href: /objects/general-ledger/journal-entry/30565
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /workflows/general-ledger/journal-entry/recall:
    post:
      summary: Recall a journal entry
      description: >
        Recalls a `submitted` journal entry that is unapproved. You can recall a journal entry as long as the journal entry hasn’t been approved.


        You can only recall an entry if:

        - you are the submitter, are not the approver, and,

        - you have already submitted the entry but the approver hasn’t approved the entry.

      tags:
        - Journal entries
      operationId: recall-general-ledger-journal-entry
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/general-ledger-journal-entry-actions-recall-request'
            examples:
              Recall a journal entry:
                value:
                  key: '30565'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  ia::result:
                    $ref: '#/components/schemas/general-ledger-journal-entry-actions-recall-response'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Recall a journal entry:
                  value:
                    ia::result:
                      key: '30565'
                      id: '30565'
                      state: draft
                      href: /objects/general-ledger/journal-entry/30565
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /workflows/general-ledger/journal-entry/reverse:
    post:
      summary: Reverse a journal entry
      description: >
        Reverses a journal entry that has been `posted` to the general ledger. You can reverse any journal entry that hasn't already been reversed, and which is in an open period. You can reverse statistical transactions, adjusting General Ledger transactions, and adjusting statistical transactions in the same way. After you reverse a journal entry, you cannot undo the reversal. However, you can create another journal entry to replace it.

      tags:
        - Journal entries
      operationId: reverse-general-ledger-journal-entry
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/general-ledger-journal-entry-actions-reverse-request'
            examples:
              Reverse a journal entry:
                value:
                  key: '30565'
                  reverseDate: '2024-07-02'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  ia::result:
                    $ref: '#/components/schemas/general-ledger-journal-entry-actions-reverse-response'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Reverse a journal entry:
                  value:
                    ia::result:
                      key: '30565'
                      id: '30565'
                      state: reversed
                      href: /objects/general-ledger/journal-entry/30565
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /workflows/general-ledger/journal-entry/reclassify:
    post:
      summary: Reclassify a journal entry
      description: >
        Reclassifies a journal entry that has been `posted` to the general ledger. In the general ledger, reclassifying enables you to make certain changes to approved and posted journal entries. For example, you might want to add a reference number or change the department, location, or other dimension.


        You can reclassify general ledger journal entries as long as:

          - you have the right permissions.
          - approvals are enabled in the company.
          - the transaction is in an open period.
          - the transaction has been approved and posted.
          - the transaction is within the same entity or at the top level in which it's created.
          - there are no tax implications for the transaction.
      tags:
        - Journal entries
      operationId: reclassify-general-ledger-journal-entry
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/general-ledger-journal-entry-actions-reclassify-request'
            examples:
              Reclassify a journal entry:
                value:
                  key: '30565'
                  postingDate: '2024-12-11'
                  state: posted
                  lines:
                    - key: '465744'
                      txnType: debit
                      txnAmount: '100'
                      glAccount:
                        id: '********'
                      dimensions:
                        department:
                          id: '11'
                        location:
                          id: '1'
                      documentId: Debit-USD-195
                      currency:
                        txnCurrency: USD
                    - key: '465745'
                      txnType: credit
                      txnAmount: '100'
                      glAccount:
                        id: '********'
                      dimensions:
                        department:
                          id: '11'
                        location:
                          id: '1'
                      documentId: Credit-USD-196
                      currency:
                        txnCurrency: USD
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  ia::result:
                    $ref: '#/components/schemas/general-ledger-journal-entry-actions-reclassify-response'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Reclassify a journal entry:
                  value:
                    ia::result:
                      key: '30565'
                      id: '30565'
                      state: posted
                      href: /objects/general-ledger/journal-entry/30565
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/journal:
    get:
      summary: List journals
      description: >-
        Returns up to 100 journals from the collection with a key, ID, and link for each journal. This operation is mostly for use in testing; use the query service to find journals that meet certain criteria and to specify the properties that you want in the response.
      tags:
        - Journals
      operationId: list-general-ledger-journal
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  ia::result:
                    type: array
                    items:
                      $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata-pages'
              examples:
                List of GL Journals:
                  value:
                    ia::result:
                      - key: '1'
                        id: AR
                        href: /objects/general-ledger/journal/1
                      - key: '2'
                        id: AP
                        href: /objects/general-ledger/journal/2
                      - key: '14'
                        id: ADJ
                        href: /objects/general-ledger/journal/14
                    ia::meta:
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/journal/{key}:
    parameters:
      - name: key
        description: System-assigned key for the journal.
        in: path
        schema:
          type: string
        required: true
        example: '411'
    get:
      summary: Get a journal
      description: Returns detailed information for a specified journal.
      tags:
        - Journals
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  ia::result:
                    $ref: '#/components/schemas/general-ledger-journal'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Single gl-journal:
                  value:
                    ia::result:
                      key: '3'
                      id: EJ
                      status: active
                      name: Expense Journal
                      isAdjustment: true
                      bookId: Accrual
                      bookType: accrual
                      disallowDirectPosting: false
                      audit:
                        createdDateTime: '2023-01-20T10:25:30Z'
                        modifiedDateTime: '2023-11-02T08:30:22Z'
                        createdBy: '68'
                        modifiedBy: '72'
                      isBillable: false
                      href: /objects/general-ledger/journal/3
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      operationId: get-general-ledger-journal-key
      security:
        - OAuth2: []
  /objects/general-ledger/reporting-period:
    get:
      summary: List reporting periods
      description: >-
        Returns a collection with a key, ID, and link for each period. This operation is mostly for use in testing; use the query service to find reporting periods that meet specific criteria and to specify the properties that you want in the response.
      tags:
        - Reporting periods
      operationId: list-general-ledger-reporting-period
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of reporting-period objects
                properties:
                  ia::result:
                    type: array
                    items:
                      $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata-pages'
              examples:
                List reporting periods:
                  value:
                    ia::result:
                      - key: '1'
                        id: Current Month
                        href: /objects/general-ledger/reporting-period/1
                      - key: '2'
                        id: Next Month
                        href: /objects/general-ledger/reporting-period/2
                    ia::meta:
                      totalCount: 2
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    post:
      summary: Create a reporting period
      description: Creates a new reporting period.
      tags:
        - Reporting periods
      operationId: create-general-ledger-reporting-period
      requestBody:
        description: Reporting period to create
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/general-ledger-reporting-period'
                - $ref: '#/components/schemas/general-ledger-reporting-periodRequiredProperties'
            examples:
              Create a reporting period:
                value:
                  startDate: '2023-01-01'
                  endDate: '2023-12-31'
                  id: Current Year 2023
                  columnHeader1: Current Year
                  isBudgetable: false
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New reporting period reference
                properties:
                  ia::result:
                    $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Reference to new reporting period:
                  value:
                    ia::result:
                      key: '1023'
                      id: Current Year 2023
                      href: /objects/general-ledger/reporting-period/1023
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/reporting-period/{key}:
    parameters:
      - name: key
        description: System-assigned unique key for the reporting period.
        in: path
        required: true
        schema:
          type: string
        example: '240'
    get:
      summary: Get a reporting period
      description: Returns detailed information for a specified reporting period.
      tags:
        - Reporting periods
      operationId: get-general-ledger-reporting-period-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the reporting period
                properties:
                  ia::result:
                    $ref: '#/components/schemas/general-ledger-reporting-period'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Get a reporting period:
                  value:
                    ia::result:
                      id: Current Year 2023
                      key: '1023'
                      href: /objects/general-ledger/reporting-period/1023
                      startDate: '2023-02-01'
                      endDate: '2023-12-31'
                      isBudgetable: false
                      reportingPeriodType: custom
                      dateType: 99
                      columnHeader1: Current Year
                      columnHeader2: null
                      status: active
                      audit:
                        createdDateTime: '2024-10-08T13:29:18Z'
                        modifiedDateTime: '2024-10-08T13:29:18Z'
                        createdByUser:
                          key: '34'
                          id: Admin
                          href: /objects/company-config/user/34
                        modifiedByUser:
                          key: '1'
                          id: Aman
                          href: /objects/company-config/user/1
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    patch:
      summary: Update a reporting period
      description: >-
        Updates an existing reporting period by setting field values. Any fields not provided remain unchanged.
      tags:
        - Reporting periods
      operationId: update-general-ledger-reporting-period-key
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/general-ledger-reporting-period'
                - type: object
                  properties:
                    id:
                      readOnly: true
            examples:
              Update a reporting period:
                value:
                  startDate: '2023-01-01'
                  endDate: '2023-11-30'
                  columnHeader1: Current Year till nov
                  isBudgetable: false
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated reporting period reference
                properties:
                  ia::result:
                    $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Reference to updated reporting period:
                  value:
                    ia::result:
                      key: '1023'
                      id: Current Year 2023
                      href: /objects/general-ledger/reporting-period/1
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    delete:
      summary: Delete a reporting period
      description: Deletes a reporting period.
      tags:
        - Reporting periods
      operationId: delete-general-ledger-reporting-period-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/statistical-account:
    get:
      summary: List statistical accounts
      description: >-
        Returns a collection with a key, ID, and link for each account. This operation is mostly for use in testing; use the query service to find statistical GL accounts that meet certain criteria and to specify the properties that you want in the response.
      tags:
        - Statistical accounts
      operationId: list-statistical-account
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Statistical accounts list
                properties:
                  ia::result:
                    type: array
                    items:
                      $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata-pages'
              examples:
                List statistical accounts:
                  value:
                    ia::result:
                      - key: '397'
                        id: '9001'
                        href: /objects/general-ledger/statistical-account/397
                      - key: '398'
                        id: '9002'
                        href: /objects/general-ledger/statistical-account/398
                      - key: '399'
                        id: '9003'
                        href: /objects/general-ledger/statistical-account/399
                    ia::meta:
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    post:
      summary: Create a statistical account
      description: >-
        Creates a new statistical account. You should assign a different account range to statistical accounts so that they are not inadvertently grouped with general ledger accounts. Statistical accounts can be consolidated similarly to general ledger accounts, if you have a consolidated structure of companies with subsidiaries.


        Individual statistical accounts are typically rolled up into account groups for use in reports.
      tags:
        - Statistical accounts
      operationId: create-statistical-account
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/general-ledger-statistical-account'
                - $ref: '#/components/schemas/general-ledger-statistical-accountRequiredProperties'
            examples:
              Create a statistical account:
                value:
                  id: '9030'
                  name: Customer Account
                  reportType: forPeriod
                  status: active
                  requireDimensions:
                    department: true
                    location: true
                  isTaxable: false
                  category: Customers
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New statistical account
                properties:
                  ia::result:
                    $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Reference to new statistical account:
                  value:
                    ia::result:
                      key: '439'
                      id: '9030'
                      href: /objects/general-ledger/statistical-account/439
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/statistical-account/{key}:
    parameters:
      - name: key
        description: System-assigned key for the statistical account.
        in: path
        required: true
        schema:
          type: string
        example: '439'
    get:
      summary: Get a statistical account
      description: Returns detailed information for a specified statistical account.
      tags:
        - Statistical accounts
      operationId: get-statistical-account-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the statistical account
                properties:
                  ia::result:
                    $ref: '#/components/schemas/general-ledger-statistical-account'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Details of a statistical account:
                  value:
                    ia::result:
                      key: '397'
                      id: '9001'
                      name: Customer Account
                      reportType: forPeriod
                      status: active
                      audit:
                        modifiedDateTime: '2023-10-21T04:55:32Z'
                        createdDateTime: '2023-09-20T11:29:30Z'
                        createdBy: '1'
                        modifiedBy: '1'
                      requireDimensions:
                        department: false
                        location: false
                        project: false
                        customer: false
                        vendor: false
                        employee: false
                        item: false
                        class: false
                      isTaxable: true
                      category: Customers
                      href: /objects/general-ledger/statistical-account/397
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    patch:
      summary: Update a statistical account
      description: >-
        Updates an existing statistical account by setting field values. Any fields not provided remain unchanged.
      tags:
        - Statistical accounts
      operationId: update-statistical-account-key
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/general-ledger-statistical-account'
            examples:
              Update a statistical account:
                value:
                  name: Customer Accounts
                  isTaxable: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated statistical account
                properties:
                  ia::result:
                    $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Reference to updated statistical account:
                  value:
                    ia::result:
                      key: '397'
                      id: '9001'
                      href: /objects/general-ledger/statistical-account/397
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    delete:
      summary: Delete a statistical account
      description: >-
        Deletes a statistical account. Accounts used in a book transaction cannot be deleted.  Deleted accounts cannot be recovered.
      tags:
        - Statistical accounts
      operationId: delete-statistical-account-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/statistical-journal-entry-line:
    get:
      summary: List statistical journal entry lines
      description: >-
        Returns up to 100 statistical journal entry lines from the collection with a key, ID, and link for each entry. This operation is mostly for use in testing; use the query service to find journal entry lines that meet certain criteria and to specify the properties that are returned.
      tags:
        - Statistical journal entry lines
      operationId: get-objects-statistical-journal-entry-line
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of statistical journal entry line objects
                properties:
                  ia::result:
                    type: array
                    items:
                      $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata-pages'
              examples:
                Get statistical journal entry lines:
                  value:
                    ia::result:
                      - key: '127'
                        id: '127'
                        href: '/objects/general-ledger/statistical-journal-entry-line/127'
                      - key: '128'
                        id: '128'
                        href: '/objects/general-ledger/statistical-journal-entry-line/128'
                      - key: '129'
                        id: '129'
                        href: '/objects/general-ledger/statistical-journal-entry-line/129'
                      - key: '130'
                        id: '130'
                        href: '/objects/general-ledger/statistical-journal-entry-line/130'
                      - key: '131'
                        id: '131'
                        href: '/objects/general-ledger/statistical-journal-entry-line/131'
                    ia::meta:
                      totalCount: 5
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/statistical-journal-entry-line/{key}:
    parameters:
      - name: key
        description: >-
          System-assigned unique key for the statistical journal entry line item.
        in: path
        required: true
        schema:
          type: string
        example: '2647'
    get:
      summary: Get a statistical journal entry line item
      description: >-
        Returns detailed information for a specified statistical journal entry line item.
      tags:
        - Statistical journal entry lines
      operationId: get-objects-statistical-journal-entry-line-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the statistical journal entry line item
                properties:
                  ia::result:
                    $ref: '#/components/schemas/general-ledger-statistical-journal-entry-line'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Get statistical journal entry line item:
                  value:
                    ia::result:
                      id: '127'
                      key: '127'
                      statisticalJournalEntry:
                        id: '66'
                        key: '66'
                        href: /objects/general-ledger/statistical-journal-entry/66
                      lineNumber: 1
                      txnType: increase
                      entryDate: '2024-03-05'
                      statisticalAccount:
                        key: '1'
                        id: '9001'
                        name: Root - Billable Utilized Statistical Account
                        href: /objects/general-ledger/statistical-account/1
                      dimensions:
                        department:
                          key: null
                          id: null
                          name: null
                        location:
                          key: '1'
                          id: USA
                          name: United States of America
                          href: /objects/location/1
                        project:
                          key: null
                          id: null
                          name: null
                        customer:
                          key: null
                          id: null
                          name: null
                        vendor:
                          key: null
                          id: null
                          name: null
                        employee:
                          key: null
                          id: null
                          name: null
                        item:
                          key: null
                          id: null
                          name: null
                        class:
                          key: null
                          id: null
                          name: null
                      documentId: Headcount_Increase_03_24
                      description: Headcount Increase March 2024
                      numberOfUnits: 3
                      reconciliationGroup:
                        cleared: 'false'
                        clearingDate: '2024-03-05'
                        reconciliationDate: '2024-03-05'
                      accountingPeriod: 5
                      allocation:
                        id: '12'
                        key: STAT_ALLOC
                        href: /objects/general-ledger/txn-allocation-template/12
                      audit:
                        createdDateTime: '2024-04-24T13:40:31Z'
                        modifiedDateTime: '2024-04-24T13:40:31Z'
                        createdBy: '1'
                        modifiedBy: '1'
                      state: posted
                      href: '/objects/general-ledger/statistical-journal-entry-line/127'
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/statistical-journal-entry:
    get:
      summary: List statistical journal entries
      description: >-
        Returns up to 100 statistical journal entries from the collection with a key, ID, and link for each entry. This operation is mostly for use in testing; use the query service to find statistical journal entries that meet certain criteria and to specify the properties that you want in the response.
      tags:
        - Statistical journal entries
      operationId: list-general-ledger-statistical-journal-entry
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of statistical journal entries
                properties:
                  ia::result:
                    type: array
                    items:
                      $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata-pages'
              examples:
                list of statistical journal entries:
                  value:
                    ia::result:
                      - key: '66'
                        id: '66'
                        href: /objects/general-ledger/statistical-journal-entry/66
                      - key: '67'
                        id: '67'
                        href: /objects/general-ledger/statistical-journal-entry/67
                      - key: '68'
                        id: '68'
                        href: /objects/general-ledger/statistical-journal-entry/68
                      - key: '70'
                        id: '70'
                        href: /objects/general-ledger/statistical-journal-entry/70
                      - key: '72'
                        id: '72'
                        href: /objects/general-ledger/statistical-journal-entry/72
                    ia::meta:
                      totalCount: 5
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    post:
      summary: Create a statistical journal entry
      description: >-
        Creates a new statistical journal entry, you can either post the entry immediately or save as a draft for review. Draft journal entries can be updated before posting, but all required information must be provided to post.
      tags:
        - Statistical journal entries
      operationId: create-general-ledger-statistical-journal-entry
      requestBody:
        description: Create a new statistical journal entry
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/general-ledger-statistical-journal-entry'
                - $ref: '#/components/schemas/general-ledger-statistical-journal-entryRequiredProperties'
            examples:
              Create a statistical journal entry:
                value:
                  description: TSSJ 5 JE Journal
                  statisticalJournal:
                    id: cs
                  postingDate: '2024-03-05'
                  lines:
                    - txnType: increase
                      txnAmount: '15'
                      dimensions:
                        location:
                          id: '1'
                      statisticalAccount:
                        id: '9001'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New statistical journal entry
                properties:
                  ia::result:
                    $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Sample POST response:
                  value:
                    ia::result:
                      id: '81'
                      key: '81'
                      href: /objects/general-ledger/statistical-journal-entry/81
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/statistical-journal-entry/{key}:
    parameters:
      - name: key
        description: System-assigned key for the statistical journal entry.
        in: path
        required: true
        schema:
          type: string
        example: '132'
    get:
      summary: Get a statistical journal entry
      description: Returns detailed information for a specified statistical journal entry.
      tags:
        - Statistical journal entries
      operationId: get-general-ledger-statistical-journal-entry-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the statistical journal entry
                properties:
                  ia::result:
                    $ref: '#/components/schemas/general-ledger-statistical-journal-entry'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Statistical Journal entry details:
                  value:
                    ia::result:
                      id: '81'
                      key: '81'
                      txnNumber: 6
                      description: TSSJ 5 JE Journal
                      statisticalJournal:
                        id: CS
                        key: '107'
                        href: /objects/general-ledger/statistical-journal/107
                      postingDate: '2024-03-05'
                      moduleName: 2.GL
                      referenceNumber: Stat_GL_101
                      entity:
                        key: null
                        id: null
                        name: null
                      attachment:
                        id: Transaction Notes
                        key: '4'
                        href: /objects/company-config/attachment/4
                      reversedBy:
                        id: null
                        key: null
                      reversedFromDate: null
                      audit:
                        createdDateTime: '2024-04-26T17:28:30Z'
                        modifiedDateTime: '2024-04-26T17:28:30Z'
                        createdBy: '1'
                        modifiedBy: '1'
                      state: posted
                      sequenceNumber: '1221'
                      lines:
                        - id: '140'
                          key: '140'
                          statisticalJournalEntry:
                            id: '81'
                            key: '81'
                            href: '/objects/general-ledger/statistical-journal-entry/81'
                          lineNumber: 1
                          txnType: increase
                          entryDate: '2024-03-05'
                          txnAmount: '15.00'
                          statisticalAccount:
                            key: '1'
                            id: '9001'
                            name: Root - Billable Utilized Statistical Account
                            href: /objects/general-ledger/statistical-gl-account/1
                          dimensions:
                            department:
                              key: null
                              id: null
                              name: null
                            location:
                              key: '1'
                              id: '1'
                              name: United States of America
                              href: /objects/location/1
                            project:
                              key: null
                              id: null
                              name: null
                            customer:
                              key: null
                              id: null
                              name: null
                            vendor:
                              key: null
                              id: null
                              name: null
                            employee:
                              key: null
                              id: null
                              name: null
                            item:
                              key: null
                              id: null
                              name: null
                            class:
                              key: null
                              id: null
                              name: null
                          documentId: null
                          description: null
                          numberOfUnits: null
                          reconciliationGroup:
                            cleared: 'false'
                            clearingDate: null
                            reconciliationDate: null
                          accountingPeriod: null
                          allocation:
                            id: null
                            key: null
                          audit:
                            createdDateTime: '2024-04-26T17:28:30Z'
                            modifiedDateTime: '2024-04-26T17:28:30Z'
                            createdBy: '1'
                            modifiedBy: '1'
                          state: posted
                          href: '/objects/general-ledger/statistical-journal-entry-line/140'
                      href: /objects/general-ledger/statistical-journal-entry/81
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    patch:
      summary: Update a statistical journal entry
      description: >-
        Updates an existing statistical journal entry by setting field values. Any fields not provided remain unchanged.
      tags:
        - Statistical journal entries
      operationId: update-general-ledger-statistical-journal-entry-key
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/general-ledger-statistical-journal-entry'
            examples:
              Update a statistical journal entry:
                value:
                  postingDate: '2023-12-11'
                  state: posted
                  lines:
                    - key: '195'
                      txnType: decrease
                      txnAmount: '100'
                      statisticalAccount:
                        id: '1000'
                      dimensions:
                        department:
                          id: '11'
                        location:
                          id: '1'
                      documentId: Statistical JE reference
                      currency:
                        txnCurrency: USD
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated statistical journal entry
                properties:
                  ia::result:
                    $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Update a single value:
                  value:
                    ia::result:
                      key: '1'
                      id: '1'
                      href: /objects/general-ledger/statistical-journal-entry/1
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    delete:
      summary: Delete a statistical journal entry
      description: Deletes a statistical journal entry.
      tags:
        - Statistical journal entries
      operationId: delete-general-ledger-statistical-journal-entry-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/statistical-journal:
    get:
      summary: List statistical journals
      description: >-
        Returns a collection with a key, ID, and link for each journal. This operation is mostly for use in testing; use the query service to find journals that meet certain criteria and to specify the properties that you want in the response.
      tags:
        - Statistical journals
      operationId: list-general-ledger-statistical-journal
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of statistical journals
                properties:
                  ia::result:
                    type: array
                    items:
                      $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata-pages'
              examples:
                List statistical journals:
                  value:
                    ia::result:
                      - key: '36'
                        id: SMAXIMUM
                        href: /objects/general-ledger/statistical-journal/36
                      - key: '34'
                        id: TSSJ
                        href: /objects/general-ledger/statistical-journal/34
                      - key: '35'
                        id: SINAJ
                        href: /objects/general-ledger/statistical-journal/35
                    ia::meta:
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    post:
      summary: Create a statistical journal
      description: Creates a new statistical journal
      tags:
        - Statistical journals
      operationId: create-general-ledger-statistical-journal
      requestBody:
        description: Statistical journal to create
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/general-ledger-statistical-journal'
                - $ref: '#/components/schemas/general-ledger-statistical-journalRequiredProperties'
            examples:
              Create a statistical journal:
                value:
                  id: TSSJ
                  name: Timesheet Statistical Journal
                  status: active
                  disallowDirectPosting: false
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New statistical journal
                properties:
                  ia::result:
                    $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Reference to new statistical journal:
                  value:
                    ia::result:
                      key: '88'
                      id: TSSJ
                      href: /objects/general-ledger/statistical-journal/88
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/statistical-journal/{key}:
    parameters:
      - name: key
        description: System-assigned key for a statistical journal.
        in: path
        required: true
        schema:
          type: string
        example: '88'
    get:
      summary: Get a statistical Journal
      description: Returns detailed information for a specified statistical journal.
      tags:
        - Statistical journals
      operationId: get-general-ledger-statistical-journal-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Statistical journal details
                properties:
                  ia::result:
                    $ref: '#/components/schemas/general-ledger-statistical-journal'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Get a statistical journal:
                  value:
                    ia::result:
                      id: TSSJ
                      name: Timesheet Statistical Journal
                      status: active
                      disallowDirectPosting: false
                      bookId: Accrual
                      bookType: accrual
                      audit:
                        createdDateTime: '2024-06-27T17:16:13Z'
                        modifiedDateTime: '2024-06-27T17:16:13Z'
                        createdBy: '1'
                        modifiedBy: '1'
                      key: '36'
                      href: /objects/general-ledger/statistical-journal/36
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    patch:
      summary: Update a statistical journal
      description: >-
        Updates an existing statistical journal by setting field values. Any fields not provided remain unchanged.
      tags:
        - Statistical journals
      operationId: update-general-ledger-statistical-journal-key
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/general-ledger-statistical-journal'
                - type: object
                  properties:
                    id:
                      readOnly: true
            examples:
              Update name and status:
                value:
                  name: Timesheet Statistical Journal
                  status: active
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  ia::result:
                    $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Reference to updated statistical journal:
                  value:
                    ia::result:
                      key: '88'
                      id: TSSJ
                      href: /objects/general-ledger/statistical-journal/88
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    delete:
      summary: Delete a statistical journal
      description: >-
        Deletes a statistical journal. You can only delete a journal if there are no transactions in it.
      tags:
        - Statistical journals
      operationId: delete-general-ledger-statistical-journal-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/txn-allocation-template-line:
    get:
      summary: List transaction allocation template lines
      description: >-
        Returns up to 100 transaction allocation template lines from the collection with a key, ID, and link for each one. This operation is mostly for use in testing; use the query service to find allocation template lines that meet specific criteria and to specify the properties that you want in the response.
      tags:
        - Transaction allocation template lines
      operationId: list-general-ledger-txn-allocation-template-line
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List transaction allocation template lines
                properties:
                  ia::result:
                    type: array
                    items:
                      $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata-pages'
              examples:
                List transaction allocation template lines:
                  value:
                    ia::result:
                      - key: '4'
                        id: '4'
                        href: /objects/general-ledger/txn-allocation-template-line/1
                      - key: '8'
                        id: '8'
                        href: /objects/general-ledger/txn-allocation-template-line/1
                      - key: '1'
                        id: '1'
                        href: /objects/general-ledger/txn-allocation-template-line/2
                    ia::meta:
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/txn-allocation-template-line/{key}:
    parameters:
      - name: key
        description: System-assigned key for the transaction allocation template line.
        in: path
        required: true
        schema:
          type: string
        example: '209856'
    get:
      summary: Get a transaction allocation template line
      description: >-
        Returns detailed information for a specified transaction allocation template line.
      tags:
        - Transaction allocation template lines
      operationId: get-general-ledger-txn-allocation-template-line-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the transaction allocation template line
                properties:
                  ia::result:
                    $ref: '#/components/schemas/general-ledger-txn-allocation-template-line'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Get a transaction allocation template line:
                  value:
                    ia::result:
                      key: '8'
                      id: '8'
                      allocation:
                        id: Fixed amount allocation
                        key: '3'
                        href: /objects/general-ledger/txn-allocation-template/3
                      valueType: amount
                      value: '100'
                      dimensions:
                        location:
                          id: '1'
                          name: United States of America
                          key: '1'
                          href: /objects/company-config/location/1
                        department:
                          id: '11'
                          name: Accounting
                          key: '9'
                          href: /objects/company-config/department/9
                        project:
                          key: '13'
                          id: CO
                          name: Contract -  Platinum - Modulus Industries
                          href: /objects/projects/project/13
                        customer:
                          key: '3'
                          id: MI
                          name: Modulus Industries
                          href: /objects/accounts-receivable/customer/3
                        vendor:
                          key: '47'
                          id: '201'
                          name: PG & E
                          href: /objects/accounts-payable/vendor/47
                        employee:
                          key: '2'
                          id: '2'
                          name: Hatcher
                          href: /objects/company-config/employee/2
                        item:
                          key: '29'
                          id: A001
                          name: Desktop-HP
                          href: /objects/inventory-control/item/29
                        class:
                          key: '6'
                          id: '4'
                          name: Professional Services
                          href: /objects/company-config/class/6
                        task:
                          key: '1'
                          id: T-001
                          name: Task-1
                          href: /objects/projects/task/1
                        warehouse:
                          key: '19'
                          id: WareHouse10004
                          name: Warehouse Name 10004 (CN)
                          href: /objects/inventory-control/warehouse/19
                      audit:
                        createdDateTime: '2022-06-23T12:25:09Z'
                        modifiedDateTime: '2022-06-23T12:25:09Z'
                        createdBy: '1'
                        modifiedBy: '1'
                      lineNumber: 1
                      href: /objects/general-ledger/txn-allocation-template-line/8
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/txn-allocation-template:
    get:
      summary: List transaction allocation templates
      description: >-
        Returns up to 100 transaction allocation templates from the collection with a key, ID, and link for each one. This operation is mostly for use in testing; use the query service to find allocation templates that meet specific criteria and to specify the properties that you want in the response.
      tags:
        - Transaction allocation templates
      operationId: list-general-ledger-txn-allocation-template
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List allocation template objects
                properties:
                  ia::result:
                    type: array
                    items:
                      $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata-pages'
              examples:
                List allocation templates:
                  value:
                    ia::result:
                      - key: '1'
                        id: Fixed Amount Allocation
                        href: /objects/general-ledger/txn-allocation-template/1
                      - key: '2'
                        id: Percentage Allocation
                        href: /objects/general-ledger/txn-allocation-template/2
                    ia::meta:
                      totalCount: 2
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    post:
      summary: Create a transaction allocation template
      description: >-
        Creates a new transaction allocation template, optionally including allocation template lines.
      tags:
        - Transaction allocation templates
      operationId: create-general-ledger-txn-allocation-template
      requestBody:
        description: Transaction allocation template to create
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/general-ledger-txn-allocation-template'
                - $ref: '#/components/schemas/general-ledger-txn-allocation-templateRequiredProperties'
            examples:
              Create an allocation template:
                value:
                  id: Fixed amount allocation
                  description: Fixed dollar amount allocation
                  status: active
                  allocateBy: exactAmount
                  lines:
                    - value: '100'
                      dimensions:
                        department:
                          id: '1'
                        location:
                          id: '1'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: Reference to new allocation template
                properties:
                  ia::result:
                    $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Reference to new allocation template:
                  value:
                    ia::result:
                      key: '4'
                      id: Fixed amount allocation
                      href: /objects/general-ledger/txn-allocation-template/4
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
  /objects/general-ledger/txn-allocation-template/{key}:
    parameters:
      - name: key
        description: System-assigned key for the transaction allocation template.
        in: path
        required: true
        schema:
          type: string
        example: '2'
    get:
      summary: Get a transaction allocation template
      description: >-
        Returns detailed information for a specified transaction allocation template.
      tags:
        - Transaction allocation templates
      operationId: get-general-ledger-txn-allocation-template-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the allocation template
                properties:
                  ia::result:
                    $ref: '#/components/schemas/general-ledger-txn-allocation-template'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Allocation template details:
                  value:
                    ia::result:
                      key: '3'
                      id: Fixed amount allocation
                      description: Fixed dollar amount allocation
                      allocateBy: exactAmount
                      documentNumber: REJCAL
                      status: active
                      audit:
                        createdDateTime: '2023-06-23T11:32:17Z'
                        modifiedDateTime: '2023-06-23T12:25:08Z'
                        createdBy: '1'
                        modifiedBy: '1'
                      lines:
                        - key: '8'
                          id: Fixed Amount Allocation
                          txnAllocationTemplate:
                            id: Fixed amount allocation
                            key: '3'
                            href: /objects/general-ledger/txn-allocation-template/3
                          valueType: amount
                          value: '100'
                          dimensions:
                            location:
                              id: '1'
                              name: United States of America
                              key: '1'
                              href: /objects/company-config/location/1
                            department:
                              id: '11'
                              name: Accounting
                              key: '9'
                              href: /objects/company-config/department/9
                            project:
                              key: '13'
                              id: CO
                              name: Contract -  Platinum - Modulus Industries
                              href: /objects/projects/project/13
                            customer:
                              key: '3'
                              id: MI
                              name: Modulus Industries
                              href: /objects/accounts-receivable/customer/3
                            vendor:
                              key: '47'
                              id: '201'
                              name: PG & E
                              href: /objects/accounts-payable/vendor/47
                            employee:
                              key: '2'
                              id: '2'
                              name: Hatcher
                              href: /objects/company-config/employee/2
                            item:
                              key: '29'
                              id: A001
                              name: Desktop-HP
                              href: /objects/inventory-control/item/29
                            class:
                              key: '6'
                              id: '4'
                              name: Professional Services
                              href: /objects/company-config/class/6
                            task:
                              key: '1'
                              id: T-001
                              name: Task-1
                              href: /objects/projects/task/1
                            warehouse:
                              key: '19'
                              id: WareHouse10004
                              name: Warehouse Name 10004 (CN)
                              href: /objects/inventory-control/warehouse/19
                            audit:
                              createdDateTime: '2024-10-08T13:29:18Z'
                              modifiedDateTime: '2024-10-08T13:29:18Z'
                              createdBy: '34'
                              modifiedBy: '1'
                              createdByUser:
                                key: '34'
                                id: Admin
                                href: /objects/company-config/user/34
                              modifiedByUser:
                                key: '1'
                                id: Aman
                                href: /objects/company-config/user/1
                              entity:
                                key: null
                                id: null
                                name: null
                          lineNumber: 1
                          href: '/objects/general-ledger/txn-allocation-template-line/8'
                      href: /objects/general-ledger/txn-allocation-template/3
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    patch:
      summary: Update a transaction allocation template
      description: >-
        Updates an existing allocation template and template lines by setting field values. Any fields not provided remain unchanged.
      tags:
        - Transaction allocation templates
      operationId: update-general-ledger-txn-allocation-template-key
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/general-ledger-txn-allocation-template'
                - type: object
                  properties:
                    id:
                      readOnly: true
            examples:
              Updates an allocation template description and one existing line:
                value:
                  description: All Shop Allocation
                  lines:
                    - key: '3'
                      valueType: amount
                      value: '95'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Reference to updated allocation template
                properties:
                  ia::result:
                    $ref: '#/components/schemas/object-reference'
                  ia::meta:
                    $ref: '#/components/schemas/metadata'
              examples:
                Reference to updated allocation template:
                  value:
                    ia::result:
                      key: '3'
                      id: Fixed amount allocation
                      href: /objects/general-ledger/txn-allocation-template/3
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
    delete:
      summary: Delete a transaction allocation template
      description: Deletes a transaction allocation template.
      tags:
        - Transaction allocation templates
      operationId: delete-general-ledger-txn-allocation-template-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
      security:
        - OAuth2: []
components:
  schemas:
    object-reference:
      type: object
      description: Reference to created or updated object
      properties:
        key:
          type: string
          example: '12345'
        id:
          type: string
          example: ID123
        href:
          type: string
          readOnly: true
          example: /objects/<application>/<name>/12345
    metadata-pages:
      description: Metadata for collection response
      type: object
      properties:
        totalCount:
          type: integer
          description: Total count
          readOnly: true
          example: 3
        start:
          type: integer
          description: Start element number
          readOnly: true
          example: 1
        pageSize:
          type: integer
          description: Page size
          readOnly: true
          example: 100
        next:
          type: integer
          description: Next element number
          readOnly: true
          nullable: true
          example: 101
        previous:
          type: integer
          description: Previous element number
          readOnly: true
          nullable: true
          example: null
    metadata:
      description: Metadata for response
      type: object
      properties:
        totalCount:
          type: integer
          description: Total count
          readOnly: true
          example: 3
        totalSuccess:
          type: integer
          description: Total success
          readOnly: true
          example: 2
        totalError:
          type: integer
          description: Total errors
          readOnly: true
          example: 1
    error-response:
      type: object
      description: Error response
      properties:
        ia::result:
          type: object
          properties:
            ia::error:
              type: object
              properties:
                code:
                  type: string
                  example: invalidRequest
                message:
                  type: string
                  example: Payload contains errors
                supportId:
                  type: string
                  example: sQrM9%7EYdh5oDEWVb80mrn9xuHjoAAAABBQ
                errorId:
                  type: string
                  example: REST-1064
                additionalInfo:
                  type: object
                  properties:
                    messageId:
                      type: string
                      example: IA.PAYLOAD_CONTAINS_ERRORS
                    placeholders:
                      type: object
                      example: {}
                    propertySet:
                      type: object
                      example: {}
                details:
                  type: array
                  items:
                    type: object
                    properties:
                      code:
                        type: string
                        example: invalidRequest
                      message:
                        type: string
                        example: /newDate is not a valid field
                      errorId:
                        type: string
                        example: REST-1043
                      target:
                        type: string
                        example: /newDate
                      additionalInfo:
                        type: object
                        properties:
                          messageId:
                            type: string
                            example: IA.NOT_A_VALID_FIELD
                          placeholders:
                            type: object
                            example: {}
                          propertySet:
                            type: object
                            example: {}
            ia::meta:
              $ref: '#/components/schemas/metadata'
      example:
        ia::result:
          ia::error:
            code: invalidRequest
            message: A POST request requires a payload
            errorId: REST-1028
            additionalInfo:
              messageId: IA.REQUEST_REQUIRES_A_PAYLOAD
              placeholders:
                OPERATION: POST
              propertySet: {}
            supportId: Kxi78%7EZuyXBDEGVHD2UmO1phYXDQAAAAo
        ia::meta:
          totalCount: 1
          totalSuccess: 0
          totalError: 1
    dimension-ref:
      type: object
      properties:
        location:
          type: object
          properties:
            key:
              type: string
              description: Location key
              example: '22'
              nullable: true
            id:
              type: string
              description: Location ID
              example: LOC-22
              nullable: true
            name:
              type: string
              description: Location name
              readOnly: true
              example: California
              nullable: true
            href:
              type: string
              readOnly: true
              example: /objects/company-config/location/22
        department:
          type: object
          properties:
            key:
              type: string
              description: Department key
              example: '11'
              nullable: true
            id:
              type: string
              description: Department ID
              example: DEP-11
              nullable: true
            name:
              type: string
              description: Department name
              readOnly: true
              example: Sales and Marketing
              nullable: true
            href:
              type: string
              readOnly: true
              example: /objects/company-config/department/11
        employee:
          type: object
          properties:
            key:
              type: string
              description: Employee key
              example: '10'
              nullable: true
            id:
              type: string
              description: Employee ID
              example: EMP-10
              nullable: true
            name:
              type: string
              description: Employee name
              readOnly: true
              example: Thomas, Glenn
              nullable: true
            href:
              type: string
              example: /objects/company-config/employee/10
              readOnly: true
        project:
          type: object
          properties:
            key:
              type: string
              description: Project key
              example: '2'
              nullable: true
            id:
              type: string
              description: Project ID
              example: NET-XML30-2
              nullable: true
            name:
              type: string
              description: Project name
              readOnly: true
              example: Talcomp training
              nullable: true
            href:
              type: string
              readOnly: true
              example: /objects/projects/project/2
        customer:
          type: object
          properties:
            key:
              type: string
              description: Customer key
              example: '13'
              nullable: true
            id:
              type: string
              description: Customer ID
              example: CUST-13
              nullable: true
            name:
              type: string
              description: Customer name
              readOnly: true
              example: Jack In the Box
              nullable: true
            href:
              type: string
              readOnly: true
              example: /objects/accounts-receivable/customer/13
        vendor:
          type: object
          properties:
            key:
              type: string
              description: Vendor key
              example: '357'
              nullable: true
            id:
              type: string
              description: Vendor ID
              example: '*************'
              nullable: true
            name:
              type: string
              description: Vendor name
              readOnly: true
              example: GenLab
              nullable: true
            href:
              type: string
              readOnly: true
              example: /objects/accounts-payable/vendor/357
        item:
          type: object
          properties:
            key:
              type: string
              description: Item key
              example: '13'
              nullable: true
            id:
              type: string
              description: Item ID
              example: Case 13
              nullable: true
            name:
              type: string
              description: Item name
              readOnly: true
              example: Platform pack
              nullable: true
            href:
              type: string
              readOnly: true
              example: /objects/inventory-control/item/13
        warehouse:
          type: object
          properties:
            key:
              type: string
              description: Warehouse key
              example: '6'
              nullable: true
            id:
              type: string
              description: Warehouse ID
              example: WH01
              nullable: true
            name:
              type: string
              description: Warehouse name
              readOnly: true
              example: WH01
              nullable: true
            href:
              type: string
              readOnly: true
              example: /objects/inventory-control/warehouse/6
        class:
          type: object
          properties:
            key:
              type: string
              description: Class key
              example: '731'
              nullable: true
            id:
              type: string
              description: Class ID
              example: REST_CLS_001
              nullable: true
            name:
              type: string
              description: Class name
              readOnly: true
              example: Enterprises
              nullable: true
            href:
              type: string
              readOnly: true
              example: /objects/company-config/class/731
        task:
          type: object
          properties:
            id:
              type: string
              description: Task ID
              example: '1'
              nullable: true
            key:
              type: string
              description: Task key
              example: '1'
              nullable: true
            name:
              type: string
              description: Task name
              readOnly: true
              example: Project Task
              nullable: true
            href:
              type: string
              readOnly: true
              example: /objects/projects/task/1
        costType:
          type: object
          properties:
            id:
              type: string
              description: Cost Type ID
              example: '2'
              nullable: true
            key:
              type: string
              description: Cost Type key
              example: '2'
              nullable: true
            name:
              type: string
              description: Cost Type name
              readOnly: true
              example: Project Expense
              nullable: true
            href:
              type: string
              readOnly: true
              example: /objects/construction/cost-type/2
        asset:
          type: object
          properties:
            id:
              type: string
              description: Asset ID
              example: A001
              nullable: true
            key:
              type: string
              description: Asset key
              example: '1'
              nullable: true
            name:
              type: string
              description: Asset name
              readOnly: true
              example: Laptop 1
              nullable: true
            href:
              type: string
              readOnly: true
              example: /objects/fixed-assets/asset/1
        contract:
          type: object
          properties:
            id:
              type: string
              description: Contract ID
              example: CON-0045-1
              nullable: true
            key:
              type: string
              description: Contract key
              example: '12'
              nullable: true
            name:
              type: string
              description: Contract name
              readOnly: true
              example: ACME Widgets - Service
              nullable: true
            href:
              type: string
              readOnly: true
              example: /objects/contracts/contract/12
        affiliateEntity:
          type: object
          properties:
            key:
              type: string
              description: Affiliate entity key
              example: '23'
              nullable: true
            id:
              type: string
              description: Affiliate entity ID
              example: AFF-23
              nullable: true
            href:
              type: string
              readOnly: true
              example: /objects/affiliate-entity/23
            name:
              type: string
              readOnly: true
              description: Affiliate entity name
              example: 100-USA
              nullable: true
    audit:
      type: object
      readOnly: true
      properties:
        createdDateTime:
          description: Date-time when this record was created.
          type: string
          format: date-time
          readOnly: true
          example: '2023-05-16T15:34:35Z'
        modifiedDateTime:
          description: Date-time when this record was modified.
          type: string
          format: date-time
          readOnly: true
          example: '2024-09-14T21:23:42Z'
        createdBy:
          description: Key for the user who created this object.
          type: string
          readOnly: true
          nullable: true
          deprecated: true
          example: '436'
        modifiedBy:
          description: Key for the user who last modified this object.
          type: string
          readOnly: true
          nullable: true
          deprecated: true
          example: '3086'
        createdByUser:
          type: object
          description: The user who created this object.
          readOnly: true
          properties:
            key:
              type: string
              description: User key.
              readOnly: true
              nullable: true
              example: '436'
            id:
              type: string
              description: User login ID.
              readOnly: true
              nullable: true
              example: JohnDoe
            href:
              type: string
              readOnly: true
              description: URL endpoint for the user.
              example: /objects/company-config/user/436
        modifiedByUser:
          type: object
          description: The user who last modified this object.
          readOnly: true
          properties:
            key:
              type: string
              description: User key.
              readOnly: true
              nullable: true
              example: '3086'
            id:
              type: string
              description: User login ID.
              readOnly: true
              nullable: true
              example: JaneDoe
            href:
              type: string
              readOnly: true
              description: URL endpoint for the user.
              example: /objects/company-config/user/3086
    general-ledger-account-allocation-basis:
      type: object
      description: >-
        Parameters of the account allocation basis used as the blueprint for calculating dynamic allocations.
      properties:
        key:
          type: string
          description: System-assigned unique key for the account allocation basis.
          readOnly: true
          example: '14'
        id:
          type: string
          description: Unique identifier for the account allocation basis.
          readOnly: true
          example: '14'
        glAccountAllocation:
          type: object
          readOnly: true
          description: Account allocation.
          properties:
            key:
              type: string
              description: Unique key for the account allocation.
              readOnly: true
              example: '21'
            id:
              type: string
              description: Unique identifier for the account allocation.
              readOnly: true
              example: '21'
            href:
              type: string
              description: URL endpoint for the account allocation.
              readOnly: true
              example: /objects/general-ledger/account-allocation/21
        accumulation:
          type: string
          description: >-
            Determines how the amounts within the basis accounts are interpreted to derive amounts for the allocation split.
          example: activity
          enum:
            - activity
            - endingBalance
          default: activity
        timePeriod:
          type: object
          description: Time period used to get the basis information.
          properties:
            key:
              type: string
              description: Unique key for the time period.
              example: '395'
            id:
              type: string
              description: Unique identifier for the time period.
              example: Current Month
            href:
              type: string
              description: URL endpoint for the time period.
              readOnly: true
              example: /objects/general-ledger/reporting-period/395
        reportingBook:
          type: string
          description: Accounting method used in the basis calculation.
          example: accrual
          enum:
            - accrual
            - cash
          default: accrual
        allocationMethod:
          type: string
          description: >-
            Method used to to distribute the source pool in the basis calculation.
          example: dynamicRelativeAccountFinancial
          enum:
            - dynamicRelativeAccountFinancial
            - dynamicRelativeAccountStatistical
          default: dynamicRelativeAccountFinancial
        skipNegative:
          type: boolean
          description: Excludes negative balances from the basis calculation.
          example: false
          default: false
        useAmountsFrom:
          type: string
          description: Uses amounts from specified reporting book in the basis calculation.
          example: mainReportingBookAndAlternateBooks
          enum:
            - mainReportingBookAndAlternateBooks
            - alternateBooksOnly
          default: mainReportingBookAndAlternateBooks
        glAccountGroup:
          type: object
          description: Account group to base your allocation split on.
          properties:
            key:
              type: string
              description: Unique key for the account group.
              example: '623'
            id:
              type: string
              description: Unique identifier for the account group.
              example: Basis
            href:
              type: string
              description: URL endpoint for the account group.
              readOnly: true
              example: /objects/general-ledger/account-group/623
        dimensions:
          type: object
          description: Account allocation basis dimensions.
          allOf:
            - $ref: '#/components/schemas/dimension-ref'
            - type: object
              title: dimensions
              properties:
                location:
                  title: location
                  description: Location dimension.
                  type: object
                  properties:
                    key:
                      type: string
                      description: Unique key for the location.
                      nullable: true
                      example: '22'
                    id:
                      type: string
                      description: Unique identifier for the location.
                      nullable: true
                      example: LOC-22
                    name:
                      type: string
                      description: Name for the location.
                      readOnly: true
                      nullable: true
                      example: California
                    href:
                      type: string
                      description: URL endpoint for the location.
                      readOnly: true
                      example: /objects/company-config/location/22
                department:
                  title: department
                  description: Department dimension.
                  type: object
                  properties:
                    key:
                      type: string
                      description: Unique key for the department.
                      nullable: true
                      example: '11'
                    id:
                      type: string
                      description: Unique identifier for the department.
                      nullable: true
                      example: DEP-11
                    name:
                      type: string
                      description: Name for the department.
                      readOnly: true
                      nullable: true
                      example: Sales and Marketing
                    href:
                      type: string
                      description: URL endpoint for the department.
                      readOnly: true
                      example: /objects/company-config/department/11
        audit:
          $ref: '#/components/schemas/audit'
        href:
          type: string
          description: URL endpoint for the account allocation basis.
          readOnly: true
          example: /objects/general-ledger/account-allocation-basis/14
    general-ledger-account-allocation-group-memberRequiredProperties:
      type: object
      required:
        - id
    status:
      type: string
      description: >-
        Object status. Active objects are fully functional. Inactive objects are essentially hidden and cannot be used or referenced.
      enum:
        - active
        - inactive
      default: active
      example: active
    general-ledger-account-allocation-group-member:
      type: object
      description: General Ledger account allocation group member.
      properties:
        key:
          type: string
          description: System-assigned key for the account allocation group member.
          readOnly: true
          example: '12'
        id:
          type: string
          description: Unique identifier for the account allocation group member.
          readOnly: true
          example: '12'
        glAccountAllocation:
          type: object
          description: General Ledger account allocation.
          properties:
            key:
              type: string
              description: System-assigned key for the account allocation.
              example: '11'
            id:
              type: string
              description: Unique identifier for the account allocation.
              example: '11'
            name:
              type: string
              description: Name for the account allocation.
              readOnly: true
              example: Monthly Expenses
            href:
              type: string
              description: URL endpoint for the account allocation.
              readOnly: true
              example: /objects/general-ledger/account-allocation/11
        glAccountAllocationGroup:
          type: object
          description: General Ledger account allocation group.
          properties:
            key:
              type: string
              description: System-assigned key for the account allocation group.
              example: '141'
            id:
              type: string
              description: Unique identifer for the account allocation group.
              example: '141'
            href:
              type: string
              description: URL endpoint for the account allocation group.
              readOnly: true
              example: /objects/general-ledger/account-allocation-group/141
        status:
          $ref: '#/components/schemas/status'
        audit:
          $ref: '#/components/schemas/audit'
    general-ledger-account-allocation-groupRequiredProperties:
      type: object
      required:
        - name
        - description
        - errorProcessingMethod
        - lines
    general-ledger-account-allocation-group:
      type: object
      description: List of processing groups for dynamic allocations.
      properties:
        key:
          type: string
          description: System-assigned key for the account allocation group.
          readOnly: true
          example: '11'
        id:
          type: string
          description: Unique identifier for the account allocation group.
          readOnly: true
          example: '11'
        name:
          type: string
          description: Name of the account allocation group (20 characters max).
          example: Month End
        description:
          type: string
          description: Description of the account allocation group.
          example: All month end allocations
        errorProcessingMethod:
          type: string
          description: >-
            How to handle any errors encountered when processing the group allocation members.


            - `stop` - The allocation process will stop and not move forward after a member of the allocation group has encountered errors. This is the recommended setting if sequential processing is important for the group.

            - `skipAndContinue` - Skips the allocation member if an error is encountered. Processing then continues starting with the next member of the allocation group. This is the setting recommended if there are no dependencies within the allocation group.
          example: stop
          enum:
            - stop
            - skipAndContinue
        lines:
          type: array
          description: >-
            Account allocation group members. If one allocation is dependent on the completion of a previous allocation, order the members accordingly.
          minItems: 1
          items:
            $ref: '#/components/schemas/general-ledger-account-allocation-group-member'
        status:
          $ref: '#/components/schemas/status'
        audit:
          $ref: '#/components/schemas/audit'
        href:
          type: string
          readOnly: true
          description: URL endpoint for the allocation group.
          example: /objects/general-ledger/account-allocation-group/11
    general-ledger-account-allocation-reverse:
      type: object
      description: >-
        Parameters of the account allocation reversal, used when reversing dynamic account allocations.
      properties:
        key:
          type: string
          description: System-assigned unique key for the account allocation reversal.
          readOnly: true
          example: '14'
        id:
          type: string
          description: Unique identifier for the account allocation reversal.
          readOnly: true
          example: '14'
        glAccountAllocation:
          type: object
          description: Account allocation being reversed.
          readOnly: true
          properties:
            key:
              type: string
              description: System-assigned unique key for the account allocation.
              readOnly: true
              nullable: true
              example: '1'
            id:
              type: string
              description: Unique identifier for the account allocation.
              readOnly: true
              nullable: true
              example: '1'
            href:
              type: string
              description: URL endpoint for the account allocation.
              readOnly: true
              example: /objects/general-ledger/account-allocation/1
        useSourceAccount:
          type: boolean
          description: Specify if the original source account is included in the reversal.
          example: true
          default: false
        glAccount:
          type: object
          description: General ledger account associated with the reversal.
          properties:
            key:
              type: string
              description: System-assigned unique key for the account.
              nullable: true
              example: '1'
            id:
              type: string
              description: Unique identifier for the account.
              nullable: true
              example: '1000'
            name:
              type: string
              description: Name for the account.
              readOnly: true
              nullable: true
              example: Cash
            href:
              type: string
              description: URL endpoint for the account.
              readOnly: true
              example: /objects/general-ledger/account/1
        dimensions:
          type: object
          description: Dimension overrides for the account allocation reversal.
          properties:
            class:
              type: object
              description: >-
                Dimension override for class, applies only if the dimension treatment for class is set to not considered.
              properties:
                key:
                  type: string
                  description: Unique key for the class.
                  example: '6'
                  nullable: true
                name:
                  type: string
                  description: Name for the class.
                  readOnly: true
                  example: Professional Services
                  nullable: true
                id:
                  type: string
                  description: Unique identifier for the class.
                  example: '4'
                  nullable: true
                href:
                  type: string
                  description: URL endpoint for the class.
                  readOnly: true
                  example: /objects/company-config/class/6
            customer:
              type: object
              description: >-
                Dimension override for customer, applies only if the dimension treatment for customer is set to not considered.
              properties:
                key:
                  type: string
                  description: Unique key for the customer.
                  example: '1'
                  nullable: true
                name:
                  type: string
                  description: Name for the customer.
                  readOnly: true
                  example: Power Aerospace Materials
                  nullable: true
                id:
                  type: string
                  description: Unique identifier for the customer.
                  example: '1'
                  nullable: true
                href:
                  type: string
                  description: URL endpoint for the customer.
                  readOnly: true
                  example: /objects/accounts-receivable/customer/1
            employee:
              type: object
              description: >-
                Dimension override for employee, applies only if the dimension treatment for employee is set to not considered.
              properties:
                key:
                  type: string
                  description: Unique key for the employee.
                  nullable: true
                  example: '27'
                name:
                  type: string
                  description: Name for the employee.
                  readOnly: true
                  nullable: true
                  example: John Smith
                id:
                  type: string
                  description: Unique identifier for the employee.
                  nullable: true
                  example: '12'
                href:
                  type: string
                  description: URL endpoint for the employee.
                  readOnly: true
                  example: /objects/company-config/employee/27
            department:
              type: object
              description: >-
                Dimension override for department, applies only if the dimension treatment for department is set to not considered.
              properties:
                key:
                  type: string
                  description: Unique key for the department.
                  example: '6'
                  nullable: true
                id:
                  type: string
                  description: Unique identifier for the department.
                  example: '6'
                  nullable: true
                name:
                  type: string
                  description: Name for the department.
                  readOnly: true
                  example: Marketing
                  nullable: true
                href:
                  type: string
                  description: URL endpoint for the department.
                  readOnly: true
                  example: /objects/company-config/department/6
            location:
              type: object
              description: >-
                Dimension override for location, applies only if the dimension treatment for location is set to not considered.
              properties:
                key:
                  type: string
                  description: Unique key for the location.
                  example: '72'
                  nullable: true
                id:
                  type: string
                  description: Unique identifier for the location.
                  example: AZ
                  nullable: true
                name:
                  type: string
                  description: Name for the location.
                  readOnly: true
                  example: Arizona
                  nullable: true
                href:
                  type: string
                  description: URL endpoint for the location.
                  readOnly: true
                  example: /objects/company-config/location/72
            project:
              type: object
              description: >-
                Dimension override for project, applies only if the dimension treatment for project is set to not considered.
              properties:
                key:
                  type: string
                  description: Unique key for the project.
                  example: '8'
                  nullable: true
                name:
                  type: string
                  description: Name for the project.
                  readOnly: true
                  example: Client Services - Power Aerospace Materials
                  nullable: true
                id:
                  type: string
                  description: Unique identifier for the project.
                  example: '8'
                  nullable: true
                href:
                  type: string
                  description: URL endpoint for the project.
                  readOnly: true
                  example: /objects/projects/project/8
            vendor:
              type: object
              description: >-
                Dimension override for vendor, applies only if the dimension treatment for vendor is set to not considered.
              properties:
                key:
                  type: string
                  description: Unique key for the vendor.
                  example: '48'
                  nullable: true
                name:
                  type: string
                  description: Name for the vendor.
                  readOnly: true
                  example: Packard Bell
                  nullable: true
                id:
                  type: string
                  description: Unique identifier for the vendor.
                  example: '202'
                  nullable: true
                href:
                  type: string
                  description: URL endpoint for the vendor.
                  readOnly: true
                  example: /objects/accounts-payable/vendor/48
            warehouse:
              type: object
              description: >-
                Dimension override for warehouse, applies only if the dimension treatment for warehouse is set to not considered.
              properties:
                key:
                  type: string
                  description: Unique key for the warehouse.
                  example: '2'
                  nullable: true
                name:
                  type: string
                  description: Name for the warehouse.
                  readOnly: true
                  example: Warehouse 2
                  nullable: true
                id:
                  type: string
                  description: Unique identifier for the warehouse.
                  example: WH02
                  nullable: true
                href:
                  type: string
                  description: URL endpoint for the warehouse.
                  readOnly: true
                  example: /objects/inventory-control/warehouse/2
            item:
              type: object
              description: >-
                Dimension override for item, applies only if the dimension treatment for item is set to not considered.
              properties:
                key:
                  type: string
                  description: Unique key for the item.
                  example: '13'
                  nullable: true
                name:
                  type: string
                  description: Name for the item.
                  readOnly: true
                  example: Platform pack
                  nullable: true
                id:
                  type: string
                  description: Unique identifier for the item.
                  example: Case 13
                  nullable: true
                href:
                  type: string
                  description: URL endpoint for the item.
                  example: /objects/inventory-control/item/13
            contract:
              type: object
              description: >-
                Dimension override for contract, applies only if the dimension treatment for contract is set to not considered.
              properties:
                key:
                  type: string
                  description: Unique key for the contract.
                  nullable: true
                  example: '12'
                name:
                  type: string
                  description: Name for the contract.
                  readOnly: true
                  nullable: true
                  example: ACME Widgets - Service
                id:
                  type: string
                  description: Unique identifier for the contract.
                  nullable: true
                  example: CON-0045-1
                href:
                  type: string
                  description: URL endpoint for the contract.
                  readOnly: true
                  example: /objects/contracts/contract/12
        audit:
          $ref: '#/components/schemas/audit'
        href:
          type: string
          description: URL endpoint for the account allocation reversal.
          readOnly: true
          example: /objects/general-ledger/account-allocation-reverse/14
    general-ledger-account-allocation-runRequiredProperties:
      type: object
      required:
        - asOfDate
        - glPostingDate
        - email
    entity-ref:
      type: object
      description: >-
        The entity that the object is associated with. Objects created at the top level do not have an entity reference so the `key`, `id`, and `name` properties will be `null`.
      readOnly: true
      properties:
        key:
          type: string
          description: Entity key.
          readOnly: true
          nullable: true
          example: '46'
        id:
          type: string
          description: Entity ID.
          readOnly: true
          nullable: true
          example: CORP
        name:
          type: string
          description: Entity name.
          readOnly: true
          nullable: true
          example: Corp
        href:
          type: string
          description: URL endpoint for the entity.
          readOnly: true
          example: /objects/company-config/entity/46
    general-ledger-account-allocation-run:
      type: object
      description: List of account allocation runs.
      properties:
        key:
          type: string
          description: System-assigned key for the account allocation run.
          readOnly: true
          example: '23'
        id:
          type: string
          description: Unique identifier for the account allocation run.
          readOnly: true
          example: '23'
        href:
          type: string
          description: URL endpoint for the account allocation run.
          readOnly: true
          example: /objects/account-allocation-run/23
        allocationType:
          type: string
          description: Allocation type.
          readOnly: true
          example: dynamicAllocation
          nullable: true
          enum:
            - null
            - dynamicAllocation
            - restrictionRelease
        asOfDate:
          type: string
          format: date
          description: As of date.
          example: '2024-01-01'
        glPostingDate:
          type: string
          format: date
          description: GL posting date.
          example: '2024-01-01'
        email:
          type: string
          description: Your email address.
          example: <EMAIL>
        state:
          type: string
          description: Status.
          readOnly: true
          example: success
          enum:
            - success
            - failed
            - inProgress
            - queued
            - partialSuccess
        message:
          type: string
          description: Status details.
          readOnly: true
          nullable: true
          example: Account allocation run failed
        parent:
          type: object
          description: Allocation run record number for the parent account.
          readOnly: true
          properties:
            key:
              type: string
              description: Record number
              nullable: true
              readOnly: true
              example: '22'
            id:
              type: string
              description: Record number
              nullable: true
              readOnly: true
              example: '22'
            href:
              type: string
              readOnly: true
              example: /objects/general-ledger/account-allocation-run/22
        allocationRunType:
          type: string
          description: Account allocation run type.
          readOnly: true
          example: regular
          enum:
            - regular
            - parent
            - child
        accountAllocation:
          type: object
          properties:
            id:
              type: string
              description: Unique identifier for the account allocation.
              nullable: true
              example: Monthly expense allocation
            key:
              type: string
              nullable: true
              example: '23'
            href:
              type: string
              description: URL endpoint for the account allocation.
              readOnly: true
              example: /objects/general-ledger/account-allocation/23
        accountAllocationGroup:
          type: object
          properties:
            href:
              type: string
              description: URL endpoint for the account allocation group.
              readOnly: true
              example: /objects/general-ledger/account-allocation-group/23
            key:
              type: string
              nullable: true
              example: '23'
            id:
              type: string
              description: Unique identifier for the account allocation group.
              nullable: true
              example: '23'
            name:
              type: string
              description: Name for the account allocation group.
              readOnly: true
              nullable: true
              example: '23'
        dimensions:
          type: object
          description: Account allocation dimensions.
          properties:
            employee:
              title: employee
              type: object
              properties:
                key:
                  type: string
                  description: Unique key for the employee.
                  nullable: true
                  example: '10'
                id:
                  type: string
                  description: Unique identifier for the employee.
                  nullable: true
                  example: EMP-10
                name:
                  type: string
                  description: Name for the employee.
                  readOnly: true
                  nullable: true
                  example: Thomas, Glenn
                href:
                  type: string
                  example: /objects/company-config/employee/10
                  readOnly: true
            project:
              title: project
              type: object
              properties:
                key:
                  type: string
                  description: Unique key for the project.
                  nullable: true
                  example: '2'
                id:
                  type: string
                  description: Unique identifier for the project.
                  nullable: true
                  example: NET-XML30-2
                name:
                  type: string
                  description: Name for the project.
                  readOnly: true
                  nullable: true
                  example: NET XML 30 phase 2
                href:
                  type: string
                  readOnly: true
                  example: /objects/projects/project/2
        entity:
          $ref: '#/components/schemas/entity-ref'
        audit:
          $ref: '#/components/schemas/audit'
    general-ledger-account-allocation-source:
      type: object
      description: >-
        Parameters of the account allocation source, used when calculating the allocation.
      properties:
        key:
          type: string
          description: System-assigned unique key for the account allocation source.
          readOnly: true
          example: '21'
        id:
          type: string
          description: Unique identifier for the account allocation source.
          readOnly: true
          example: '21'
        percentToAllocate:
          type: string
          description: >-
            Percent of the source pool to be allocated and applied to the source amount during calculation.
          example: '100'
        timePeriod:
          type: object
          description: >-
            Source pool time period, the default time interval for the allocation.
          properties:
            key:
              type: string
              example: '395'
            id:
              type: string
              example: Current Month
            href:
              type: string
              readOnly: true
              example: /objects/general-ledger/reporting-period/395
        reportingBook:
          type: string
          description: Accounting method used when calculating the allocation.
          example: accrual
          enum:
            - accrual
            - cash
          default: accrual
        currency:
          type: string
          description: Base currency used in the entity where the allocation is processed.
          readOnly: true
          example: USD
        useAmountsFrom:
          type: string
          description: >-
            Use amounts from specified reporting book when calculating the allocation.
          example: mainReportingBookAndAlternateBooks
          enum:
            - mainReportingBookAndAlternateBooks
            - alternateBooksOnly
          default: mainReportingBookAndAlternateBooks
        glAccountAllocation:
          type: object
          description: Account allocation.
          properties:
            key:
              type: string
              description: Unique key for the account allocation.
              readOnly: true
              example: '21'
            id:
              type: string
              description: Unique identifier for the account allocation.
              readOnly: true
              example: '21'
            href:
              type: string
              description: URL endpoint for the account allocation.
              readOnly: true
              example: /objects/general-ledger/account-allocation-source/21
        glAccountGroup:
          type: object
          description: Account group to base your allocation split on.
          properties:
            key:
              type: string
              description: Unique key for the account group.
              example: '623'
            id:
              type: string
              description: Unique identifier for the account group.
              example: Basis
            href:
              type: string
              description: URL endpoint for the account group.
              readOnly: true
              example: /objects/general-ledger/account-group/623
        dimensions:
          type: object
          description: Account allocation source dimensions.
          allOf:
            - $ref: '#/components/schemas/dimension-ref'
            - type: object
              title: dimensions
              properties:
                location:
                  title: location
                  description: Location dimension.
                  type: object
                  properties:
                    key:
                      type: string
                      description: Unique key for the location.
                      nullable: true
                      example: '1'
                    id:
                      type: string
                      description: Unique identifier for the location.
                      nullable: true
                      example: LOC-1
                    name:
                      type: string
                      description: Name for the location.
                      readOnly: true
                      nullable: true
                      example: United States of America
                    href:
                      type: string
                      description: URL endpoint for the location.
                      readOnly: true
                      example: /objects/company-config/location/1
                department:
                  title: department
                  description: Department dimension.
                  type: object
                  properties:
                    key:
                      type: string
                      description: Unique key for the department.
                      nullable: true
                      example: '11'
                    id:
                      type: string
                      description: Unique identifier for the department.
                      nullable: true
                      example: DEP-11
                    name:
                      type: string
                      description: Name for the department.
                      readOnly: true
                      nullable: true
                      example: Sales and Marketing
                    href:
                      type: string
                      description: URL endpoint for the department.
                      readOnly: true
                      example: /objects/company-config/department/11
        audit:
          $ref: '#/components/schemas/audit'
        href:
          type: string
          description: URL endpoint for the account allocation source.
          readOnly: true
          example: /objects/general-ledger/account-allocation-source/21
    general-ledger-account-allocation-target:
      type: object
      description: >-
        Parameters of the account allocation target, used when calculating dynamic allocations.
      properties:
        key:
          type: string
          description: System-assigned unique key for the account allocation target.
          readOnly: true
          example: '14'
        id:
          type: string
          description: Unique identifier for the account allocation target.
          readOnly: true
          example: '14'
        glAccountAllocation:
          type: object
          readOnly: true
          description: Account allocation.
          properties:
            key:
              type: string
              description: Unique key for the account allocation.
              readOnly: true
              example: '21'
            id:
              type: string
              description: Unique identifier for the account allocation.
              readOnly: true
              example: '21'
            href:
              type: string
              description: URL endpoint for the account allocation.
              readOnly: true
              example: /objects/general-ledger/account-allocation/21
        glAccountGroup:
          type: object
          description: Account group to base your allocation split on.
          properties:
            key:
              type: string
              description: Unique key for the account group.
              example: '1'
            id:
              type: string
              description: Unique identifier for the account group.
              example: '1'
            href:
              type: string
              description: URL endpoint for the account group.
              readOnly: true
              example: /objects/general-ledger/account-group/1
        isBillable:
          type: boolean
          description: Flag lines in the allocation target as billable.
          default: false
          example: false
        glAccount:
          type: object
          description: General ledger account.
          properties:
            key:
              type: string
              description: Unique key for the account.
              nullable: true
              example: '378'
            id:
              type: string
              description: Unique identifier for the account.
              nullable: true
              example: '1105'
            name:
              type: string
              description: Name for the account.
              readOnly: true
              nullable: true
              example: Target
            href:
              type: string
              description: URL endpoint for the account.
              readOnly: true
              example: /objects/general-ledger/account/378
        journal:
          type: object
          description: Journal where the allocation will be recorded when generated.
          properties:
            key:
              type: string
              description: Unique key for the journal.
              example: '39'
            id:
              type: string
              description: Unique identifier for the journal.
              example: Others
            title:
              type: string
              description: Title for the account.
              readOnly: true
              example: Accommodation Expenses
            href:
              type: string
              description: URL endpoint for the journal.
              readOnly: true
              example: /objects/general-ledger/journal/39
        exchangeRate:
          type: object
          description: Exchange rate details used to calculate the base amount.
          properties:
            typeId:
              type: string
              description: >-
                The type of exchange rate used to calculate the base amount from the transaction amount.
              example: '1'
              nullable: true
        dimensions:
          type: object
          description: Dimension overrides for the account allocation target.
          properties:
            class:
              type: object
              description: >-
                Dimension override for class, applies only if the dimension treatment for class is set to not considered.
              properties:
                key:
                  type: string
                  description: Unique key for the class.
                  example: '6'
                  nullable: true
                name:
                  type: string
                  description: Name for the class.
                  readOnly: true
                  example: '4'
                  nullable: true
                id:
                  type: string
                  description: Unique identifier for the class.
                  example: Professional Services
                  nullable: true
                href:
                  type: string
                  description: URL endpoint for the class.
                  readOnly: true
                  example: /objects/company-config/class/6
            customer:
              type: object
              description: >-
                Dimension override for customer, applies only if the dimension treatment for customer is set to not considered.
              properties:
                key:
                  type: string
                  description: Unique key for the customer.
                  example: '1'
                  nullable: true
                name:
                  type: string
                  description: Name for the customer.
                  readOnly: true
                  example: ACME Widgets
                  nullable: true
                id:
                  type: string
                  description: Unique identifier for the customer.
                  example: ACME
                  nullable: true
                href:
                  type: string
                  description: URL endpoint for the customer.
                  readOnly: true
                  example: /objects/accounts-receivable/customer/2
            employee:
              type: object
              description: >-
                Dimension override for employee, applies only if the dimension treatment for employee is set to not considered.
              properties:
                key:
                  type: string
                  description: Unique key for the employee.
                  example: '27'
                  nullable: true
                name:
                  type: string
                  description: Name for the employee.
                  readOnly: true
                  example: Eberhardt
                  nullable: true
                id:
                  type: string
                  description: Unique identifier for the employee.
                  example: '12'
                  nullable: true
                href:
                  type: string
                  description: URL endpoint for the employee.
                  readOnly: true
                  example: /objects/company-config/employee/27
            department:
              type: object
              description: >-
                Dimension override for department, applies only if the dimension treatment for department is set to not considered.
              properties:
                key:
                  type: string
                  description: Unique key for the department.
                  example: '12'
                  nullable: true
                id:
                  type: string
                  description: Unique identifier for the department.
                  example: '12'
                  nullable: true
                name:
                  type: string
                  description: Name for the department.
                  readOnly: true
                  example: Sales
                  nullable: true
                href:
                  type: string
                  description: URL endpoint for the department.
                  readOnly: true
                  example: /objects/company-config/department/12
            location:
              type: object
              description: >-
                Dimension override for location, applies only if the dimension treatment for location is set to not considered.
              properties:
                key:
                  type: string
                  description: Unique key for the location.
                  example: '72'
                  nullable: true
                id:
                  type: string
                  description: Unique identifier for the location.
                  example: AZ
                  nullable: true
                name:
                  type: string
                  description: Name for the location.
                  readOnly: true
                  example: Arizona
                  nullable: true
                href:
                  type: string
                  description: URL endpoint for the location.
                  readOnly: true
                  example: /objects/company-config/location/72
            project:
              type: object
              description: >-
                Dimension override for project, applies only if the dimension treatment for project is set to not considered.
              properties:
                key:
                  type: string
                  description: Unique key for the project.
                  example: '8'
                  nullable: true
                name:
                  type: string
                  description: Name for the project.
                  readOnly: true
                  example: Client Services - Power Aerospace Materials
                  nullable: true
                id:
                  type: string
                  description: Unique identifier for the project.
                  example: '8'
                  nullable: true
                href:
                  type: string
                  description: URL endpoint for the project.
                  readOnly: true
                  example: /objects/projects/project/8
            vendor:
              type: object
              description: >-
                Dimension override for vendor, applies only if the dimension treatment for vendor is set to not considered.
              properties:
                key:
                  type: string
                  description: Unique key for the vendor.
                  example: '48'
                  nullable: true
                name:
                  type: string
                  description: Name for the vendor.
                  readOnly: true
                  example: Packard Bell
                  nullable: true
                id:
                  type: string
                  description: Unique identifier for the vendor.
                  example: '202'
                  nullable: true
                href:
                  type: string
                  description: URL endpoint for the vendor.
                  readOnly: true
                  example: /objects/accounts-payable/vendor/48
            warehouse:
              type: object
              description: >-
                Dimension override for warehouse, applies only if the dimension treatment for warehouse is set to not considered.
              properties:
                key:
                  type: string
                  description: Unique key for the warehouse.
                  example: '2'
                  nullable: true
                name:
                  type: string
                  description: Name for the warehouse.
                  readOnly: true
                  example: WH02 Name
                  nullable: true
                id:
                  type: string
                  description: Unique identifier for the warehouse.
                  example: WH02
                  nullable: true
                href:
                  type: string
                  description: URL endpoint for the warehouse.
                  readOnly: true
                  example: /objects/inventory-control/warehouse/2
            item:
              type: object
              description: >-
                Dimension override for item, applies only if the dimension treatment for item is set to not considered.
              properties:
                key:
                  type: string
                  description: Unique key for the item.
                  example: '13'
                  nullable: true
                name:
                  type: string
                  description: Name for the item.
                  readOnly: true
                  example: Platform pack
                  nullable: true
                id:
                  type: string
                  description: Unique identifier for the item.
                  example: Case 13
                  nullable: true
                href:
                  type: string
                  description: URL endpoint for the item.
                  example: /objects/inventory-control/item/13
            contract:
              type: object
              description: >-
                Dimension override for contract, applies only if the dimension treatment for contract is set to not considered.
              properties:
                key:
                  type: string
                  description: Unique key for the contract.
                  example: '12'
                  nullable: true
                name:
                  type: string
                  description: Name for the contract.
                  readOnly: true
                  example: ACME Widgets - Service
                  nullable: true
                id:
                  type: string
                  description: Unique identifier for the contract.
                  example: CON-0045-1
                  nullable: true
                href:
                  type: string
                  description: URL endpoint for the contract.
                  readOnly: true
                  example: /objects/contracts/contract/12
        audit:
          $ref: '#/components/schemas/audit'
        href:
          type: string
          description: URL endpoint for the account allocation target.
          readOnly: true
          example: /objects/general-ledger/account-allocation-target/14
    general-ledger-account-allocationRequiredProperties:
      type: object
      required:
        - name
    general-ledger-account-allocation:
      type: object
      description: Parameters of the account allocation definition.
      properties:
        key:
          type: string
          description: System-assigned key for the account allocation definition.
          readOnly: true
          example: '21'
        id:
          type: string
          description: Unique identifier for the account allocation definition.
          readOnly: true
          example: '21'
        name:
          type: string
          description: >-
            Name for the account allocation definition (must be 20 characters or less).
          example: AllocaForAdjBookNew
        description:
          type: string
          description: Description of the account allocation definition.
          example: Monthly allocation of expenses
        methodology:
          type: string
          description: >-
            The reasoning and methodology behind the configuration of the account allocation definition.
          example: Expense allocation across revenue earning departments
        journal:
          type: object
          description: >-
            The journal where allocated transactions are recorded when the account allocation is generated based on its definition.
          readOnly: true
          properties:
            key:
              type: string
              description: System-assigned key for the journal.
              readOnly: true
              example: '39'
            id:
              type: string
              description: Unique identifier for the journal.
              readOnly: true
              example: Others
            href:
              type: string
              description: URL endpoint for the journal.
              readOnly: true
              example: /objects/general-ledger/journal/39
        attachment:
          type: object
          description: >-
            Supporting documents that provide reasoning and methodology for the account allocation.
          properties:
            key:
              type: string
              description: System-assigned key for the attachment.
              example: '21'
              nullable: true
            id:
              type: string
              description: Unique identifier for the attachment.
              example: Sales01
              nullable: true
            href:
              type: string
              description: URL endpoint for the attachment.
              readOnly: true
              example: /objects/attachment/21
        latestVersion:
          type: integer
          description: Latest version of the account allocation definition.
          nullable: true
          example: null
        dimensionTreatment:
          type: object
          description: >-
            Determine how dimensions behave during allocation calculations, the level of detail included in the created allocation entries and whether the allocation occurs within a single entity, or across entities.


            Valid values:


            - `notConsidered` - The dimension is not used for calculations during the generation of the allocation but can still be used as a filter to narrow the source pool or basis. You can either leave the dimension with no value in the allocated entry, or apply a single value by using the override parameters in `glAccountAllocationTarget`.

            - `preserveValues` - These dimensions keep their original value assigned during initial entry. The dimension is included in the calculations for the source and the basis to ensure that the proportional distribution is kept when the allocated entry is recorded.

            - `allocationFocus` - Allocate or reclassify dimensions based on the calculation method selected in `glAccountAllocationBasis`. These dimensions are included in any dynamic calculations for the basis.
          properties:
            location:
              type: string
              description: >-
                How location dimension values are applied in the account allocation definition.
              default: notConsidered
              example: preserveValues
              enum:
                - notConsidered
                - preserveValues
                - allocationFocus
            department:
              type: string
              description: >-
                How department dimension values are applied in the account allocation definition.
              example: allocationFocus
              default: notConsidered
              enum:
                - notConsidered
                - preserveValues
                - allocationFocus
            project:
              type: string
              description: >-
                How project dimension values are applied in the account allocation definition.
              example: notConsidered
              default: notConsidered
              enum:
                - notConsidered
                - preserveValues
                - allocationFocus
                - perDimensionValue
            customer:
              type: string
              description: >-
                How customer dimension values are applied in the account allocation definition.
              example: notConsidered
              default: notConsidered
              enum:
                - notConsidered
                - preserveValues
                - allocationFocus
            vendor:
              type: string
              description: >-
                How vendor dimension values are applied in the account allocation definition.
              example: notConsidered
              default: notConsidered
              enum:
                - notConsidered
                - preserveValues
                - allocationFocus
            employee:
              type: string
              description: >-
                How employee dimension values are applied in the account allocation definition.
              example: notConsidered
              default: notConsidered
              enum:
                - notConsidered
                - preserveValues
                - allocationFocus
                - perDimensionValue
            class:
              type: string
              description: >-
                How class dimension values are applied in the account allocation definition.
              example: notConsidered
              default: notConsidered
              enum:
                - notConsidered
                - preserveValues
                - allocationFocus
            item:
              type: string
              description: >-
                How item dimension values are applied in the account allocation definition.
              example: notConsidered
              default: notConsidered
              enum:
                - notConsidered
                - preserveValues
                - allocationFocus
            warehouse:
              type: string
              description: >-
                How warehouse dimension values are applied in the account allocation definition.
              example: notConsidered
              default: notConsidered
              enum:
                - notConsidered
            contract:
              type: string
              description: >-
                How contract dimension values are applied in the account allocation definition.
              example: notConsidered
              default: notConsidered
              enum:
                - notConsidered
        activityDelta:
          type: boolean
          description: >
            When set to `true`, the activity delta includes another layer of validation for your allocation. For example, you create an allocation that needs to be run more often than once a period (payroll, for example). Enabling the activity delta tells Intacct to validate that your account setup is designed to clear the source amounts with each allocation generation. This is accomplished as follows:


            - By making sure that the accounts selected for your source pool are used as the reversing source pool selection.

            - If an alternate specific account is used for the reversal, it's included in the source pool account group so that it removes any prior allocation activity.


            Allocated entries must be reflected in the source books to correctly consider the prior allocation impact. This option is only effective if the source account is used for the pool reversal in the allocation target, either via `glAccountAllocationTarget` or `glAccountAllocationReverse`.

          default: false
          example: true
        autoReversePriorPostedJournalEntry:
          type: boolean
          description: "When set to `true`, Intacct checks for a previously generated allocation in the same source period. If found, it's reversed as the first step when the new allocation is generated.\n\nThis reversal occurs on the same posting date as the new allocation. The reversal resets the allocation amount for the new allocation to consider the source amounts as though the prior allocation didn't take place. \n\nThe result is a new, more accurate allocation that uses the latest basis information to allocate for the entire period. This property cannot be set to `true` if allocations have already been generated with this definition.\n"
          default: false
          example: false
        glAccountAllocationSource:
          type: array
          description: The source pool specified in the account allocation definition.
          minItems: 1
          maxItems: 1
          items:
            $ref: '#/components/schemas/general-ledger-account-allocation-source'
        glAccountAllocationBasis:
          type: array
          description: "Determines how the account allocation definition distributes the source pool amount across each allocation-focused dimension. \n"
          minItems: 1
          maxItems: 1
          items:
            $ref: '#/components/schemas/general-ledger-account-allocation-basis'
        glAccountAllocationTarget:
          type: array
          description: >-
            Specifies where the account allocation definition distributes source pool amounts during calculation.
          minItems: 1
          maxItems: 1
          items:
            $ref: '#/components/schemas/general-ledger-account-allocation-target'
        glAccountAllocationReverse:
          type: array
          description: >-
            Determines whether to keep the allocated amounts in the allocation book or move them using copy and reverse to another book (like accrual).
          minItems: 1
          maxItems: 1
          items:
            $ref: '#/components/schemas/general-ledger-account-allocation-reverse'
        allowAllocation:
          type: string
          description: >-
            Specifies how the account allocation definition interacts with multiple entities.


            - `withinOneEntity` - You can only select source and basis options from the entity in which the allocation originates. Target entries are made in the same entity as the reversing source entries.

            - ` acrossEntities` - For companies with multiple entities, account allocations can include more than one entity, even if they have different currencies. If using ` acrossEntities`, specify an exchange rate type in the `glAccountAllocationTarget`.
          example: withinOneEntity
          enum:
            - withinOneEntity
            - acrossEntities
          default: withinOneEntity
        status:
          $ref: '#/components/schemas/status'
        audit:
          type: object
          allOf:
            - $ref: '#/components/schemas/audit'
            - type: object
              properties:
                createdBy:
                  type: string
                  description: >-
                    Unique identifier of the user who created the account allocation definition.
                  example: Admin
                  readOnly: true
                  nullable: true
                modifiedBy:
                  type: string
                  description: >-
                    Unique identifier of the user who modified the account allocation definition.
                  example: Admin
                  readOnly: true
                  nullable: true
        entity:
          $ref: '#/components/schemas/entity-ref'
        href:
          type: string
          description: URL endpoint of the account allocation definition.
          readOnly: true
          example: /objects/general-ledger/account-allocation/21
    general-ledger-account-category:
      type: object
      description: Account category
      properties:
        key:
          type: string
          description: System-assigned key for the account category.
          readOnly: true
          example: '134'
        id:
          type: string
          description: Unique identifier for the account category.
          readOnly: true
          example: Cash and Cash Equivalents
        href:
          type: string
          description: URL endpoint for the account category.
          readOnly: true
          example: /objects/general-ledger/account-category/23
        isStatistical:
          type: boolean
          description: >-
            Indicates whether the account category is used for tracking statistical data such as operational metrics.
          example: false
        accountType:
          type: string
          description: Specifies the account type associated with the account category.
          example: asset
          enum:
            - asset
            - liability
            - equity
            - income
            - costOfRevenue
            - expense
            - other
        normalBalance:
          type: string
          description: Normal balance.
          example: debit
          enum:
            - debit
            - credit
    general-ledger-account-group-category-member:
      type: object
      description: Account group category member.
      properties:
        key:
          type: string
          description: System-assigned key for the account group category member.
          readOnly: true
          example: '23'
        id:
          type: string
          description: Account group category member ID.
          readOnly: true
          example: '23'
        href:
          type: string
          description: Endpoint for the account group category member.
          readOnly: true
          example: /objects/general-ledger/account-group-category-member/23
        sortOrder:
          type: integer
          description: Sort order.
          example: 1
        accountCategory:
          type: object
          properties:
            key:
              type: string
              example: '21'
            id:
              type: string
              example: Fixed Assets
            href:
              type: string
              readOnly: true
              example: /objects/general-ledger/account-category/21
        glAccountGroup:
          type: object
          readOnly: true
          properties:
            key:
              type: string
              readOnly: true
              example: '21'
            id:
              type: string
              readOnly: true
              example: Assets
            href:
              type: string
              readOnly: true
              example: /objects/general-ledger/account-group/21
        audit:
          $ref: '#/components/schemas/audit'
    general-ledger-account-group-computation:
      type: object
      description: Computation account groups
      properties:
        key:
          type: string
          description: System-assigned key for the computation account group.
          readOnly: true
          example: '23'
        id:
          type: string
          description: Unique identifier for the computation account group.
          readOnly: true
          example: '23'
        href:
          type: string
          description: URL endpoint for the computation account group.
          readOnly: true
          example: /objects/general-ledger/account-group-computation/23
        formulaLeft:
          type: object
          oneOf:
            - title: Account group
              type: object
              properties:
                glAccountGroup:
                  type: object
                  properties:
                    id:
                      type: string
                      description: >-
                        Unique identifier for the left-hand account group in the computation formula.
                      nullable: true
                      example: Payable account group
                    key:
                      type: string
                      description: >-
                        Key for the left-hand account group in the computation formula.
                      nullable: true
                      example: '21'
                    href:
                      type: string
                      description: >-
                        URL endpoint for the left-hand account group in the computation formula.
                      readOnly: true
                      example: /objects/general-ledger/account-group/21
            - title: Account
              type: object
              properties:
                glAccount:
                  type: object
                  properties:
                    id:
                      type: string
                      description: >-
                        Unique identifier for the left-hand account in the computation formula.
                      nullable: true
                      example: '1000'
                    key:
                      type: string
                      description: >-
                        Key for the left-hand account in the computation formula.
                      nullable: true
                      example: '402'
                    name:
                      type: string
                      description: >-
                        Name for the left-hand account in the computation formula.
                      readOnly: true
                      nullable: true
                      example: Cash account
                    href:
                      type: string
                      description: >-
                        URL endpoint for the left-hand account in the computation formula.
                      readOnly: true
                      example: /objects/general-ledger/account/402
                asOf:
                  type: string
                  description: >-
                    As of period for the left-hand account in the computation formula.
                  example: forPeriod
                  enum:
                    - forPeriod
                    - startOfPeriod
                    - endOfPeriod
            - title: Constant
              type: object
              properties:
                constant:
                  type: string
                  description: >-
                    Constant for the left-hand account in the computation formula, for example, a tax rate or discount.
                  nullable: true
                  example: '12'
        operator:
          type: string
          description: Operator for the computation formula.
          example: add
          enum:
            - add
            - subtract
            - multiply
            - divide
        formulaRight:
          type: object
          oneOf:
            - title: Account group
              type: object
              properties:
                glAccountGroup:
                  type: object
                  properties:
                    id:
                      type: string
                      description: >-
                        Unique identifier for the right-hand account group in the computation formula.
                      nullable: true
                      example: Payable account group
                    key:
                      type: string
                      description: >-
                        Key for the right-hand account group in the computation formula.
                      nullable: true
                      example: '21'
                    href:
                      type: string
                      description: >-
                        URL endpoint for the right-hand account group in the computation formula.
                      readOnly: true
                      example: /objects/general-ledger/account-group/21
            - title: Account
              type: object
              properties:
                glAccount:
                  type: object
                  properties:
                    id:
                      type: string
                      description: >-
                        Unique identifier for the right-hand account in the computation formula.
                      nullable: true
                      example: '1000'
                    key:
                      type: string
                      description: >-
                        Key for the right-hand account in the computation formula.
                      nullable: true
                      example: '402'
                    name:
                      type: string
                      description: >-
                        Name for the right-hand account in the computation formula.
                      nullable: true
                      readOnly: true
                      example: Cash account
                    href:
                      type: string
                      description: >-
                        URL endpoint for the right-hand account in the computation formula.
                      readOnly: true
                      example: /objects/general-ledger/account/402
                asOf:
                  type: string
                  description: >-
                    As of period for the right-hand account in the computation formula.
                  example: forPeriod
                  enum:
                    - forPeriod
                    - startOfPeriod
                    - endOfPeriod
            - title: Constant
              type: object
              properties:
                constant:
                  type: string
                  description: >-
                    Constant for the right-hand account in the computation formula, for example, a tax rate or discount.
                  nullable: true
                  example: '12'
        numberOfDecimalPlaces:
          type: integer
          description: Number of decimal places to display.
          minimum: 0
          maximum: 9
          example: 2
        displayAs:
          type: string
          description: Type of number to display.
          example: number
          enum:
            - number
            - percent
            - ratioWithDecimals
            - ratioWithoutDecimals
            - dailyAverage
            - weeklyAverage
            - monthlyAverage
            - quarterlyAverage
        unit:
          type: string
          description: Unit of measurement.
          example: $
        unitPlacement:
          type: string
          description: Placement of the unit of measurement.
          example: right
          nullable: true
          enum:
            - null
            - left
            - right
        glAccountGroup:
          type: object
          readOnly: true
          properties:
            key:
              type: string
              readOnly: true
              example: '21'
            id:
              type: string
              description: >-
                Reference to the owning account group for this account group computation.
              readOnly: true
              example: Payable account group
            href:
              type: string
              readOnly: true
              example: /objects/general-ledger/account-group/21
        audit:
          $ref: '#/components/schemas/audit'
    general-ledger-account-group-member:
      type: object
      description: General ledger account group member.
      properties:
        key:
          type: string
          description: System-assigned unique key for the account group member.
          example: '23'
        id:
          type: string
          description: Unique identifier for the account group member.
          example: '23'
        href:
          type: string
          description: URL endpoint for the account group member.
          readOnly: true
          example: /objects/general-ledger/account-group-member/23
        sortOrder:
          type: integer
          description: Sort order for the account group members.
          example: 1
        accountGroup:
          type: object
          description: Account group containing the account group member.
          properties:
            key:
              type: string
              description: Account group key.
              example: '22'
            id:
              type: string
              description: Account group ID.
              example: Payroll group
            href:
              type: string
              readOnly: true
              example: /objects/general-ledger/account-group/22
        glAccountGroup:
          type: object
          readOnly: true
          description: General ledger account group.
          properties:
            key:
              type: string
              readOnly: true
              description: General ledger account group key.
              example: '21'
            id:
              type: string
              readOnly: true
              description: General ledger account group ID.
              example: 85 - Cash
            href:
              type: string
              readOnly: true
              example: /objects/general-ledger/account-group/21
        audit:
          $ref: '#/components/schemas/audit'
    general-ledger-account-group-purposeRequiredProperties:
      type: object
      required:
        - id
    general-ledger-account-group-purpose:
      type: object
      description: >-
        Account group purposes let you filter account groups according to why you might use them, which is particularly useful in financial reporting. You create account group purposes and then assign them to account groups when you create or update the groups.
      properties:
        key:
          type: string
          description: System-assigned key for the account group purpose.
          readOnly: true
          example: '21'
        id:
          type: string
          description: Name for the account group purpose.
          example: P&L
        href:
          type: string
          description: URL endpoint of the account group purpose.
          readOnly: true
          example: /objects/general-ledger/account-group-purpose/2
        status:
          $ref: '#/components/schemas/status'
        audit:
          $ref: '#/components/schemas/audit'
        entity:
          $ref: '#/components/schemas/entity-ref'
    general-ledger-account-groupRequiredProperties:
      type: object
      required:
        - id
    general-ledger-account-range:
      type: object
      description: Account range
      properties:
        key:
          type: string
          description: System-assigned key for the account range.
          readOnly: true
          example: '2'
        id:
          type: string
          description: Unique identifier for the account range.
          readOnly: true
          example: '2'
        sortOrder:
          type: integer
          description: Sort order for the account range.
          example: 2
          default: 0
        rangeFrom:
          type: string
          description: Lowest account number to include in the account range.
          example: '1000'
        rangeTo:
          type: string
          description: Highest account number to include in the account range.
          example: '1001'
        glAccountGroup:
          type: object
          readOnly: true
          description: Account group that includes the account range.
          properties:
            key:
              type: string
              description: Unique key for the account group.
              readOnly: true
              example: '21'
            id:
              type: string
              description: Unique identifier for the account group.
              readOnly: true
              example: Accounts Payable group
            href:
              type: string
              description: URL endpoint for the account group.
              readOnly: true
              example: /objects/general-ledger/account-group/21
        audit:
          $ref: '#/components/schemas/audit'
        href:
          type: string
          description: URL endpoint for the account range.
          readOnly: true
          example: /objects/general-ledger/account-range/2
    general-ledger-account-group:
      type: object
      description: General ledger account groups.
      properties:
        key:
          type: string
          description: System-assigned key for the account group.
          readOnly: true
          example: '33'
        id:
          type: string
          description: ID for the account group.
          example: 85 - Cash
        title:
          type: string
          description: Header row title for the account group on financial reports.
          example: Cash
        displayTotalLineAs:
          type: string
          description: Total line title for the account group on financial reports.
          example: Total Accounts Payable
        manager:
          type: string
          description: Name of the manager responsible for the account group.
          example: John Smith
        calculationMethod:
          type: string
          description: >
            Calculation method used when calculating amounts on reports for each account group.


            Valid calculation methods are:

              - `period` - calculates only the amounts in the selected period.
              - `startOfPeriod` - calculates the amounts cumulatively up to the start of the selected period.
              - `endOfPeriod` - calculates the amounts cumulatively up to the end of the selected period.
              - `null` - not supported.
          example: period
          enum:
            - null
            - period
            - startOfPeriod
            - endOfPeriod
          nullable: true
          default: period
        normalBalance:
          type: string
          description: "Use this account group as either a debit balance or a credit balance, for example Cash is generally a debit balance, and Sales, a credit balance. \n\nMake sure that the normal balance is the same for all accounts in the account group. The normal balance takes the designation from the top of the group. For example, if the parent is debit normal, all included accounts will be added, regardless of their normal balance setting. \n"
          default: credit
          example: credit
          enum:
            - debit
            - credit
        groupType:
          type: string
          description: >
            Indicate the type of account group. Different account group types yield different results in your financial reports.


            Valid account group types:

            - `accounts` - the simplest and most basic type of account group types, it consists of one or  more accounts from the chart of accounts.

            - `statisticalAccounts` - includes accounts that contain specific non-financial data, used for calculating ratios and business metrics such as, headcount or square footage.

            - `computation` - consists of other account groups or individual accounts that you use as components in a mathematical equation; results of the equation display in your financial report.

            - `category` - contains accounts based on categories (account groups) configured when your Intacct company was first set up.

            - `statisticalCategory` - contains statistical accounts, also based on the pre-configured categories.

            - `null` - not supported.

          example: groups
          enum:
            - null
            - accounts
            - groups
            - statisticalAccounts
            - computation
            - category
            - statisticalCategory
          nullable: true
          default: accounts
        isKPI:
          type: boolean
          description: "Use `true` to specify the account group is a KPI account group. Key Performance Indicators (KPI) measure how effectively your company is meeting business objectives.  KPI account groups can be used in financial reports just like any other account groups. \n"
          example: false
          default: false
        includeChildAmount:
          type: boolean
          description: >-
            Use `true` to roll up an entire hierarchy of child transactions into one total.
          example: true
          default: false
        accountGroupPurpose:
          type: object
          description: Account group purpose to associate with this account group.
          properties:
            key:
              type: string
              description: System-assigned key for the account group purpose.
              nullable: true
              example: '126'
            id:
              type: string
              description: Unique identifier of the account group purpose.
              nullable: true
              example: P&L
            href:
              type: string
              description: URL endpoint for the account group purpose.
              readOnly: true
              example: /objects/company-config/account-group-purpose/126
        reportFilters:
          description: "Filtering on the account group restricts the information displayed to certain dimensions. You can also filter for multiple locations and departments at the same time by using a dimension group.\n\nValid values for all report filters, use in reports where the account group is specified:\n  \n- `noFilter` - includes all account group transactions for the dimension and it's sub-dimensions.\n- `null` - includes account group transactions where the dimension is not specified.\n- `specific` - only includes account group transactions for the specified dimension or dimension group (only for location and department). \n- `specificHierarchy` - includes account group transactions for the specified dimension hierarchy, including it's sub-dimensions.\n"
          type: object
          properties:
            location:
              type: string
              description: >
                Include account group transactions for a specific location (including sub-locations) or location group (contains multiple locations) in your reports where the account group is specified.

              nullable: true
              example: noFilter
              default: noFilter
              enum:
                - null
                - noFilter
                - specificHierarchy
                - specific
                - nullValue
            debitOrCredit:
              type: string
              description: >
                Include debit only or credit only account group transactions in your reports where the account group is specified. Use `both` to include all debit and credit transactions within the account group. This filter has limited options, it works on charts, graphs, performance cards, and financial reports, but is ignored in GL reports.

              example: both
              enum:
                - null
                - both
                - debitOnly
                - creditOnly
              nullable: true
              default: null
            department:
              type: string
              description: >
                Include account group transactions for a specific department (including sub-departments) or department group (contains multiple departments) in your reports where the account group is specified.

              default: null
              nullable: true
              example: noFilter
              enum:
                - null
                - noFilter
                - specificHierarchy
                - specific
                - nullValue
            vendor:
              type: string
              description: "Include account group transactions for a specific vendor (including sub-vendors) in your reports where the account group is specified. \n"
              example: noFilter
              enum:
                - null
                - noFilter
                - specificHierarchy
                - specific
                - unspecified
              nullable: true
              default: null
            customer:
              type: string
              description: "Include account group transactions for a specific customer (including sub-customers) in your reports where the account group is specified. \n"
              default: null
              nullable: true
              example: noFilter
              enum:
                - null
                - noFilter
                - specificHierarchy
                - specific
                - nullValue
            project:
              type: string
              description: >
                Include account group transactions for a specific project (including sub-projects) in your reports where the account group is specified.

              example: noFilter
              default: null
              nullable: true
              enum:
                - null
                - noFilter
                - specificHierarchy
                - specific
                - nullValue
            employee:
              type: string
              description: >
                Include account group transactions for a specific employee (including sub-employees) in your reports where the account group is specified.

              nullable: true
              default: null
              example: noFilter
              enum:
                - null
                - noFilter
                - specificHierarchy
                - specific
                - nullValue
            item:
              type: string
              description: >
                Include all account group transactions with specific inventory items in your reports where the account group is specified. This filter has limited options, it works on charts, graphs, performance cards, and financial reports, but is ignored in GL reports.

              nullable: true
              default: null
              example: noFilter
              enum:
                - null
                - noFilter
                - specific
                - nullValue
            class:
              type: string
              description: "Include account group transactions for a specific class (including sub-classes) in your reports where the account group is specified. \n"
              nullable: true
              default: null
              example: noFilter
              enum:
                - null
                - noFilter
                - specificHierarchy
                - specific
                - nullValue
            contract:
              type: string
              description: >
                Include account group transactions for a specific contract (including sub-contracts) in your reports where the account group is specified.

              nullable: true
              default: null
              example: noFilter
              enum:
                - null
                - noFilter
                - specificHierarchy
                - specific
                - nullValue
            task:
              type: string
              description: >
                Include all account group transactions with tasks in your reports where the account group is specified. This filter has limited options, it works on charts, graphs, performance cards, and financial reports, but is ignored in GL reports.

              nullable: true
              default: null
              example: noFilter
              enum:
                - null
                - noFilter
                - nullValue
            warehouse:
              type: string
              description: "Include account group transactions for a specific warehouse (including sub-warehouses) in your reports where the account group is specified. \n"
              nullable: true
              default: null
              example: noFilter
              enum:
                - null
                - noFilter
                - specificHierarchy
                - specific
                - nullValue
            costType:
              type: string
              description: >
                Include all account group transactions with cost types in your reports where the account group is specified. This filter has limited options, it works on charts, graphs, performance cards, and financial reports, but is ignored in GL reports.

              nullable: true
              default: null
              example: noFilter
              enum:
                - null
                - noFilter
                - nullValue
            asset:
              type: string
              description: >
                Include account group transactions for a specific asset (including sub-assets) in your reports where the account group is specified.

              nullable: true
              default: null
              example: noFilter
              enum:
                - null
                - noFilter
                - specificHierarchy
                - specific
                - nullValue
            affiliateEntity:
              type: string
              description: >
                Include all account group transactions with specific affiliate entities in your reports where the account group is specified. This filter has limited options, it works on charts, graphs, performance cards, and financial reports, but is ignored in GL reports.

              example: noFilter
              enum:
                - null
                - noFilter
                - specific
                - nullValue
        dimensions:
          type: object
          description: "Filter by the specified dimension values if `reportFilters` is set to `specific` or `specificHierarchy`. \n\nA dimension is a classification system used to organize, sort, and report on your company information in meaningful ways. Each dimension has a set of related values with transactions and entries. Every transaction you enter can be tagged with multiple dimension values for identification and reporting.\n"
          allOf:
            - $ref: '#/components/schemas/dimension-ref'
            - type: object
              title: dimensions
              properties:
                location:
                  type: object
                  description: "Location to filter by if `reportFilters.location` is set to `specific` or `specificHierarchy`. \n\nYou can filter by a single `location`, or alternatively, you can filter by multiple locations using a `locationGroup`. You cannot filter both `location` and `locationGroup` at the same time. \n"
                  title: location
                  properties:
                    key:
                      type: string
                      example: '1'
                      nullable: true
                    id:
                      type: string
                      example: '1'
                      nullable: true
                    name:
                      type: string
                      example: New York
                      readOnly: true
                      nullable: true
                    href:
                      type: string
                      description: URL endpoint for the location.
                      readOnly: true
                      example: /objects/company-config/location/1
                locationGroup:
                  type: object
                  description: "Location group to filter by if `reportFilters.location` is set to `specific` or `specificHierarchy`.\n\nYou can filter by multiple locations using a `locationGroup`, or alternatively, you can filter by a single `location`. You cannot filter both `locationGroup` and `location` at the same time. \n"
                  properties:
                    key:
                      type: string
                      example: '7'
                      nullable: true
                    id:
                      type: string
                      example: USA-GRP
                      nullable: true
                    name:
                      type: string
                      example: USA Locations
                      readOnly: true
                      nullable: true
                    href:
                      type: string
                      description: URL endpoint for the location group.
                      readOnly: true
                      example: /objects/company-config/location-group/7
                department:
                  type: object
                  description: >
                    Department to filter by if `reportFilters.department` is set to `specific` or `specificHierarchy`.


                    You can filter by a single `department`, or alternatively, you can filter by multiple departments using a `departmentGroup`. You cannot filter both `department` and `departmentGroup` at the same time.

                  title: department
                  properties:
                    key:
                      type: string
                      description: System-assigned key for the department.
                      nullable: true
                      example: '9'
                    id:
                      type: string
                      description: Unique identifier of the department.
                      nullable: true
                      example: PRS
                    name:
                      type: string
                      description: Name of the department.
                      readOnly: true
                      nullable: true
                      example: Partner Sales
                    href:
                      type: string
                      description: URL endpoint for the department.
                      readOnly: true
                      example: /objects/company-config/department/9
                departmentGroup:
                  type: object
                  description: "Department group to filter by if `reportFilters.department` is set to `specific` or `specificHierarchy`. If you've defined department groups, these will be available to you in addition to the individual departments. \n\nYou can filter by multiple departments using a `departmentGroup`, or alternatively, you can filter by a single `department`. You cannot filter both `departmentGroup` and `department` at the same time.\n"
                  properties:
                    key:
                      type: string
                      description: System-assigned key for the department group.
                      nullable: true
                      example: '3086'
                    id:
                      type: string
                      description: Unique identifier of the department group.
                      nullable: true
                      example: SA-GRP
                    name:
                      type: string
                      description: Name of the department group.
                      readOnly: true
                      example: Sales Group
                      nullable: true
                    href:
                      type: string
                      description: URL endpoint for the department group.
                      readOnly: true
                      example: /objects/company-config/department-group/3086
        accountRanges:
          description: Array of account ranges for the account group.
          type: array
          items:
            $ref: '#/components/schemas/general-ledger-account-range'
        statisticalAccountRanges:
          description: Array of statistical account ranges for the account group.
          type: array
          items:
            $ref: '#/components/schemas/general-ledger-account-range'
        accountGroupMembers:
          description: Array of account group members for the account group.
          type: array
          items:
            $ref: '#/components/schemas/general-ledger-account-group-member'
        accountGroupCategoryMembers:
          description: Array of account group category members for the account group.
          type: array
          items:
            $ref: '#/components/schemas/general-ledger-account-group-category-member'
        statisticalAccountGroupCategoryMembers:
          description: >-
            Array of statistical account group category members for the account group.
          type: array
          items:
            $ref: '#/components/schemas/general-ledger-account-group-category-member'
        accountGroupComputation:
          description: Array of computation group members for the account group.
          type: array
          items:
            $ref: '#/components/schemas/general-ledger-account-group-computation'
        audit:
          $ref: '#/components/schemas/audit'
        entity:
          $ref: '#/components/schemas/entity-ref'
        href:
          type: string
          description: URL endpoint for the account group.
          readOnly: true
          example: /objects/general-ledger/account-group/33
    general-ledger-accountRequiredProperties:
      type: object
      required:
        - id
        - name
    required-dimensions-ref:
      type: object
      description: >-
        The dimensions that must be included on transactions that post to the account.
      properties:
        class:
          type: boolean
          description: Set to `true` to require a value for class.
          default: false
          example: false
        contract:
          type: boolean
          description: Set to `true` to require a value for contract.
          default: false
          example: false
        customer:
          type: boolean
          description: Set to `true` to require a value for customer.
          default: false
          example: false
        department:
          type: boolean
          description: Set to `true` to require a value for department.
          default: false
          example: true
        employee:
          type: boolean
          description: Set to `true` to require a value for employee.
          default: false
          example: false
        item:
          type: boolean
          description: Set to `true` to require a value for item.
          default: false
          example: false
        location:
          type: boolean
          description: Set to `true` to require a value for location.
          default: false
          example: false
        project:
          type: boolean
          description: Set to `true` to require a value for project.
          default: false
          example: false
        vendor:
          type: boolean
          description: Set to `true` to require a value for vendor.
          default: false
          example: false
        warehouse:
          type: boolean
          description: Set to `true` to require a value for warehouse.
          default: false
          example: false
        asset:
          type: boolean
          description: Set to `true` to require a value for asset.
          default: false
          example: false
        affiliateEntity:
          type: boolean
          description: Set to `true` to require a value for affiliate entity.
          default: false
          example: false
        task:
          type: boolean
          description: Set to `true` to require a value for task.
          default: false
          example: false
        costType:
          type: boolean
          description: Set to `true` to require a value for cost type.
          default: false
          example: false
    general-ledger-account:
      type: object
      description: General ledger account
      properties:
        key:
          type: string
          description: >-
            System-assigned key for the account. Used to identify the account in URLs or JSON bodies for all operations on the account.
          readOnly: true
          example: '411'
        id:
          type: string
          description: >-
            The primary account number. This number must be a specific length, which is set on the Accounting tab of the Company Information page.
          example: '1501.04'
        name:
          type: string
          description: >-
            Name or title of the account, which appears on report headings. Max length is 80.
          example: Expense Account
        accountType:
          type: string
          description: >-
            Type of account:

            - `balanceSheet` - A snapshot of the current state of a company's assets, liabilities, and equity at a specific time.

            - `incomeStatement` - Income statement accounts, sometimes called Profit and Loss statements, are cumulative for the selected period.
          enum:
            - balanceSheet
            - incomeStatement
          default: balanceSheet
          example: balanceSheet
        normalBalance:
          type: string
          description: >-
            Sets whether the normal balance, from an accounting standpoint, is a debit or credit. For example, expense accounts are normally a debit. Sales accounts are normally a credit.
          enum:
            - debit
            - credit
          default: debit
          example: debit
        closingType:
          type: string
          description: "Sets the account as a closing or non-closing account. Instead of manually closing accounts at year end, you can set them to be closing accounts and then specify the account in which to close that period. \n\n- `nonClosingAccount` - Balance sheet accounts.\n- `closingAccount` - Income statement accounts. Also specify a `closeToGLAccount`, which will typically be Retained Earnings. Sage Intacct then zeroes closing-type accounts into retained earnings at year-end.\n- `closedToAccount` - An account that other accounts close to, such as Retained Earnings. "
          enum:
            - nonClosingAccount
            - closingAccount
            - closedToAccount
          default: nonClosingAccount
          example: nonClosingAccount
        closeToGLAccount:
          type: object
          description: >-
            Account into which this account should close. Required if `closingType` is set to `closingAccount`.
          properties:
            key:
              type: string
              description: System-assigned key for account.
              example: '5'
            id:
              type: string
              description: GL account number.
              example: '3500'
            href:
              type: string
              description: URL endpoint of the account.
              readOnly: true
              example: /objects/general-ledger/account/5
        alternativeGLAccount:
          type: string
          description: >
            Sets whether the account can be used as an override of the default AP or AR GL accounts.


            - `payablesAccount` - The account can be used as an override account for bill and adjustment transactions, and transactions involving a specific vendor.

            - `receivablesAccount` - The account can be used as an override for invoice and adjustment transactions and transactions involving a specific customer.

            - `none` - The account cannot be used as an override account.


            The same alternative GL account can be used for all line items in a transaction, or different alternative accounts can be set for each line item. For vendors and customers, one alternative GL account can be used for balancing all transactions involving that vendor or customer.


            Note: Currently, this field applies to bills, invoices, and adjustments only. It doesn't apply to recurring bills or invoices, manual payments, manual deposits, or advances.

          enum:
            - none
            - payablesAccount
            - receivablesAccount
          default: none
          example: payablesAccount
        disallowDirectPosting:
          type: boolean
          description: >-
            Set to `true` to prevent direct entry of journal entries to the subledger control account (for example Accounts Payable, Accounts Receivable, etc.). Use this control to ensure that the account balance for the subledger account is accurate and has the necessary subledger details supporting the figure, rather than a direct entry which would not be reflected in the subledger application area.
          default: false
          example: false
        status:
          $ref: '#/components/schemas/status'
        requireDimensions:
          $ref: '#/components/schemas/required-dimensions-ref'
        category:
          type: string
          description: >-
            Account categories are pre-defined groupings that arrange accounts into out-of-the-box reports, graphs, and performance cards. This field is available only for companies that were created with a QuickStart template or chose one later. If enabled, set a category for the account. The available values are tied to the particular QuickStart template used for the company. The category chosen will automatically set values for `accountType`, `normalBalance`, and `closingType`.
          example: Cash and Cash Equivalents
        isTaxable:
          type: boolean
          description: Set to `true` to mark the account as taxable.
          default: false
          example: false
        taxCode:
          type: string
          description: >-
            Provide the tax return code needed by external tax compliance products to map tax codes to the tax forms that the company uses. Requires tax codes to be enabled in the General Ledger.
          example: CST
        mrcCode:
          type: string
          description: The M-3 return code box to map M-3 return codes to your M-3 form.
          example: m-3 1065
        entity:
          $ref: '#/components/schemas/entity-ref'
        audit:
          $ref: '#/components/schemas/audit'
        href:
          description: URL endpoint for the account.
          type: string
          readOnly: true
          example: /objects/general-ledger/account/411
    gl-account-ref:
      type: object
      properties:
        key:
          type: string
          description: Account key
          example: '144'
        id:
          type: string
          description: Account ID
          example: '1112'
        name:
          type: string
          description: Account title
          readOnly: true
          example: Employee Advances
        href:
          type: string
          description: URL endpoint of the general ledger account
          readOnly: true
          example: /objects/general-ledger/account/144
    currency:
      type: object
      properties:
        exchangeRateDate:
          type: string
          format: date
          example: '2014-01-08'
          description: Exchange rate date
        exchangeRateTypeId:
          type: string
          description: Exchange rate type
          example: Intacct Daily Rate
        exchangeRate:
          type: number
          description: Exchange rate
          example: 0.78
        baseCurrency:
          type: string
          description: Base currency
          example: USD
        txnCurrency:
          type: string
          description: Transaction currency
          example: GBP
    general-ledger-budget-detail:
      type: object
      description: Budget detail
      properties:
        key:
          type: string
          description: >-
            System-assigned key for the budget detail. Used to identify the budget detail in URLs or JSON bodies for all operations on the budget.
          readOnly: true
          example: '291'
        id:
          type: string
          description: Budget detail ID. Same as `key` for this object.
          readOnly: true
          example: '291'
        budget:
          description: The budget object that this budget detail is owned by.
          type: object
          readOnly: true
          properties:
            key:
              type: string
              description: Unique key of the budget object
              readOnly: true
              example: '5'
            id:
              type: string
              description: Name/ID of the budget object
              readOnly: true
              example: Employee Expense Budget
            href:
              type: string
              description: URL endpoint of the budget object.
              readOnly: true
              example: /objects/general-ledger/budget/5
        glAccount:
          type: object
          description: >-
            The General Ledger or Statistical account that this budget is for. The account in a budget-detail object cannot be changed after it has been created.
          allOf:
            - $ref: '#/components/schemas/gl-account-ref'
            - type: object
              properties:
                key:
                  type: string
                  description: Account key
                  example: '9'
                id:
                  type: string
                  description: Account ID
                  example: '4000'
                name:
                  type: string
                  description: Account name or title
                  readOnly: true
                  example: Revenue
                href:
                  type: string
                  description: URL endpoint of the account.
                  readOnly: true
                  example: /objects/general-ledger/account/9
        dimensions:
          description: "Dimensions to apply to this budget detail. Any dimension that is available in the General Ledger can be used with budgets, including user-defined dimensions (not listed here). \n\nTo track budget data at the dimension level, such as tracking data for different departments and locations, you will need to add multiple budget detail objects for each account. For example, if you are entering Travel Expense data for 3 locations, you will need 3 budget detail objects for this account, one for each location.\n\nThe dimensions in a budget-detail object cannot be changed after it has been saved."
          type: object
          allOf:
            - $ref: '#/components/schemas/dimension-ref'
            - type: object
              properties:
                location:
                  title: location
                  description: Location is required if multi-currency is enabled.
                  type: object
                  properties:
                    key:
                      type: string
                      description: Location key
                      example: '1'
                    id:
                      type: string
                      description: Location ID
                      example: NV
                    name:
                      type: string
                      description: Location name
                      readOnly: true
                      example: Nevada
                    href:
                      type: string
                      example: /objects/company-config/location/1
                department:
                  title: department
                  type: object
                  properties:
                    key:
                      type: string
                      description: Department Key
                      example: '3'
                    id:
                      type: string
                      description: Department
                      example: ENG
                    name:
                      type: string
                      description: Department Name
                      readOnly: true
                      example: Engineering
                    href:
                      type: string
                      example: /objects/company-config/department/3
        reportingPeriod:
          type: object
          description: >-
            The reporting period for the budget detail. A period cannot be used if it is already being used in another budget combination of reporting period, account, department, and location.
          properties:
            key:
              type: string
              description: Unique key for the time period.
              example: '395'
            id:
              type: string
              description: Unique identifier for the time period.
              example: Current Month
            startDate:
              description: Start date for budget.
              type: string
              format: date
              readOnly: true
              example: '2016-01-08'
            endDate:
              description: End date for budget.
              type: string
              format: date
              readOnly: true
              example: '2025-01-08'
            href:
              type: string
              description: URL endpoint for the time period.
              readOnly: true
              example: /objects/general-ledger/reporting-period/395
        notes:
          type: string
          description: Notes or description of the budget detail.
          example: Projection for 2021
        budgetGrowth:
          type: object
          description: >-
            To create a budget from an existing budget, provide the type of budget to use and the planned growth amount. The resulting amount will be computed when you create or update the budget-detail.
          properties:
            basedOn:
              description: >-
                Whether the new budget should be based on the budget amounts from the selected past period, the actual amounts, or by employee count.
              type: string
              example: budget
              enum:
                - null
                - budget
                - actual
                - employeeCount
              nullable: true
              default: null
            growBy:
              description: >-
                The amount of planned budget growth or reduction to apply to the past period budget or actual amount.
              type: string
              format: decimal-precision-2
              example: '10.00'
            perPeriod:
              description: >-
                Whether planned budget change is an actual value or a percentage.
              type: string
              enum:
                - null
                - actual
                - percentage
              nullable: true
              default: null
              example: percentage
        amount:
          description: Budget amount for the specified period.
          type: string
          format: decimal-precision-2
          example: '100.00'
        currency:
          description: >-
            For global consolidations, the base and transaction currencies to use.
          $ref: '#/components/schemas/currency'
        audit:
          $ref: '#/components/schemas/audit'
        entity:
          $ref: '#/components/schemas/entity-ref'
    general-ledger-budgetRequiredProperties:
      type: object
      required:
        - id
        - description
      properties:
        lines:
          type: array
          items:
            required:
              - glAccount
              - amount
    general-ledger-budget:
      type: object
      description: General ledger budget header
      properties:
        key:
          type: string
          description: >-
            System-assigned key for the budget. Used to identify the budget in URLs or JSON bodies for all operations on the budget.
          readOnly: true
          example: '5'
        id:
          type: string
          description: >-
            Budget name or ID. Best practice is to give the budget an ID that doesn't reference a particular fiscal year or date. This allows appending new time periods to an existing budget.
          example: Employee Expense Budget
        description:
          type: string
          description: Description of the budget.
          example: Budget for project costs
        isDefault:
          type: boolean
          description: >-
            Set to `true` to mark the budget as the company's default budget to use in financial reports. One top-level budget must be designated as the default. The system sets this value for all other budgets to `false`.
          default: false
          example: false
        submitterName:
          type: string
          description: The ID of the user who most recently modified the budget.
          readOnly: true
          example: Admin
        status:
          $ref: '#/components/schemas/status'
          example: active
        consolidateAmounts:
          type: boolean
          description: >-
            For companies using consolidation, set this field to `true` to mark this as a consolidated budget.
          default: false
          example: false
        currency:
          type: string
          description: >-
            Consolidation currency code. Required if `consolidateAmounts` is set to `true`.
          example: USD
        postProjectEstimate:
          type: boolean
          description: >-
            Set to `true` to enable project estimates to post to this budget. Requires Construction subscription.
          default: false
          example: false
        postProjectContract:
          type: boolean
          description: Set to `true` to enable project contract to post to this budget.
          default: false
          example: false
        audit:
          type: object
          allOf:
            - $ref: '#/components/schemas/audit'
            - type: object
              properties:
                createdBy:
                  type: string
                  description: ID of the user who created this object.
                  example: Admin
                  readOnly: true
                  nullable: true
                modifiedBy:
                  type: string
                  description: ID of the user who modified this object.
                  example: Admin
                  readOnly: true
                  nullable: true
        entity:
          $ref: '#/components/schemas/entity-ref'
        href:
          type: string
          description: URL endpoint for the budget.
          readOnly: true
          example: /objects/general-ledger/budget/1
        lines:
          description: Array of budget details for each reporting period.
          type: array
          items:
            $ref: '#/components/schemas/general-ledger-budget-detail'
    general-ledger-journal-entry-lineRequiredProperties:
      type: object
      properties:
        glAccount:
          required:
            - id
          example: '1000'
      required:
        - txnAmount
        - txnType
        - glAccount
    state:
      type: string
      enum:
        - draft
        - submitted
        - partiallyApproved
        - partiallyPaid
        - approved
        - paid
        - posted
        - declined
        - reversalPending
        - reversed
        - reversal
      default: posted
      description: State to update the entry to. Posted to post to the GL
      example: draft
    tax-entries:
      description: Tax entries for line items.
      type: object
      properties:
        key:
          type: string
          description: System-assigned key for the tax entry.
          example: '7149'
          readOnly: true
        id:
          type: string
          description: Unique identifier for the tax entry.
          example: '7149'
          readOnly: true
        baseTaxAmount:
          type: string
          description: Base tax amount.
          format: decimal-precision-2
          example: '100.00'
        txnTaxAmount:
          type: string
          description: >-
            Transaction tax amount. For a PATCH request, set to `null` if you want Sage Intacct to recalculate the amount, or set to the value you want if you don't want the system to recalculate.
          format: decimal-precision-2
          example: '100.00'
        taxRate:
          type: number
          description: Tax rate.
          example: 1.0299
    general-ledger-journal-entry-tax-entry:
      type: object
      description: journal entry tax entry, owned object of journal entry line.
      allOf:
        - $ref: '#/components/schemas/tax-entries'
        - type: object
          properties:
            taxDetail:
              type: object
              description: >-
                Tax details describe a specific type of tax that applies to journal entry lines.
              readOnly: true
              properties:
                key:
                  type: string
                  description: System-assigned key for the tax detail.
                  example: '1'
                id:
                  type: string
                  description: Unique identifier of the tax detail.
                  example: Alaska Tax Detail
                href:
                  type: string
                  description: URL of the tax detail object.
                  readOnly: true
                  example: /objects/tax/tax-detail/1
            journalEntryLine:
              title: journalEntryLine
              type: object
              readOnly: true
              properties:
                id:
                  type: string
                  description: Identifier for the journal entry line object.
                  example: '100'
                  readOnly: true
                key:
                  type: string
                  description: System-assigned key for the journal entry line object.
                  example: '100'
                  readOnly: true
                href:
                  type: string
                  description: URL for the journal entry line line object.
                  readOnly: true
                  example: /objects/general-ledger/journal-entry-line/100
    general-ledger-journal-entry-line:
      type: object
      description: >-
        Provides detailed and summary information for each line item within a journal entry.
      properties:
        key:
          type: string
          description: System assigned unique key for the journal entry line item.
          readOnly: true
          example: '1981'
        id:
          type: string
          description: Unique identifier for the journal entry line item.
          readOnly: true
          example: '1981'
        lineNumber:
          type: integer
          description: Line number of a line item within the journal entry.
          readOnly: true
          example: 1
        txnType:
          type: string
          description: >-
            Specifies whether the line item in the journal entry is a debit or credit.
          enum:
            - debit
            - credit
          default: debit
          example: credit
        txnAmount:
          type: string
          format: decimal-precision-2
          description: >-
            Amount of the line item as an absolute value within the journal entry.
          example: '100.45'
        entryDate:
          type: string
          format: date
          description: Date when the line item was added to the journal entry.
          readOnly: true
          example: '2024-01-23'
        documentId:
          type: string
          description: >-
            Reference for the journal entry line item, this is a user-entered value that can contain numeric or alpha-numeric values.
          example: CalOil_Credit_01-24
        description:
          type: string
          description: Description of the journal entry line.
          example: CalOil Credit entries January 2024
        numberOfUnits:
          type: integer
          description: >-
            Quantity of units associated with the journal entry line item, representing a non-monetary measurement, such as hours or other measurable units.
          example: 5
        reconciliationGroup:
          type: object
          description: Reconciliation group that links related journal entry line items.
          readOnly: true
          properties:
            clearingDate:
              type: string
              format: date
              description: >-
                Automatically generated date when the line item was reconciled or cleared as part of the reconciliation process.
              readOnly: true
              example: '2024-01-23'
            cleared:
              type: string
              enum:
                - 'true'
                - 'false'
                - matched
              description: >-
                Indicates whether the line item has been reconciled or cleared as part of the reconciliation process.
              readOnly: true
              example: 'false'
            reconciliationDate:
              type: string
              format: date
              description: >-
                Date when the line item was reconciled as part of the reconciliation process.
              readOnly: true
              example: '2024-01-23'
        accountingPeriod:
          type: integer
          description: >-
            Financial reporting period in which the line item is recorded. if company uses custom accounting periods.
          example: 11
        isBillable:
          type: boolean
          description: >-
            Indicates whether the line item is billable. Requires a Projects subscription and the billable option to be enabled for General Ledger transactions.
          default: false
          example: false
        isBilled:
          type: boolean
          description: Indicates whether the line item has already been billed.
          readOnly: true
          default: false
          example: false
        baseAmount:
          type: string
          format: decimal-precision-2
          description: Line item amount in the company's base currency.
          example: '100.45'
          readOnly: true
        interEntityTxnType:
          type: string
          description: >-
            Specifies whether the transaction is payable or receivable in inter-entity transactions (IET).
          readOnly: true
          example: r
        parent:
          type: object
          description: >-
            Represents the parent of the line item, used for nested items within the same journal entry.
          readOnly: true
          properties:
            key:
              type: string
              description: >-
                System-assigned unique key for the immediate parent of the line item.
              readOnly: true
              nullable: true
              example: '40'
            id:
              type: string
              description: Unique identifier for the immediate parent of the line item.
              readOnly: true
              nullable: true
              example: '40'
            href:
              type: string
              description: URL endpoint for the immediate parent of the line item.
              example: /objects/general-ledger/journal-entry-line/40
              readOnly: true
        state:
          $ref: '#/components/schemas/state'
        audit:
          $ref: '#/components/schemas/audit'
        currency:
          $ref: '#/components/schemas/currency'
        glAccount:
          $ref: '#/components/schemas/gl-account-ref'
        allocation:
          type: object
          description: >-
            Transaction allocation template, a predefined template used to allocate line items across accounts, departments, or entities.
          properties:
            key:
              type: string
              description: >-
                System-assigned unique key for the transaction allocation template.
              example: '21'
            id:
              type: string
              description: >-
                Unique identifier (user-defined) for the transaction allocation template.
              example: FA_ALLOC
            href:
              type: string
              readOnly: true
              description: URL endpoint for the transaction allocation template.
              example: /objects/general-ledger/txn-allocation-template/21
        dimensions:
          $ref: '#/components/schemas/dimension-ref'
        href:
          type: string
          description: URL endpoint for journal entry line item.
          readOnly: true
          example: /objects/general-ledger/journal-entry-line/1981
        journalEntry:
          type: object
          description: Journal entry to which the line item belongs.
          properties:
            id:
              type: string
              description: >-
                Unique identifier for the journal entry containing the line item.
              example: '132'
            key:
              type: string
              description: >-
                System-assigned unique key for the journal entry containing the line item.
              example: '132'
            href:
              type: string
              description: >-
                URL endpoint for the associated journal entry containing the line item.
              readOnly: true
              example: /objects/general-ledger/journal-entry/132
        taxEntries:
          type: array
          description: Tax-related details for the line item.
          items:
            $ref: '#/components/schemas/general-ledger-journal-entry-tax-entry'
    general-ledger-journal-entryRequiredProperties:
      type: object
      required:
        - glJournal
        - postingDate
        - description
        - lines
      properties:
        lines:
          type: array
          items:
            required:
              - txnAmount
              - txnType
              - glAccount
    general-ledger-journal-entry:
      type: object
      description: >-
        Journal entries are used to create, update, or reverse accounting entries within a journal before they are posted to the general ledger for financial reporting and analysis.
      properties:
        key:
          type: string
          description: System-assigned unique key for the journal entry.
          example: '132'
          readOnly: true
        id:
          type: string
          description: Unique identifier for the journal entry.
          example: '132'
          readOnly: true
        txnNumber:
          type: integer
          description: System-assigned transaction number for the journal entry.
          example: 40
          readOnly: true
        glJournal:
          type: object
          description: Specifies the journal associated with this journal entry.
          properties:
            key:
              type: string
              description: >-
                System-assigned unique key for the journal associated with this journal entry.
              example: '3'
            id:
              type: string
              description: >-
                Unique identifier for the journal associated with this journal entry.
              example: EJ
            isAdjustment:
              type: boolean
              description: Indicates if the journal is intended for recording adjustments.
              readOnly: true
              example: false
              default: false
            href:
              type: string
              description: URL endpoint for the journal entry.
              example: /objects/general-ledger/journal/3
              readOnly: true
        description:
          type: string
          description: Description of the journal entry.
          example: Revenue entries
        postingDate:
          type: string
          format: date
          description: >-
            The date when the journal entry is recorded in the journal and posted to the general ledger for tracking and reporting.
          example: '2023-04-01'
        automaticReversalDate:
          type: string
          format: date
          description: >-
            The date a journal entry is automatically reversed in the general ledger. `automaticReversalDate` must be later than the `postingDate`.
          example: '2023-04-04'
          nullable: true
        reversedFromDate:
          type: string
          format: date
          description: >-
            The original posting date of the journal entry that is being reversed. Only populated if the journal entry has been reversed.
          example: '2023-04-05'
          readOnly: true
          nullable: true
        reversedBy:
          type: object
          description: >-
            Identifies the journal entry that reversed the original entry. Only populated if the journal entry has been reversed.
          readOnly: true
          properties:
            key:
              type: string
              description: System-assigned unique key of the reversing entry.
              readOnly: true
              example: '663'
              nullable: true
            id:
              type: string
              description: Unique identifier of the reversing entry.
              readOnly: true
              example: '663'
            href:
              type: string
              description: URL endpoint for the reversing entry.
              readOnly: true
              example: /objects/general-ledger/journal-entry/663
        moduleName:
          type: string
          description: The Sage Intacct module from which the journal entry originates.
          example: 2.GL
        referenceNumber:
          type: string
          description: >-
            Unique identifier assigned to the journal entry by the user for tracking and reconciliation.
          example: Recurr_GL_101
        attachment:
          type: object
          description: Supporting documents or files linked to the journal entry.
          properties:
            key:
              type: string
              description: >-
                System-assigned unique key for the attachment linked to the journal entry.
              example: '6'
            id:
              type: string
              description: >-
                Unique identifier for the attachment linked to the journal entry.
              example: Doc6331
            href:
              type: string
              description: URL endpoint for the attachment linked to the journal entry.
              readOnly: true
              example: /objects/company-config/attachment/6
        entity:
          $ref: '#/components/schemas/entity-ref'
        baseLocation:
          type: object
          description: >-
            Specifies the base location for a multi-entity company. Required if multi-entity is enabled and entries do not balance by entity.
          properties:
            key:
              description: System-assigned unique key for the base location.
              type: string
              example: '32'
            id:
              description: Unique identifier for the base location.
              type: string
              example: BC
            href:
              description: URL endpoint for the base location.
              type: string
              example: /objects/company-config/location/32
              readOnly: true
        state:
          type: string
          description: "Shows the current state of the journal entry. \n\nNote: The only valid values when creating a new entry are `posted` (default, system-defined state), and `draft`. Only populate the state field when creating a `draft` entry.\n"
          enum:
            - draft
            - submitted
            - partiallyApproved
            - approved
            - posted
            - declined
            - reversalPending
            - reversed
          default: posted
          example: draft
        sequenceNumber:
          type: string
          description: >-
            System-assigned accounting sequence number that uniquely identifies each journal entry within the accounting sequence. For multi-entity companies that use accounting sequences.
          example: GLJE_001
          readOnly: true
        tax:
          type: object
          description: >-
            Tax-related details applied to the journal entry, such as tax codes, rates and exemptions. This field applies only to companies using the Taxes application to track VAT or GST.
          properties:
            taxImplication:
              type: string
              description: >-
                Specifies the impact of a journal entry on tax calculations. This field applies only to companies using the Taxes application to track VAT or GST.
              example: inbound
              nullable: true
              enum:
                - null
                - none
                - inbound
                - outbound
              default: null
            taxSolution:
              type: object
              description: >-
                Reference to the tax solution. Required only if the company is set up for multiple tax jurisdictions and the journal entry occurs at the top level (GB, AU, and ZA only). See [Tax solutions](/openapi/tax/tax.tax-solution/tag/Tax-solutions) for more information.
              title: tax-solution
              readOnly: true
              properties:
                key:
                  type: string
                  description: System-assigned unique key for the tax solution.
                  example: '5'
                id:
                  type: string
                  description: Unique identifier for the tax solution.
                  example: Australia - GST
                href:
                  type: string
                  description: URL endpoint for the tax solution.
                  readOnly: true
                  example: /objects/tax/tax-solution/5
            vendor:
              type: object
              title: vendor
              description: >-
                Specifies the vendor for tax-related information when `taxImplication` is set to `inbound`.
              properties:
                key:
                  type: string
                  description: System-assigned unique key for the vendor.
                  example: '15'
                id:
                  type: string
                  description: Unique identifier for the vendor.
                  example: V-00014
                href:
                  type: string
                  description: URL endpoint for the vendor.
                  readOnly: true
                  example: /objects/accounts-payable/vendor/15
            customer:
              type: object
              title: customer
              description: >-
                Specifies the customer for tax-related information when `taxImplication` is set to `outbound`.
              properties:
                key:
                  type: string
                  description: System-assigned unique key for the customer.
                  example: '14'
                id:
                  type: string
                  description: Unique identifier for the customer.
                  example: C-00019
                href:
                  type: string
                  description: URL endpoint for the customer.
                  readOnly: true
                  example: /objects/accounts-receivable/customer/14
            contact:
              type: object
              description: >-
                Specifies the associated contact details for the journal entry such as a vendor, customer, or other relevant entity.
              title: contact
              properties:
                key:
                  type: string
                  description: System-assigned unique key for the contact.
                  example: '416'
                id:
                  type: string
                  description: Unique idetifier for the contact.
                  example: Klay Vanderbilt
                href:
                  type: string
                  description: URL endpoint for the contact.
                  readOnly: true
                  example: /objects/company-config/contact/416
        audit:
          $ref: '#/components/schemas/audit'
        href:
          type: string
          description: URL endpoint for the journal entry.
          readOnly: true
          example: /objects/general-ledger/journal-entry/132
        accountAllocationRun:
          type: object
          description: Account allocation run used by this journal entry.
          readOnly: true
          properties:
            key:
              type: string
              description: System-assigned unique key for the account allocation run.
              readOnly: true
              nullable: true
              example: '87'
            id:
              type: string
              description: Unique identifier for the account allocation run.
              readOnly: true
              nullable: true
              example: '87'
            href:
              type: string
              description: URL endpoint for the account allocation run.
              example: /objects/general-ledger/account-allocation-run/87
              readOnly: true
        lines:
          description: >-
            A collection of line items within the journal entry. At least two line items are required per journal entry, one debit and one credit.
          type: array
          minItems: 2
          items:
            $ref: '#/components/schemas/general-ledger-journal-entry-line'
    general-ledger-journal-entry-actions-submit-request:
      type: object
      properties:
        key:
          type: string
          description: System-assigned unique key for the journal entry to be submitted.
          example: '132'
      required:
        - key
    general-ledger-journal-entry-actions-submit-response:
      type: object
      description: Reference to the submitted journal entry.
      properties:
        key:
          type: string
          description: System-assigned unique key for the journal entry.
          example: '132'
        id:
          type: string
          description: Unique identifier of the journal entry, same as the `key`.
          example: '132'
        href:
          type: string
          description: URL endpoint that links to the journal entry.
          example: /objects/general-ledger/journal-entry/132
        state:
          type: string
          description: State of the submitted journal entry.
          enum:
            - submitted
          example: submitted
    general-ledger-journal-entry-actions-approve-request:
      type: object
      properties:
        key:
          type: string
          description: System-assigned unique key for the journal entry to be approved.
          example: '132'
        notes:
          type: string
          description: Comments about the journal entry approval.
          example: Approved, ready for use.
      required:
        - key
    general-ledger-journal-entry-actions-approve-response:
      type: object
      description: Reference to the approved journal entry.
      properties:
        key:
          type: string
          description: System-assigned unique key for the journal entry.
          example: '132'
        id:
          type: string
          description: Unique identifier of the journal entry, same as the `key`.
          example: '132'
        href:
          type: string
          description: URL endpoint that links to the journal entry.
          example: /objects/general-ledger/journal-entry/132
        state:
          type: string
          description: State of the approved journal entry.
          readOnly: true
          enum:
            - approved
          example: approved
    general-ledger-journal-entry-actions-decline-request:
      type: object
      properties:
        key:
          type: string
          description: System-assigned unique key for the journal entry to be declined.
          example: '132'
        notes:
          type: string
          description: Comments about the journal entry decline.
          example: Declined, missing information.
      required:
        - key
    general-ledger-journal-entry-actions-decline-response:
      type: object
      description: Reference to the declined journal entry.
      properties:
        key:
          type: string
          description: System-assigned unique key for the journal entry.
          example: '132'
        id:
          type: string
          description: Unique identifier of the journal entry, same as the `key`.
          example: '132'
        href:
          type: string
          description: URL endpoint that links to the journal entry.
          example: /objects/general-ledger/journal-entry/132
        state:
          type: string
          description: State of the declined journal entry.
          enum:
            - declined
          example: declined
    general-ledger-journal-entry-actions-recall-request:
      type: object
      properties:
        key:
          type: string
          description: System-assigned unique key for the journal entry to be recalled.
          example: '132'
      required:
        - key
    general-ledger-journal-entry-actions-recall-response:
      type: object
      description: Reference to the recalled journal entry.
      properties:
        key:
          type: string
          description: System-assigned unique key for the journal entry.
          example: '132'
        id:
          type: string
          description: Unique identifier of the journal entry, same as the `key`.
          example: '132'
        href:
          type: string
          description: URL endpoint that links to the journal entry.
          example: /objects/general-ledger/journal-entry/132
        state:
          type: string
          description: State of the recalled journal entry.
          enum:
            - draft
          example: draft
    general-ledger-journal-entry-actions-reverse-request:
      type: object
      properties:
        key:
          type: string
          description: System-assigned unique key for the journal entry to be reversed.
          example: '132'
        reverseDate:
          type: string
          format: date
          description: >-
            Date on which the journal entry should be reversed, must be later than `postingDate`.
          example: '2023-04-04'
      required:
        - key
        - reverseDate
    general-ledger-journal-entry-actions-reverse-response:
      type: object
      description: Reference to the reversed journal entry.
      properties:
        key:
          type: string
          description: System-assigned unique key for the journal entry.
          example: '132'
        id:
          type: string
          description: Unique identifier of the journal entry, same as the `key`.
          example: '132'
        href:
          type: string
          description: URL endpoint that links to the journal entry.
          example: /objects/general-ledger/journal-entry/132
        state:
          type: string
          description: State of the reversed journal entry.
          enum:
            - reversed
          example: reversed
    general-ledger-journal-entry-actions-reclassify-request:
      type: object
      properties:
        key:
          type: string
          description: System-assigned unique key for the journal entry to be reclassified.
          example: '132'
        id:
          type: string
          description: Unique identifier of the journal entry, same as the `key`.
          example: '132'
        description:
          type: string
          description: Description of the journal entry.
          example: Revenue entries
        referenceNumber:
          type: string
          description: Reference for the journal entry.
          example: Recurr_GL_101
        attachment:
          type: object
          description: Supporting documents or files linked to the journal entry.
          properties:
            key:
              type: string
              description: System-assigned unique key for the attachment.
              example: '6'
            id:
              type: string
              description: Unique identifier of the attachment.
              example: Doc6331
            href:
              type: string
              description: URL endpoint that links to the attachment.
              readOnly: true
              example: /objects/company-config/attachment/6
        state:
          type: string
          description: State of the journal entry being reclassified.
          enum:
            - posted
          default: posted
          example: posted
        href:
          type: string
          description: URL endpoint that links to the journal entry.
          readOnly: true
          example: /objects/general-ledger/journal-entry/132
        lines:
          description: >-
            Journal entry lines included in the journal entry. At least two lines are required, one debit and one credit.
          type: array
          items:
            $ref: '#/components/schemas/general-ledger-journal-entry-line'
      required:
        - key
    general-ledger-journal-entry-actions-reclassify-response:
      type: object
      description: Reference to the reclassified journal entry.
      properties:
        key:
          type: string
          description: System-assigned unique key for the journal entry.
          example: '132'
        id:
          type: string
          description: Unique identifier of the journal entry, same as the `key`.
          example: '132'
        href:
          type: string
          description: URL endpoint that links to the journal entry.
          example: /objects/general-ledger/journal-entry/132
        state:
          type: string
          description: State of the reclassified journal entry.
          enum:
            - posted
          example: posted
    general-ledger-journalRequiredProperties:
      type: object
      required:
        - id
        - name
    general-ledger-journal:
      type: object
      description: General ledger journal
      properties:
        key:
          type: string
          description: >-
            System-assigned key for the journal. Used to identify the journal in URLs or JSON bodies for all operations on the journal.
          readOnly: true
          example: '3'
        id:
          type: string
          description: >-
            An abbreviation (symbol) for this journal, for example, AP. In most places in Sage Intacct, the symbol appears along with the `name` of the journal, as in "AP--Accounts Payable."
          example: EJ
        name:
          type: string
          description: The full name of the journal, as it should appear in reports.
          example: Expense Journal
        isBillable:
          type: boolean
          description: >-
            Set to `true` to enable journal entry line items for a project to be marked as billable. (Requires Projects subscription and billable General Ledger transactions to be enabled.)
          default: false
          example: true
        status:
          $ref: '#/components/schemas/status'
        isAdjustment:
          type: boolean
          description: To Check if the journal is an adjustments journal.
          default: false
          example: false
        bookId:
          type: string
          description: >-
            The reporting book for which the journal was created. For most companies, the reporting book is `Accrual`. If the company is set up for dual-method reporting, the choices are Accrual, Cash, or Accrual and cash.
          readOnly: true
          example: Accrual
        bookType:
          type: string
          description: >-
            A book type could be Accrual, Cash, Cash and Accrual, or Consolidation.  For most companies, the reporting book is accrual. If the company is set up for dual-method reporting, the choices are `accrual`, `cash`, or `cashAndAccrual`.
          enum:
            - accrual
            - cash
            - cashAndAccrual
            - consolidation
          default: accrual
          example: accrual
        disallowDirectPosting:
          type: boolean
          description: >-
            Set to `true` to prevent direct entry of journal entries for this journal .
          default: false
          example: false
        audit:
          $ref: '#/components/schemas/audit'
        enableApproval:
          type: boolean
          description: >-
            Set to `true` to enable [journal entry approvals](https://www.intacct.com/ia/docs/en_US/help_action/General_Ledger/Approvals/journal-entry-approval-overview.htm).
          readOnly: true
          default: false
          example: false
        href:
          type: string
          description: URL endpoint for this journal.
          example: /objects/general-ledger/journal/3
          readOnly: true
    general-ledger-reporting-periodRequiredProperties:
      type: object
      required:
        - id
        - columnHeader1
    general-ledger-reporting-period:
      type: object
      description: Reporting period definition
      properties:
        key:
          type: string
          description: System-assigned unique key for the reporting period.
          readOnly: true
          example: '421'
        id:
          type: string
          description: >-
            Name of the reporting period. The name cannot be changed after the reporting period is created.
          example: Current Month
        columnHeader1:
          type: string
          description: The first line to be shown on the report, such as "Quarter Ended".
          example: Quarter Ended
        columnHeader2:
          type: string
          description: The second line to be shown on the report, such as "April 2023".
          nullable: true
          example: April 2023
        startDate:
          type: string
          format: date
          description: The starting date of the reporting period.
          nullable: true
          example: '2023-04-01'
        endDate:
          type: string
          format: date
          description: The ending date of the reporting period.
          nullable: true
          example: '2023-06-30'
        isBudgetable:
          type: boolean
          description: >-
            Set to `true` to make this reporting period available for inclusion in budgets.
          example: true
          default: false
        reportingPeriodType:
          type: string
          description: >-
            Reporting period type rule for GL listing will be custom. System reporting period will be standard.
          readOnly: true
          enum:
            - standard
            - custom
          example: custom
        dateType:
          type: integer
          description: The date type is the rule.
          readOnly: true
          example: 99
        href:
          type: string
          description: URL endpoint for the reporting period.
          readOnly: true
          example: /objects/general-ledger/reporting-period/1
        status:
          $ref: '#/components/schemas/status'
        audit:
          $ref: '#/components/schemas/audit'
    general-ledger-statistical-accountRequiredProperties:
      type: object
      required:
        - id
        - name
    general-ledger-statistical-account:
      type: object
      description: Statistical account
      properties:
        key:
          type: string
          description: >-
            System-assigned key for the statistical  account. Used to identify the account in URLs or JSON bodies for all operations on the account.
          readOnly: true
          example: '397'
        id:
          type: string
          description: >-
            The primary account number. This number must be a specific length, which is set on the Accounting tab of the Company Information page.
          example: '9001'
        name:
          type: string
          description: Name or title of this statistical account.
          example: Customer Account
        reportType:
          type: string
          description: >-
            This is a non-functional field, for information purposes only. It does not affect how amounts are calculated for reporting.
          enum:
            - forPeriod
            - cumulative
          default: forPeriod
          example: forPeriod
        requireDimensions:
          $ref: '#/components/schemas/required-dimensions-ref'
        isTaxable:
          type: boolean
          description: Set to `true` to mark the account as taxable.
          default: false
          example: true
        category:
          type: string
          description: >-
            Account categories are pre-defined groupings that arrange accounts into out-of-the-box reports, graphs, and performance cards. This field is available only for companies that were created with a QuickStart template or chose one later. If enabled, set a category for the account. The available values are set by the particular QuickStart template used for the company.
          example: Customers
        status:
          $ref: '#/components/schemas/status'
          example: inactive
        audit:
          $ref: '#/components/schemas/audit'
        href:
          type: string
          description: URL endpoint for the statistical account.
          readOnly: true
          example: /objects/general-ledger/statistical-account/397
        entity:
          $ref: '#/components/schemas/entity-ref'
    gl-statistical-account-ref:
      type: object
      properties:
        key:
          type: string
          description: Statistical GL account key
          example: '144'
        id:
          type: string
          description: Statistical GL account ID
          example: '1112'
        name:
          type: string
          description: Statistical GL account title
          readOnly: true
          example: Root - Billable Non-Utilized Statistical Account
        href:
          type: string
          description: URL endpoint for the statistical GL account
          readOnly: true
          example: /objects/general-ledger/statistical-account/144
    general-ledger-statistical-journal-entry-line:
      type: object
      description: >-
        Detail and header information for the statistical journal line items within the statistical journal entry.
      properties:
        key:
          type: string
          description: System assigned unique key for the statistical journal entry line.
          readOnly: true
          example: '1981'
        id:
          type: string
          description: Unique identifier for the statistical journal entry line.
          readOnly: true
          example: '1981'
        lineNumber:
          type: integer
          description: >-
            Specifies the specific line number within the statistical journal entry.
          readOnly: true
          example: 1
        txnType:
          type: string
          description: >-
            Indicates the type of line item recorded in the statistical journal entry.
          enum:
            - increase
            - decrease
          default: increase
          example: increase
        txnAmount:
          type: string
          description: >-
            Represents the amount of the line item as an absolute value within the statistical journal entry.
          format: decimal-precision-2
          example: '100.45'
        entryDate:
          type: string
          format: date
          description: >-
            Specifies the date when the line item was added to the statistical journal entry.
          readOnly: true
          example: '2021-01-23'
        documentId:
          type: string
          description: >-
            Reference for the statistical journal entry line item, this is a user-entered value that can contain numeric or alpha-numeric values.
          example: Headcount_Increase-03-24
        description:
          type: string
          description: Description of the statistical journal entry line.
          example: Headcount Increase March 2024
        numberOfUnits:
          type: integer
          description: >-
            Indicates the quantity associated with the line item, representing a non-monetary measurement, for example, headcount or production units.
          example: 5
          readOnly: true
        reconciliationGroup:
          type: object
          readOnly: true
          properties:
            clearingDate:
              type: string
              format: date
              description: >-
                Represents an automatically generated date when the line item was reconciled or cleared as part of the reconciliation process.
              readOnly: true
              example: '2021-01-23'
            cleared:
              type: string
              enum:
                - 'true'
                - 'false'
                - matched
              description: >-
                Indicates whether the line item has been reconciled or cleared as part of the reconciliation process.
              readOnly: true
              example: 'false'
            reconciliationDate:
              type: string
              format: date
              description: >-
                The date when the line item was reconciled as part of the reconciliation process.
              readOnly: true
              example: '2021-01-23'
        accountingPeriod:
          type: integer
          description: Accounting period if company uses custom accounting periods.
          nullable: true
          example: 11
          readOnly: true
        state:
          $ref: '#/components/schemas/state'
        audit:
          $ref: '#/components/schemas/audit'
        statisticalAccount:
          $ref: '#/components/schemas/gl-statistical-account-ref'
        allocation:
          type: object
          description: >-
            Transaction allocation template, a predefined template used to allocate statistical line items across accounts, departments, or entities.
          properties:
            key:
              type: string
              description: >-
                System-assigned unique key for the transaction allocation template.
              example: '21'
            id:
              type: string
              description: >-
                Unique identifier (user-defined) for the transaction allocation template.
              example: FA_ALLOC
            href:
              type: string
              readOnly: true
              description: URL endpoint for the transaction allocation template.
              example: /objects/general-ledger/txn-allocation-template/21
        dimensions:
          $ref: '#/components/schemas/dimension-ref'
        href:
          type: string
          description: URL endpoint for the statistical journal entry line.
          readOnly: true
          example: /objects/general-ledger/statistical-journal-entry-line/1981
        statisticalJournalEntry:
          type: object
          description: Statistical journal entry to which this line item belongs.
          readOnly: true
          properties:
            id:
              type: string
              description: Unique identifier for the statistical journal entry.
              readOnly: true
              example: '132'
            key:
              type: string
              description: System-assigned unique key for the statistical journal entry.
              readOnly: true
              example: '132'
            href:
              type: string
              description: URL endpoint for the statistical journal entry.
              readOnly: true
              example: /objects/general-ledger/statistical-journal-entry/132
    general-ledger-statistical-journal-entryRequiredProperties:
      type: object
      required:
        - statisticalJournal
        - postingDate
        - description
        - lines
      properties:
        lines:
          type: array
          items:
            required:
              - txnAmount
              - txnType
              - statisticalAccount
    general-ledger-statistical-journal-entry:
      type: object
      description: >
        Statistical journal entries allow you to add, edit, or reverse statistical transactions within a journal before posting them to the general ledger for reporting and analysis.

      properties:
        key:
          type: string
          description: System-assigned key for the statistical journal entry.
          readOnly: true
          example: '23'
        id:
          type: string
          description: Unique identifier for the statistical journal entry.
          readOnly: true
          example: '23'
        txnNumber:
          type: integer
          description: >-
            System-assigned transaction number for the statistical journal entry.
          example: 40
          readOnly: true
        description:
          type: string
          description: Description of the statistical journal entry.
          example: Revenue entries
        postingDate:
          type: string
          format: date
          description: >-
            The date when the statistical journal entry is recorded in the journal and posted to the general ledger for tracking and reporting.
          example: '2023-04-01'
        moduleName:
          type: string
          description: >-
            The Sage Intacct module from which the statistical journal entry originates.
          default: 2.GL
          example: 2.GL
          readOnly: true
        automaticReversalDate:
          type: string
          format: date
          description: >-
            The date a statistical journal entry is automatically reversed in the general ledger. `automaticReversalDate` must be later than the `postingDate`.
          example: '2023-04-04'
          nullable: true
        reversedFromDate:
          type: string
          format: date
          description: >-
            The original posting date of the statistical journal entry that is being reversed. Only populated if the journal entry has been reversed.
          example: '2023-04-05'
          readOnly: true
          nullable: true
        reversedBy:
          type: object
          description: >-
            Identifies the statistical journal entry that reversed the original entry. Only populated if the journal entry has been reversed.
          readOnly: true
          properties:
            key:
              type: string
              description: System-assigned key of the reversing entry.
              readOnly: true
              example: '663'
              nullable: true
            id:
              type: string
              description: Unique identifier of the reversing entry.
              readOnly: true
              example: '663'
              nullable: true
            href:
              type: string
              description: URL endpoint for the reversing entry.
              readOnly: true
              example: /objects/general-ledger/statistical-journal-entry/663
        state:
          type: string
          description: >
            Shows the current state of the statistical journal entry. The only valid values when creating a new entry are `posted` (default, system-defined state), and `draft`. Only populate the state field when creating a `draft` entry. If you leave the state field blank, it will have the default state of `posted`.

          enum:
            - draft
            - submitted
            - partiallyApproved
            - approved
            - posted
            - declined
            - reversalPending
            - reversed
          default: posted
          readOnly: true
          example: draft
        sequenceNumber:
          type: string
          description: >-
            System-assigned number that uniquely identifies the order of statistical transactions within a journal entry.
          readOnly: true
          example: STATGLJE_001
        statisticalJournal:
          type: object
          description: >-
            Identifies the statistical journal associated to which the statistical journal entry belongs.
          properties:
            key:
              type: string
              description: System-assigned key for the statistical journal.
              example: '3'
            id:
              type: string
              description: Unique identifier for the statistical journal.
              example: EJ
            href:
              type: string
              description: URL endpoint for the statistical journal.
              example: /objects/general-ledger/statistical-journal/3
              readOnly: true
        referenceNumber:
          type: string
          description: >-
            Unique identifier assigned to the statistical journal entry by the user for tracking and reconciliation.
          example: Recurr_GL_101
        attachment:
          type: object
          description: >-
            Supporting documents or files linked to the statistical journal entry.
          properties:
            key:
              type: string
              description: System-assigned key for the attachment.
              example: '6'
            id:
              type: string
              description: Unique identifier for the attachment.
              example: Doc6331
            href:
              type: string
              description: URL endpoint for the attachment.
              readOnly: true
              example: /objects/company-config/attachment/6
        entity:
          $ref: '#/components/schemas/entity-ref'
        audit:
          $ref: '#/components/schemas/audit'
        href:
          type: string
          description: URL endpoint for the statistical journal entry.
          readOnly: true
          example: /objects/general-ledger/statistical-journal-entry/23
        lines:
          description: >-
            A collection of statistical transaction lines within the journal entry. At least one line in required per statistical journal entry.
          type: array
          minItems: 1
          items:
            $ref: '#/components/schemas/general-ledger-statistical-journal-entry-line'
    general-ledger-statistical-journalRequiredProperties:
      type: object
      required:
        - id
        - name
    general-ledger-statistical-journal:
      type: object
      description: Statistical journals hold all non-financial transactions.
      properties:
        key:
          type: string
          description: >-
            System-assigned key for the statistical journal. Used to identify the journal in URLs or JSON bodies for all operations on the journal.
          readOnly: true
          example: '36'
        id:
          type: string
          description: >-
            An abbreviation (symbol) for this journal, for example, APJ. In most places in Sage Intacct the symbol appears along with the `name` of the journal, as in "TS--Timesheets."
          example: TSSJ
        name:
          type: string
          description: >-
            The full name or title of the journal, as it should appear in reports.
          example: Timesheet Statistical Journal
        bookId:
          type: string
          description: >-
            The reporting book for which the journal was created. For most companies, the reporting book is accrual. If the company is set up for dual-method reporting, the choices are accrual, cash, or accrual and cash.
          readOnly: true
          example: Accrual
        bookType:
          type: string
          description: >-
            A book type could be Accrual, Cash, Cash and Accrual or Consolidation.  For most companies, the reporting book is accrual. If the company is set up for dual-method reporting, the choices are accrual, cash, or accrual and cash.
          readOnly: true
          enum:
            - accrual
            - cash
            - cashAndAccrual
            - consolidation
          default: accrual
          example: accrual
        disallowDirectPosting:
          type: boolean
          description: >-
            Set to `true` to prevent direct entry of journal entries for this journal.
          default: false
          example: false
        status:
          $ref: '#/components/schemas/status'
          example: active
        audit:
          $ref: '#/components/schemas/audit'
        href:
          type: string
          description: URL endpoint for this journal.
          readOnly: true
          example: /objects/general-ledger/statistical-journal/36
    general-ledger-txn-allocation-template-line:
      type: object
      description: Line level component of a transaction allocation template.
      properties:
        key:
          type: string
          description: System-assigned unique key for the allocation line.
          readOnly: true
          example: '1'
        id:
          type: string
          description: This value is the same as the `key` value for this object.
          readOnly: true
          example: '1'
        value:
          type: string
          description: The exact amount or percentage to allocate for this line.
          example: '60.56'
        valueType:
          type: string
          description: >-
            Specifies whether the current line is an exact amount or percentage. Must be set when creating `fixedAmount` allocations, but is set automatically for `percentage` and `exactAmount` allocations.
          example: amount
          enum:
            - amount
            - percent
          default: amount
        lineNumber:
          type: integer
          description: >-
            Line number of the allocation entry. For `fixedAmount` allocations, exact amounts are distributed by line number.
          example: 1
        dimensions:
          type: object
          description: Dimensions to use in allocations.
          allOf:
            - $ref: '#/components/schemas/dimension-ref'
            - type: object
              properties:
                location:
                  title: location
                  type: object
                  properties:
                    key:
                      type: string
                      description: Location key
                      example: '1'
                    id:
                      type: string
                      description: Location ID
                      example: US
                    name:
                      type: string
                      description: Location name
                      readOnly: true
                      example: United States of America
                    href:
                      type: string
                      description: URL endpoint for the location
                      example: /objects/company-config/location/1
                department:
                  title: department
                  type: object
                  properties:
                    key:
                      type: string
                      description: Department key
                      example: '3'
                    id:
                      type: string
                      description: Department ID
                      example: ENG
                    name:
                      type: string
                      description: Department name
                      readOnly: true
                      example: Engineering
                    href:
                      type: string
                      description: URL endpoint for the department
                      example: /objects/company-config/department/3
        audit:
          $ref: '#/components/schemas/audit'
        href:
          type: string
          description: URL endpoint for the allocation template line.
          readOnly: true
          example: /objects/general-ledger/txn-allocation-template-line/1
        txnAllocationTemplate:
          type: object
          description: >-
            Transaction allocation template that this allocation line belongs to.
          properties:
            key:
              type: string
              description: Allocation template key
              readOnly: true
              example: '1'
            id:
              type: string
              description: Allocation template ID
              readOnly: true
              example: RootAllocation
            href:
              type: string
              description: URL endpoint for the allocation template
              readOnly: true
              example: /objects/general-ledger/txn-allocation-template/1
    general-ledger-txn-allocation-templateRequiredProperties:
      type: object
      required:
        - id
        - lines
      properties:
        lines:
          type: array
          items:
            required:
              - value
              - dimensions
    general-ledger-txn-allocation-template:
      type: object
      description: >-
        Header object for standard allocation combinations that are used routinely.
      properties:
        key:
          type: string
          description: System-assigned unique key for the transaction allocation template.
          example: '21'
          readOnly: true
        id:
          type: string
          description: User defined transaction allocation template ID.
          example: FA_ALLOC
        description:
          type: string
          description: A description of the allocation.
          example: Fixed Amount Allocation
        allocateBy:
          type: string
          description: >
            Determines how transaction allocations are split across the dimensions included in a transaction.


            * Use `percentage` to specify entries as percentages (default)

            * Use `exactAmount` to specify entries as fixed amounts

            * Use `fixedAmount` for a combination of both. Exact amounts are distributed first, based on the `lineNumber` values in each txn-allocation-template-line. Any remaining amount is distributed using the percentage allocations. Entries for exact amounts must have lower line numbers than entries for percentages, and percentages must always total 100%. (You use the `valueType` parameter on each entry to specify amount or percent.)

          example: percentage
          enum:
            - percentage
            - exactAmount
            - fixedAmount
          default: percentage
        documentNumber:
          type: string
          description: >-
            A reference number or name to a specific document--such as a contract--used to define the transaction allocation template formula.
          example: RJEALC
        status:
          $ref: '#/components/schemas/status'
        attachment:
          type: object
          description: Supporting document ID for this attachment.
          properties:
            key:
              type: string
              example: '21'
            id:
              type: string
              example: Sales01
            href:
              type: string
              readOnly: true
              example: /objects/attachment/21
        entity:
          $ref: '#/components/schemas/entity-ref'
        audit:
          $ref: '#/components/schemas/audit'
        href:
          type: string
          description: URL endpoint of the allocation template.
          example: /objects/general-ledger/txn-allocation-template/21
          readOnly: true
        lines:
          type: array
          description: Transaction allocation lines.
          minItems: 1
          items:
            $ref: '#/components/schemas/general-ledger-txn-allocation-template-line'
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/error-response'
  securitySchemes:
    OAuth2:
      description: Sage Intacct OAuth 2.0 authorization code flow
      type: oauth2
      flows:
        authorizationCode:
          authorizationUrl: https://api.intacct.com/ia/api/v1/oauth2/authorize
          tokenUrl: https://api.intacct.com/ia/api/v1/oauth2/token
          refreshUrl: https://api.intacct.com/ia/api/v1/oauth2/token
          scopes: {}
