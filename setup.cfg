[flake8]
max-line-length = 88
extend-ignore = E203, W503, E501
exclude = 
    .git,
    __pycache__,
    build,
    dist,
    .venv,
    venv,
    .tox,
    .eggs,
    *.egg,
    openapi
per-file-ignores =
    __init__.py:F401
    tests/*:D100,D101,D102,D103

[bandit]
exclude = /tests,/venv,/.venv

[tool:pytest]
testpaths = tests
python_files = test_*.py *_test.py
python_functions = test_*
addopts = -v --tb=short
