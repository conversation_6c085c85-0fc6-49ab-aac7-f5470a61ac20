# Sage Intacct OAuth 2.0 Authentication Configuration Template
# Copy this file to auth_config.yaml and fill in your credentials

# OAuth 2.0 Configuration
oauth:
  # Client credentials from Sage Intacct
  client_id: "YOUR_CLIENT_ID_HERE"
  client_secret: "YOUR_CLIENT_SECRET_HERE"
  
  # OAuth URLs
  authorization_url: "https://api.intacct.com/ia/oauth2/authorize"
  token_url: "https://api.intacct.com/ia/oauth2/token"
  
  # Redirect URI - must match exactly what's configured in Intacct
  redirect_uri: "http://localhost:8080/callback"
  
  # OAuth scope - typically "full" for complete API access
  scope: "full"
  
  # PKCE (Proof Key for Code Exchange) - recommended for security
  use_pkce: true
  
  # State parameter for CSRF protection
  use_state: true

# Authentication Flow Settings
auth_flow:
  # Supported flows: "authorization_code", "client_credentials"
  preferred_flow: "authorization_code"
  
  # For client credentials flow (server-to-server)
  # Requires Web Services User credentials
  web_services_user:
    sender_id: ""
    sender_password: ""
    
  # Token refresh settings
  auto_refresh: true
  refresh_buffer_seconds: 300  # Refresh token 5 minutes before expiry

# Token Storage Configuration
token_storage:
  # Storage backend: "file", "memory", "redis" (future)
  backend: "file"
  
  # File storage settings
  file:
    directory: "./tokens"
    encryption_enabled: true
    # Encryption key from env: INTACCT_TOKEN_ENCRYPTION_KEY  
  # Token cache settings
  cache_tokens: true
  cache_ttl_seconds: 3600

# Multi-Entity Support
multi_entity:
  enabled: false
  # Entity selection strategy: "prompt", "first", "specific"
  selection_strategy: "first"
  # For "specific" strategy, specify the entity ID
  default_entity_id: ""
  
  # Store tokens per entity
  per_entity_tokens: true

# Session Management
session:
  # Session timeout in seconds (0 = no timeout)
  timeout_seconds: 3600
  
  # Keep-alive ping interval
  keepalive_interval_seconds: 300
  
  # Maximum concurrent sessions
  max_concurrent_sessions: 5

# Security Settings
security:
  # Validate SSL certificates
  verify_ssl: true
  
  # Additional headers for all requests
  custom_headers:
    User-Agent: "Sage-Intacct-MCP-Server/1.0"
  
  # IP allowlist (empty = allow all)
  allowed_ips: []
  
  # Rate limiting per client
  rate_limit:
    enabled: true
    requests_per_minute: 60
    burst_size: 10

# Logging Configuration
logging:
  # Log authentication events
  log_auth_events: true
  
  # Log token refresh events  log_token_refresh: true
  
  # Mask sensitive data in logs
  mask_sensitive_data: true
  
  # Fields to mask in logs
  masked_fields:
    - client_secret
    - access_token
    - refresh_token
    - sender_password