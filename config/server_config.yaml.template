# Sage Intacct MCP Server Configuration Template
# Copy this file to server_config.yaml and customize as needed

# Server Basic Configuration
server:
  name: "Sage Intacct MCP Server"
  version: "1.0.0"
  description: "MCP server for Sage Intacct API integration"
  
  # Server host and port
  host: "0.0.0.0"
  port: 8000
  
  # Transport protocol: "stdio", "streamable-http", "sse"
  transport: "streamable-http"
  
  # Enable debug mode
  debug: false
  
  # Request timeout in seconds
  request_timeout: 30
  
  # Maximum request size in bytes
  max_request_size: ********  # 10MB

# Module Configuration
modules:
  # Accounts Payable Module
  accounts_payable:
    enabled: true
    # Load priority (lower numbers load first)
    priority: 1
    # Custom tools to enable
    custom_tools:
      - create_bill_with_approval_flow
      - bulk_payment_processing
      - vendor_statement_generator
      - payment_batch_creator
    # Priority endpoints to load immediately
    priority_endpoints:
      - /objects/accounts-payable/bill
      - /objects/accounts-payable/payment
      - /objects/accounts-payable/vendor
    # Resource templates to enable
    resource_templates:
      - "intacct://ap/vendor/{vendorId}"
      - "intacct://ap/bills/{status}"
      - "intacct://ap/payments/{date_range}"
  # Accounts Receivable Module
  accounts_receivable:
    enabled: true
    priority: 2
    custom_tools:
      - invoice_generation_workflow
      - payment_application
      - credit_memo_processor
      - collections_management
    priority_endpoints:
      - /objects/accounts-receivable/invoice
      - /objects/accounts-receivable/payment
      - /objects/accounts-receivable/customer
    resource_templates:
      - "intacct://ar/customer/{customerId}"
      - "intacct://ar/invoices/{status}"
      - "intacct://ar/aging/{period}"
  
  # General Ledger Module
  general_ledger:
    enabled: true
    priority: 3
    custom_tools:
      - journal_entry_creator
      - account_reconciliation
      - financial_report_generator
      - budget_vs_actual_analyzer
    priority_endpoints:
      - /objects/general-ledger/account
      - /objects/general-ledger/journal
      - /objects/general-ledger/transaction
    resource_templates:
      - "intacct://gl/accounts/{accountNumber}"
      - "intacct://gl/balance/{accountId}/{period}"
      - "intacct://gl/reports/{reportType}"

# Performance Configuration
performance:
  # Enable lazy loading of modules
  lazy_loading: true
  
  # Cache configuration
  cache:
    enabled: true
    # Cache TTL in seconds
    ttl: 300
    # Maximum cache size in MB
    max_size_mb: 100    # Cache strategy: "lru", "fifo"
    strategy: "lru"
  
  # Connection pooling
  connection_pool:
    # Maximum connections per host
    max_connections: 100
    # Maximum keepalive connections
    max_keepalive_connections: 20
    # Keepalive timeout in seconds
    keepalive_timeout: 30
  
  # Request batching
  batching:
    enabled: true
    # Maximum batch size
    max_batch_size: 50
    # Batch timeout in milliseconds
    batch_timeout_ms: 100
  
  # Rate limiting
  rate_limiting:
    # Requests per second
    requests_per_second: 10
    # Burst size
    burst_size: 20

# API Configuration
api:
  # Base URL for Intacct API
  base_url: "https://api.intacct.com/ia/xml/xmlgw.phtml"
  
  # API version
  version: "3.0"
  
  # Request retry configuration
  retry:
    enabled: true
    max_attempts: 3
    # Backoff strategy: "exponential", "linear", "fixed"
    backoff_strategy: "exponential"
    initial_delay_ms: 1000
    max_delay_ms: 30000
  
  # Response handling
  response:
    # Maximum response size in MB
    max_size_mb: 50
    # Parse responses incrementally
    streaming_parse: true
# Feature Flags
features:
  # Enable cross-module search
  cross_module_search: true
  
  # Enable batch operations
  batch_operations: true
  
  # Enable workflow automation
  workflow_automation: false
  
  # Enable custom report builder
  custom_reports: true
  
  # Enable audit logging
  audit_logging: true
  
  # Enable metrics collection
  metrics: true

# Logging Configuration
logging:
  # Log level: "DEBUG", "INFO", "WARNING", "ERROR"
  level: "INFO"
  
  # Log format: "json", "text"
  format: "json"
  
  # Log destinations
  handlers:
    console:
      enabled: true
      level: "INFO"
    file:
      enabled: true
      level: "DEBUG"
      path: "./logs/intacct-mcp.log"
      # Maximum file size in MB
      max_size_mb: 100
      # Number of backup files to keep
      backup_count: 5
    
  # Structured logging fields
  extra_fields:
    service: "intacct-mcp"
    environment: "${ENVIRONMENT:development}"

# Monitoring Configuration
monitoring:
  # Health check endpoint
  health_check:
    enabled: true
    path: "/health"
    include_details: true  
  # Metrics endpoint
  metrics:
    enabled: true
    path: "/metrics"
    # Prometheus format
    format: "prometheus"
  
  # Distributed tracing
  tracing:
    enabled: false
    # Tracing backend: "jaeger", "zipkin"
    backend: "jaeger"
    endpoint: "http://localhost:14268/api/traces"
    sample_rate: 0.1

# Error Handling
error_handling:
  # Include stack traces in errors
  include_stack_trace: false
  
  # User-friendly error messages
  friendly_messages: true
  
  # Error code mapping
  map_intacct_errors: true
  
  # Fallback behavior
  fallback:
    # Enable fallback for unavailable modules
    enabled: true
    # Return cached data on error
    use_cache_on_error: true