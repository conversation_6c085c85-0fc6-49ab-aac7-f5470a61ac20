# Sage Intacct MCP Server Configuration
# This is an example configuration file. Copy to server_config.yaml and customize.

# Server settings
server_name: sage-intacct-mcp
server_version: 1.0.0
log_level: INFO

# Authentication settings
authentication:
  client_id: ${INTACCT_CLIENT_ID}  # Can use environment variables
  client_secret: ${INTACCT_CLIENT_SECRET}
  redirect_uri: http://localhost:8080/callback
  scope: full
  authorize_url: https://secure.intacct.com/ia/webapp/authz/oauth2/v1/authorize
  token_url: https://secure.intacct.com/ia/webapp/authz/oauth2/v1/token

# Module configuration
modules:
  ap:
    enabled: true
    settings:
      # AP-specific settings
      default_vendor_type: standard
      auto_approve_threshold: 1000
  
  ar:
    enabled: true
    settings:
      # AR-specific settings
      default_payment_terms: net30
      auto_send_invoices: false
  
  gl:
    enabled: true
    settings:
      # GL-specific settings
      require_approval: true
      auto_post_entries: false

# Performance settings
performance:
  cache_enabled: true
  cache_ttl: 300  # seconds
  max_concurrent_requests: 10
  request_timeout: 30  # seconds

# Feature flags
features:
  strict_module_loading: false  # Fail if any module fails to load
  auto_retry: true  # Automatically retry failed requests
  batch_operations: false  # Enable batch processing tools
