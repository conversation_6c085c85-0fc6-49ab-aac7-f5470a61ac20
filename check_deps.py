#!/usr/bin/env python3
"""
Quick dependency check script.
Run this after installing dependencies to verify everything is set up correctly.
"""

import sys


def check_import(module_name: str, package_name: str = None) -> bool:
    """Check if a module can be imported."""
    if package_name is None:
        package_name = module_name
    
    try:
        __import__(module_name)
        print(f"✅ {package_name} - OK")
        return True
    except ImportError as e:
        print(f"❌ {package_name} - MISSING ({e})")
        return False


def main():
    """Check all required dependencies."""
    print("Checking Sage Intacct MCP Server dependencies...\n")
    
    core_deps = [
        ("fastmcp", "FastMCP"),
        ("httpx", "httpx"),
        ("authlib", "Authlib"),
        ("yaml", "PyYAML"),
        ("dotenv", "python-dotenv"),
        ("pydantic", "Pydantic"),
        ("aiofiles", "aiofiles"),
        ("dateutil", "python-dateutil"),
        ("yarl", "yarl"),
        ("orjson", "orjson"),
        ("structlog", "structlog"),
        ("aiolimiter", "aiolimiter"),
        ("tenacity", "tenacity"),
    ]
    
    print("Core Dependencies:")
    print("-" * 40)
    missing = []
    for module, name in core_deps:
        if not check_import(module, name):
            missing.append(name)
    
    if missing:
        print(f"\n❌ Missing dependencies: {', '.join(missing)}")
        print("Please run: pip install -r requirements.txt")
        sys.exit(1)
    else:
        print("\n✅ All core dependencies are installed!")
        sys.exit(0)


if __name__ == "__main__":
    main()
